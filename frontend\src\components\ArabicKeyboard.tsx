import React from 'react';

type ArabicKeyboardProps = {
  onKeyPress: (char: string) => void;
};

const keys = [
  'ض','ص','ث','ق','ف','غ','ع','ه','خ','ح','ج','د',
  'ش','س','ي','ب','ل','ا','ت','ن','م','ك','ط','ذ',
  'ء','ؤ','ر','ى','ة','و','ز','ظ','إ','أ','ئ','لا','←'
];

export const ArabicKeyboard: React.FC<ArabicKeyboardProps> = ({ onKeyPress }) => {
  return (
    <div className="flex flex-wrap bg-white border border-gray-300 shadow-lg p-2 w-[360px] mt-2 rounded">
      {keys.map((key, index) => (
        <button
          key={index}
          onClick={() => onKeyPress(key)}
          className="w-10 h-10 m-1 border rounded text-lg hover:bg-gray-100"
        >
          {key}
        </button>
      ))}
    </div>
  );
};
export default ArabicKeyboard;