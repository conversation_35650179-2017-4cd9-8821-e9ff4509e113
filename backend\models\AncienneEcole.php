<?php

class AncienneEcole
{
    private $pdo;
    private $table = "Ancienne_Ecole";

    public function __construct($db)
    {
        $this->pdo = $db;
    }

    public function create($data)
    {
        try {
            // Vérifier si l'école existe déjà
            if ($this->findByCodeGresa($data['code_gresa'])) {
                return $data['code_gresa'];
            }

            $query = "INSERT INTO " . $this->table . " (code_gresa, nom, type, cycle, adresse)
                VALUES (:code_gresa, :nom, :type, :cycle, :adresse)";
            $stmt = $this->pdo->prepare($query);

            $stmt->bindParam(':code_gresa', $data['code_gresa'], PDO::PARAM_STR);
            $stmt->bindParam(':nom', $data['nom'], PDO::PARAM_STR);
            $stmt->bindParam(':type', $data['type'], PDO::PARAM_STR);
            $stmt->bindParam(':cycle', $data['cycle'], PDO::PARAM_STR);
            $stmt->bindParam(':adresse', $data['adresse'], PDO::PARAM_STR);

            if ($stmt->execute()) {
                return $data['code_gresa'];
            } else {
                error_log("Erreur SQL AncienneEcole : " . implode(", ", $stmt->errorInfo()));
                return false;
            }
        } catch (PDOException $e) {
            error_log("Erreur PDO dans AncienneEcole::create : " . $e->getMessage());
            error_log("Données reçues : " . json_encode($data));
            return false;
        }
    }

    public function findByCodeGresa($code_gresa)
    {
        try {
            $query = "SELECT * FROM " . $this->table . " WHERE code_gresa = :code_gresa";
            $stmt = $this->pdo->prepare($query);
            $stmt->bindParam(':code_gresa', $code_gresa, PDO::PARAM_STR);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return $result ? $result : false;
        } catch (PDOException $e) {
            error_log("Erreur dans findByCodeGresa: " . $e->getMessage());
            return false;
        }
    }

    public function update($code_gresa, $data)
    {
        try {
            $query = "UPDATE " . $this->table . " SET 
                     nom = :nom,
                     type = :type,
                     cycle = :cycle,
                     adresse = :adresse
                     WHERE code_gresa = :code_gresa";
            $stmt = $this->pdo->prepare($query);

            $stmt->bindParam(':nom', $data['nom'], PDO::PARAM_STR);
            $stmt->bindParam(':type', $data['type'], PDO::PARAM_STR);
            $stmt->bindParam(':cycle', $data['cycle'], PDO::PARAM_STR);
            $stmt->bindParam(':adresse', $data['adresse'], PDO::PARAM_STR);
            $stmt->bindParam(':code_gresa', $code_gresa, PDO::PARAM_STR);

            if ($stmt->execute()) {
                return $stmt->rowCount() > 0;
            } else {
                error_log("Erreur SQL Update AncienneEcole : " . implode(", ", $stmt->errorInfo()));
                return false;
            }
        } catch (PDOException $e) {
            error_log("Erreur PDO dans AncienneEcole::update : " . $e->getMessage());
            return false;
        }
    }

    public function getAll()
    {
        try {
            $stmt = $this->pdo->prepare("SELECT * FROM " . $this->table . " ORDER BY nom");
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur dans getAll: " . $e->getMessage());
            return false;
        }
    }

    public function delete($code_gresa)
    {
        try {
            $query = "DELETE FROM " . $this->table . " WHERE code_gresa = :code_gresa";
            $stmt = $this->pdo->prepare($query);
            $stmt->bindParam(':code_gresa', $code_gresa, PDO::PARAM_STR);
            
            if ($stmt->execute()) {
                return $stmt->rowCount() > 0;
            } else {
                error_log("Erreur SQL Delete AncienneEcole : " . implode(", ", $stmt->errorInfo()));
                return false;
            }
        } catch (PDOException $e) {
            error_log("Erreur PDO dans AncienneEcole::delete : " . $e->getMessage());
            return false;
        }
    }
}
