<?php

require_once __DIR__ . '/../models/Unite.php';

class UniteController {
    private $uniteModel;

    public function __construct($pdo) {
        $this->uniteModel = new Unite($pdo);
    }

    public function getAll() {
        try {
            $unites = $this->uniteModel->getAll();

            echo json_encode([
                'success' => true,
                'data' => $unites,
                'message' => 'Unités récupérées avec succès'
            ]);

        } catch (Exception $e) {
            error_log("Erreur dans getAll: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la récupération des unités: ' . $e->getMessage()
            ]);
        }
    }

    public function getById($id) {
        try {
            if (!is_numeric($id) || $id <= 0) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'ID unité invalide'
                ]);
                return;
            }

            $unite = $this->uniteModel->getById($id);

            if ($unite) {
                echo json_encode([
                    'success' => true,
                    'data' => $unite,
                    'message' => 'Unité récupérée avec succès'
                ]);
            } else {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'message' => 'Unité non trouvée'
                ]);
            }

        } catch (Exception $e) {
            error_log("Erreur dans getById: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la récupération de l\'unité: ' . $e->getMessage()
            ]);
        }
    }

    public function create() {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Données JSON invalides'
                ]);
                return;
            }

            // Valider les champs requis
            if (empty($input['id_matiere']) || empty($input['nom_unite'])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Les champs id_matiere et nom_unite sont requis'
                ]);
                return;
            }

            $result = $this->uniteModel->create($input);

            if ($result) {
                http_response_code(201);
                echo json_encode([
                    'success' => true,
                    'data' => ['id_unite' => $result],
                    'message' => 'Unité créée avec succès'
                ]);
            } else {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la création de l\'unité'
                ]);
            }

        } catch (Exception $e) {
            error_log("Erreur dans create: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la création de l\'unité: ' . $e->getMessage()
            ]);
        }
    }

    public function update($id) {
        try {
            if (!is_numeric($id) || $id <= 0) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'ID unité invalide'
                ]);
                return;
            }

            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Données JSON invalides'
                ]);
                return;
            }

            $result = $this->uniteModel->update($id, $input);

            if ($result) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Unité mise à jour avec succès'
                ]);
            } else {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la mise à jour de l\'unité'
                ]);
            }

        } catch (Exception $e) {
            error_log("Erreur dans update: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la mise à jour de l\'unité: ' . $e->getMessage()
            ]);
        }
    }

    public function delete($id) {
        try {
            if (!is_numeric($id) || $id <= 0) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'ID unité invalide'
                ]);
                return;
            }

            $result = $this->uniteModel->delete($id);

            if ($result) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Unité supprimée avec succès'
                ]);
            } else {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la suppression de l\'unité'
                ]);
            }

        } catch (Exception $e) {
            error_log("Erreur dans delete: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la suppression de l\'unité: ' . $e->getMessage()
            ]);
        }
    }
}

?>
