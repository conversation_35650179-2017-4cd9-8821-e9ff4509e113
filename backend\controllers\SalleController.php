<?php

require_once __DIR__ . '/../models/Salle.php';

class SalleController
{
    private $salle;

    public function __construct($db)
    {
        $this->salle = new Salle($db);
    }

    public function getAll()
    {
        echo json_encode(['success' => true, 'data' => $this->salle->getAll()]);
    }

    public function getById($id)
    {
        $data = $this->salle->getById($id);
        if ($data) {
            echo json_encode(['success' => true, 'data' => $data]);
        } else {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Salle non trouvée']);
        }
    }

    public function create()
    {
        $input = json_decode(file_get_contents("php://input"), true);
        if (!$input || !isset($input['nom_salle'], $input['capacite'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Données invalides']);
            return;
        }
        $data = $this->salle->create($input);
        echo json_encode(['success' => true, 'data' => $data]);
    }

    public function update($id)
    {
        $input = json_decode(file_get_contents("php://input"), true);
        if (!$input || !isset($input['nom_salle'], $input['capacite'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Données invalides']);
            return;
        }
        $data = $this->salle->update($id, $input);
        echo json_encode(['success' => true, 'data' => $data]);
    }

    public function delete($id)
    {
        $ok = $this->salle->delete($id);
        echo json_encode(['success' => $ok]);
    }
}