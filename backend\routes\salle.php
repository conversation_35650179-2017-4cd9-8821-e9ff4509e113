<?php

require_once __DIR__ . '/../controllers/SalleController.php';

function handleSalleRoutes($uri, $method, $pdo) {
    $controller = new SalleController($pdo);

    if ($uri === '/salles' && $method === 'GET') {
        $controller->getAll();
        return true;
    }
    if (preg_match('#^/salles/(\d+)$#', $uri, $matches)) {
        if ($method === 'GET') {
            $controller->getById($matches[1]);
            return true;
        }
        if ($method === 'PUT') {
            $controller->update($matches[1]);
            return true;
        }
        if ($method === 'DELETE') {
            $controller->delete($matches[1]);
            return true;
        }
    }
    if ($uri === '/salles' && $method === 'POST') {
        $controller->create();
        return true;
    }
    return false;
}