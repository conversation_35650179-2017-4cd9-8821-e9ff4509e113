import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Calendar, CheckCircle, XCircle, X } from 'lucide-react';
import Button from '../Button';
import Input from '../Input';
import { AnneeScolaire } from '../../types';
import {
          getAnneesScolaires,
          addAnneeScolaire,
          updateAnneeScolaire,
          deleteAnneeScolaire,
          toggleAnneeScolaireActive
        } from '../../services/api';
import { extractErrorMessage, extractSuccessMessage } from '../../utils/errorUtils';
import { useDeleteConfirmation  } from "../../hooks/useConfirmation";


const AnneeScolaireTab: React.FC = () => {
  const [anneesScolaires, setAnneesScolaires] = useState<AnneeScolaire[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [editingAnnee, setEditingAnnee] = useState<AnneeScolaire | null>(null);
  const [formData, setFormData] = useState<Partial<AnneeScolaire>>({
    libelle: '',
    date_debut: '',
    date_fin: '',
    est_active: false
  });
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [validationErrors, setValidationErrors] = useState<{ [key: string]: string }>({});
  const { confirmDelete, ConfirmationComponent } = useDeleteConfirmation();
 
  

  useEffect(() => {
    loadAnneesScolaires();
  }, []);

  // Effet pour faire disparaître le message après 3 secondes
  useEffect(() => {
    if (message) {
      const timer = setTimeout(() => {
        setMessage(null);
      }, 3000); // 3 secondes

      return () => clearTimeout(timer);
    }
  }, [message]);



  const loadAnneesScolaires = async () => {
    setIsLoading(true);
    try {
      const response = await getAnneesScolaires();
      if (response.data.success) {
        setAnneesScolaires(response.data.data);
      } else {
        const errorMessage = response.data.message || 'Erreur lors du chargement des années scolaires';
        setMessage({ type: 'error', text: errorMessage });
      }
    } catch (error: any) {
      console.error('Erreur lors du chargement des années scolaires:', error);
      const errorMessage = extractErrorMessage(error, 'Erreur lors du chargement des années scolaires');
      setMessage({ type: 'error', text: errorMessage });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));

    // Effacer l'erreur de validation pour ce champ
    if (validationErrors[name]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Fonction pour valider les champs
  const validateForm = (): boolean => {
    const errors: { [key: string]: string } = {};

    if (!formData.libelle?.trim()) {
      errors.libelle = 'Le libellé est requis';
    }

    if (!formData.date_debut) {
      errors.date_debut = 'La date de début est requise';
    }

    if (!formData.date_fin) {
      errors.date_fin = 'La date de fin est requise';
    }

    if (formData.date_debut && formData.date_fin && formData.date_debut >= formData.date_fin) {
      errors.date_fin = 'La date de fin doit être postérieure à la date de début';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Fonction pour extraire les erreurs de validation du backend
  const extractValidationErrors = (error: any): void => {
    const errors: { [key: string]: string } = {};

    if (error.response?.data?.errors) {
      // Si le backend renvoie des erreurs de validation structurées
      Object.keys(error.response.data.errors).forEach(field => {
        errors[field] = error.response.data.errors[field][0]; // Premier message d'erreur
      });
    } else if (error.response?.data?.message) {
      // Analyser le message d'erreur pour extraire les champs
      const message = error.response.data.message.toLowerCase();

      if (message.includes('libellé')) {
        errors.libelle = error.response.data.message;
      } else if (message.includes('date de début')) {
        errors.date_debut = error.response.data.message;
      } else if (message.includes('date de fin')) {
        errors.date_fin = error.response.data.message;
      } else if (message.includes('existe déjà')) {
        errors.libelle = error.response.data.message;
      }
    }

    setValidationErrors(errors);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Effacer les erreurs de validation précédentes
    setValidationErrors({});

    // Valider le formulaire côté client
    if (!validateForm()) {
      return;
    }

    try {
      if (editingAnnee) {
        // Mise à jour
        const response = await updateAnneeScolaire(editingAnnee.id_annee_scolaire, formData);
        if (response.data.success) {
          const successMessage = extractSuccessMessage(response, 'Année scolaire mise à jour avec succès');
          setMessage({ type: 'success', text: successMessage });
          loadAnneesScolaires(); // Recharger la liste
          resetForm(); // Fermer la modal seulement en cas de succès
        } else {
          const errorMessage = response.data.message || 'Erreur lors de la mise à jour';
          setMessage({ type: 'error', text: errorMessage });
        }
      } else {
        // Création
        const response = await addAnneeScolaire(formData);
        if (response.data.success) {
          const successMessage = extractSuccessMessage(response, 'Année scolaire créée avec succès');
          setMessage({ type: 'success', text: successMessage });
          loadAnneesScolaires(); // Recharger la liste
          resetForm(); // Fermer la modal seulement en cas de succès
        } else {
          const errorMessage = response.data.message || 'Erreur lors de la création';
          setMessage({ type: 'error', text: errorMessage });
        }
      }
    } catch (error: any) {
      console.error('Erreur lors de la sauvegarde:', error);

      // Extraire les erreurs de validation du backend
      extractValidationErrors(error);

      // Afficher aussi le message d'erreur général si pas d'erreurs de validation spécifiques
      if (Object.keys(validationErrors).length === 0) {
        const errorMessage = extractErrorMessage(error, 'Erreur lors de la sauvegarde');
        setMessage({ type: 'error', text: errorMessage });
      }
    }
  };

  const handleEdit = (annee: AnneeScolaire) => {
    setEditingAnnee(annee);
    setFormData({
      libelle: annee.libelle,
      date_debut: annee.date_debut,
      date_fin: annee.date_fin,
      est_active: annee.est_active
    });
    setShowForm(true);
  };

  const handleDeleteClick = (annee: AnneeScolaire) => {
    const name = annee.libelle;
    confirmDelete(
      () => handleDelete(annee.id_annee_scolaire),
      name,
      "Êtes-vous sûr de vouloir supprimer cette année scolaire ?"
    );
  };
  const handleDelete = async (id: number) => {
      try {
        const response = await deleteAnneeScolaire(id);
        if (response.data.success) {
          const successMessage = extractSuccessMessage(response, 'Année scolaire supprimée avec succès');
          setMessage({ type: 'success', text: successMessage });
          loadAnneesScolaires(); // Recharger la liste
        } else {
          const errorMessage = response.data.message || 'Erreur lors de la suppression';
          setMessage({ type: 'error', text: errorMessage });
        }
      } catch (error: any) {
        console.error('Erreur lors de la suppression:', error);
        const errorMessage = extractErrorMessage(error, 'Erreur lors de la suppression');
        setMessage({ type: 'error', text: errorMessage });
      }
  };

  const handleToggleActive = async (id: number) => {
    try {
      const response = await toggleAnneeScolaireActive(id);
      if (response.data.success) {
        setMessage({ type: 'success', text: 'Statut mis à jour avec succès' });
        loadAnneesScolaires(); // Recharger la liste pour refléter les changements
      } else {
        setMessage({ type: 'error', text: response.data.message || 'Erreur lors du changement de statut' });
      }
      // Effacer le message après 3 secondes
      setTimeout(() => setMessage(null), 3000);
    } catch (error) {
      console.error('Erreur lors du changement de statut:', error);
      setMessage({ type: 'error', text: 'Erreur lors du changement de statut' });
    }
  };

  const resetForm = () => {
    setFormData({
      libelle: '',
      date_debut: '',
      date_fin: '',
      est_active: false
    });
    setEditingAnnee(null);
    setShowForm(false);
    setValidationErrors({}); // Effacer les erreurs de validation
  };

  const getStatusIcon = (annee: AnneeScolaire) => {
    if (annee.est_active) {
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    } else {
      return <XCircle className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusText = (annee: AnneeScolaire) => {
    if (annee.est_active) {
      return { text: 'Activée', color: 'text-green-600 bg-green-100' };
    } else {
      return { text: 'Désactivée', color: 'text-gray-600 bg-gray-100' };
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Chargement...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Message de statut - seulement quand la modal n'est pas ouverte */}
      {message && !showForm && (
        <div className={`p-4 rounded-lg relative ${
          message.type === 'success' ? 'bg-green-50 text-green-800 border border-green-200' : 'bg-red-50 text-red-800 border border-red-200'
        }`}>
          <div className="flex items-center justify-between">
            <span>{message.text}</span>
            <button
              onClick={() => setMessage(null)}
              className="ml-4 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Gestion des années scolaires</h3>
          <p className="text-sm text-gray-500">Configurez les périodes scolaires de votre établissement</p>
        </div>
        <Button
          onClick={() => setShowForm(true)}
          className="flex items-center space-x-2"
        >
          <Plus className="h-4 w-4" />
          <span>Ajouter une année</span>
        </Button>
      </div>

      {/* Modal Formulaire */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-lg w-full mx-4">
            {/* En-tête de la modal */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">
                {editingAnnee ? 'Modifier l\'année scolaire' : 'Ajouter une nouvelle année scolaire'}
              </h3>
              <button
                onClick={resetForm}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Message d'erreur dans la modal */}
            {message && (
              <div className={`mx-6 mt-4 p-4 rounded-lg relative ${
                message.type === 'success' ? 'bg-green-50 text-green-800 border border-green-200' : 'bg-red-50 text-red-800 border border-red-200'
              }`}>
                <div className="flex items-center justify-between">
                  <span>{message.text}</span>
                  <button
                    onClick={() => setMessage(null)}
                    className="ml-4 text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>
            )}

            {/* Corps de la modal */}
            <form onSubmit={handleSubmit} className="p-6 space-y-4">
              <Input
                name="libelle"
                label="Libellé"
                value={formData.libelle || ''}
                onChange={handleInputChange}
                required
                placeholder="Ex: 2024-2025"
                error={validationErrors.libelle}
              />

              <div className="grid grid-cols-2 gap-4">
                <Input
                  name="date_debut"
                  label="Date de début"
                  type="date"
                  value={formData.date_debut || ''}
                  onChange={handleInputChange}
                  required
                  error={validationErrors.date_debut}
                />

                <Input
                  name="date_fin"
                  label="Date de fin"
                  type="date"
                  value={formData.date_fin || ''}
                  onChange={handleInputChange}
                  required
                  error={validationErrors.date_fin}
                />
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="est_active"
                  name="est_active"
                  checked={formData.est_active || false}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="est_active" className="text-sm font-medium text-gray-700">
                  Année active
                </label>
              </div>

              {/* Pied de la modal */}
              <div className="flex justify-end space-x-3 pt-4">
                <Button
                  type="button"
                  variant="secondary"
                  onClick={resetForm}
                >
                  Annuler
                </Button>
                <Button type="submit" variant="primary">
                  {editingAnnee ? 'Modifier' : 'Ajouter'}
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Liste des années scolaires */}
      <div className="bg-white border rounded-lg overflow-hidden">
        <div className="px-6 py-3 bg-gray-50 border-b">
          <h4 className="text-lg font-medium text-gray-900">Années scolaires</h4>
        </div>
        
        <div className="divide-y divide-gray-200">
          {anneesScolaires.map((annee) => {
            const status = getStatusText(annee);
            return (
              <div key={annee.id_annee_scolaire} className="px-6 py-4 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(annee)}
                      <h5 className="text-lg font-medium text-gray-900">{annee.libelle}</h5>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${status.color}`}>
                        {status.text}
                      </span>
                    </div>
                    <div className="mt-2 flex items-center space-x-6 text-sm text-gray-500">
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-4 w-4" />
                        <span>Du {new Date(annee.date_debut).toLocaleDateString('fr-FR')} au {new Date(annee.date_fin).toLocaleDateString('fr-FR')}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button
                      size="sm"
                      variant={annee.est_active ? "outline" : "default"}
                      onClick={() => handleToggleActive(annee.id_annee_scolaire)}
                      className={annee.est_active ? "text-red-600 hover:text-red-700" : ""}
                    >
                      {annee.est_active ? 'Désactiver' : 'Activer'}
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleEdit(annee)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline" 
                      onClick={() => handleDeleteClick(annee)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      disabled={annee.est_active}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {anneesScolaires.length === 0 && (
        <div className="text-center py-12">
          <Calendar className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Aucune année scolaire configurée</h3>
          <p className="mt-1 text-sm text-gray-500">Commencez par ajouter une année scolaire.</p>
          <div className="mt-6">
            <Button onClick={() => setShowForm(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Ajouter la première année
            </Button>
          </div>
        </div>
      )}
      <ConfirmationComponent />
    </div>
  );
};

export default AnneeScolaireTab;
