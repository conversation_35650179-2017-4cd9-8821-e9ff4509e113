<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emails Sauvegardés - ScolaNova Admin</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: #2563eb;
            color: white;
            padding: 20px;
            margin: -20px -20px 20px -20px;
            border-radius: 8px 8px 0 0;
        }
        .email-list {
            display: grid;
            gap: 15px;
        }
        .email-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: #f9f9f9;
        }
        .email-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .email-info {
            flex: 1;
        }
        .email-actions {
            display: flex;
            gap: 10px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            font-size: 14px;
        }
        .btn-primary {
            background: #2563eb;
            color: white;
        }
        .btn-success {
            background: #16a085;
            color: white;
        }
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        .email-preview {
            max-height: 200px;
            overflow: hidden;
            border: 1px solid #ccc;
            padding: 10px;
            background: white;
            margin-top: 10px;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #2563eb;
        }
        .no-emails {
            text-align: center;
            padding: 40px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📧 Emails Sauvegardés - ScolaNova</h1>
            <p>Interface d'administration pour consulter les emails non envoyés</p>
        </div>

        <?php
        $emailsDir = __DIR__ . '/../emails_backup';
        $emails = [];

        if (is_dir($emailsDir)) {
            $files = glob($emailsDir . '/email_*.html');
            
            foreach ($files as $file) {
                $content = file_get_contents($file);
                $info = [
                    'file' => basename($file),
                    'path' => $file,
                    'size' => filesize($file),
                    'date' => filemtime($file),
                    'content' => $content
                ];
                
                // Extraire les informations de l'email
                if (preg_match('/<strong>Destinataire:<\/strong>\s*([^<]+)/', $content, $matches)) {
                    $info['recipient'] = trim($matches[1]);
                }
                if (preg_match('/<strong>Sujet:<\/strong>\s*([^<]+)/', $content, $matches)) {
                    $info['subject'] = trim($matches[1]);
                }
                if (preg_match('/class=[\'"]password[\'"]>([^<]+)</', $content, $matches)) {
                    $info['password'] = trim($matches[1]);
                }
                
                $emails[] = $info;
            }
            
            // Trier par date (plus récent en premier)
            usort($emails, function($a, $b) {
                return $b['date'] - $a['date'];
            });
        }
        ?>

        <!-- Statistiques -->
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number"><?= count($emails) ?></div>
                <div>Emails sauvegardés</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?= count(array_filter($emails, function($e) { return isset($e['password']); })) ?></div>
                <div>Mots de passe temporaires</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?= is_dir($emailsDir) ? round(array_sum(array_map('filesize', glob($emailsDir . '/*.html'))) / 1024, 1) : 0 ?> KB</div>
                <div>Taille totale</div>
            </div>
        </div>

        <?php if (empty($emails)): ?>
            <div class="no-emails">
                <h3>Aucun email sauvegardé</h3>
                <p>Les emails non envoyés apparaîtront ici automatiquement.</p>
            </div>
        <?php else: ?>
            <div class="email-list">
                <?php foreach ($emails as $email): ?>
                    <div class="email-item">
                        <div class="email-header">
                            <div class="email-info">
                                <strong><?= htmlspecialchars($email['recipient'] ?? 'Destinataire inconnu') ?></strong><br>
                                <small><?= htmlspecialchars($email['subject'] ?? 'Pas de sujet') ?></small><br>
                                <small style="color: #666;">
                                    📅 <?= date('d/m/Y H:i:s', $email['date']) ?> | 
                                    📁 <?= round($email['size'] / 1024, 1) ?> KB
                                    <?php if (isset($email['password'])): ?>
                                        | 🔑 Mot de passe: <code><?= htmlspecialchars($email['password']) ?></code>
                                    <?php endif; ?>
                                </small>
                            </div>
                            <div class="email-actions">
                                <a href="?view=<?= urlencode($email['file']) ?>" class="btn btn-primary" target="_blank">👁️ Voir</a>
                                <a href="?download=<?= urlencode($email['file']) ?>" class="btn btn-success">💾 Télécharger</a>
                                <a href="?delete=<?= urlencode($email['file']) ?>" class="btn btn-danger" 
                                   onclick="return confirm('Êtes-vous sûr de vouloir supprimer cet email ?')">🗑️ Supprimer</a>
                            </div>
                        </div>
                        
                        <div class="email-preview">
                            <?= substr(strip_tags($email['content']), 0, 300) ?>...
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <?php
        // Gestion des actions
        if (isset($_GET['view']) && !empty($_GET['view'])) {
            $file = $emailsDir . '/' . basename($_GET['view']);
            if (file_exists($file)) {
                header('Content-Type: text/html; charset=utf-8');
                readfile($file);
                exit;
            }
        }

        if (isset($_GET['download']) && !empty($_GET['download'])) {
            $file = $emailsDir . '/' . basename($_GET['download']);
            if (file_exists($file)) {
                header('Content-Type: application/octet-stream');
                header('Content-Disposition: attachment; filename="' . basename($file) . '"');
                header('Content-Length: ' . filesize($file));
                readfile($file);
                exit;
            }
        }

        if (isset($_GET['delete']) && !empty($_GET['delete'])) {
            $file = $emailsDir . '/' . basename($_GET['delete']);
            if (file_exists($file)) {
                unlink($file);
                header('Location: ' . $_SERVER['PHP_SELF']);
                exit;
            }
        }
        ?>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; font-size: 14px;">
            <h4>💡 Instructions pour activer l'envoi d'email :</h4>
            <ol>
                <li><strong>Gmail :</strong> Configurez un mot de passe d'application dans votre compte Gmail</li>
                <li><strong>SMTP :</strong> Modifiez le fichier <code>backend/config/email.php</code></li>
                <li><strong>PHPMailer :</strong> Installez avec <code>composer require phpmailer/phpmailer</code></li>
                <li><strong>Test :</strong> Utilisez un serveur web avec SMTP configuré (pas en local)</li>
            </ol>
            
            <p><strong>En attendant :</strong> Vous pouvez copier-coller le contenu des emails sauvegardés et les envoyer manuellement aux parents.</p>
        </div>
    </div>
</body>
</html>
