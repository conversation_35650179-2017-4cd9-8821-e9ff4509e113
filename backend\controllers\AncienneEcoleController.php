<?php

require_once '../models/AncienneEcole.php';

class AncienneEcoleController
{
    private $ancienneEcole;

    public function __construct($db)
    {
        $this->ancienneEcole = new AncienneEcole($db);
    }

    public function create()
    {
        try {
            $data = json_decode(file_get_contents("php://input"), true);
            
            // Validation des données
            if (empty($data['code_gresa']) || empty($data['nom']) || empty($data['type']) || 
                empty($data['cycle']) || empty($data['adresse'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Tous les champs sont requis']);
                return;
            }

            $result = $this->ancienneEcole->create($data);
            if ($result) {
                echo json_encode(['success' => true, 'data' => ['code_gresa' => $result], 'message' => 'Ancienne école créée avec succès']);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Erreur lors de la création de l\'ancienne école']);
            }
        } catch (Exception $e) {
            error_log("Erreur dans AncienneEcoleController::create: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    public function getAll()
    {
        try {
            $result = $this->ancienneEcole->getAll();
            if ($result !== false) {
                echo json_encode(['success' => true, 'data' => $result]);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Erreur lors de la récupération des anciennes écoles']);
            }
        } catch (Exception $e) {
            error_log("Erreur dans AncienneEcoleController::getAll: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    public function getByCodeGresa($code_gresa)
    {
        try {
            $result = $this->ancienneEcole->findByCodeGresa($code_gresa);
            if ($result) {
                echo json_encode(['success' => true, 'data' => $result]);
            } else {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Ancienne école non trouvée']);
            }
        } catch (Exception $e) {
            error_log("Erreur dans AncienneEcoleController::getByCodeGresa: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    public function update($code_gresa)
    {
        try {
            $data = json_decode(file_get_contents("php://input"), true);
            
            $result = $this->ancienneEcole->update($code_gresa, $data);
            if ($result) {
                echo json_encode(['success' => true, 'message' => 'Ancienne école mise à jour avec succès']);
            } else {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Ancienne école non trouvée ou aucune modification']);
            }
        } catch (Exception $e) {
            error_log("Erreur dans AncienneEcoleController::update: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    public function delete($code_gresa)
    {
        try {
            $result = $this->ancienneEcole->delete($code_gresa);
            if ($result) {
                echo json_encode(['success' => true, 'message' => 'Ancienne école supprimée avec succès']);
            } else {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Ancienne école non trouvée']);
            }
        } catch (Exception $e) {
            error_log("Erreur dans AncienneEcoleController::delete: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }
}
