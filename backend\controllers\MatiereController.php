<?php

require_once __DIR__ . '/../models/Matiere.php';

class MatiereController
{
    private $matiere;

    public function __construct($db)
    {
        $this->matiere = new Matiere($db);
    }

    public function getAll()
    {
        try {
            $matieres = $this->matiere->getAll();
            echo json_encode([
                'success' => true,
                'data' => $matieres,
                'message' => 'Matières récupérées avec succès'
            ]);
        } catch (Exception $e) {
            error_log("Erreur dans MatiereController::getAll: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'data' => [],
                'message' => 'Erreur lors de la récupération des matières'
            ]);
        }
    }

    public function getById($id)
    {
        try {
            $matiere = $this->matiere->getById($id);
            if ($matiere) {
                echo json_encode([
                    'success' => true,
                    'data' => $matiere,
                    'message' => 'Matière récupérée avec succès'
                ]);
            } else {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'data' => null,
                    'message' => 'Matière non trouvée'
                ]);
            }
        } catch (Exception $e) {
            error_log("Erreur dans MatiereController::getById: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'data' => null,
                'message' => 'Erreur lors de la récupération de la matière'
            ]);
        }
    }

    public function create()
    {
        try {
            $data = json_decode(file_get_contents("php://input"), true);
            
            if (!$data) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Données invalides'
                ]);
                return;
            }

            $id = $this->matiere->create($data);
            if ($id) {
                echo json_encode([
                    'success' => true,
                    'id_matiere' => $id,
                    'message' => 'Matière créée avec succès'
                ]);
            } else {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la création de la matière'
                ]);
            }
        } catch (Exception $e) {
            error_log("Erreur dans MatiereController::create: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur serveur: ' . $e->getMessage()
            ]);
        }
    }

    public function update($id)
    {
        try {
            $data = json_decode(file_get_contents("php://input"), true);
            
            if (!$data) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Données invalides'
                ]);
                return;
            }

            if ($this->matiere->update($id, $data)) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Matière mise à jour avec succès'
                ]);
            } else {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la mise à jour de la matière'
                ]);
            }
        } catch (Exception $e) {
            error_log("Erreur dans MatiereController::update: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur serveur: ' . $e->getMessage()
            ]);
        }
    }

    public function delete($id)
    {
        try {
            if ($this->matiere->delete($id)) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Matière supprimée avec succès'
                ]);
            } else {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'message' => 'Matière non trouvée ou erreur lors de la suppression'
                ]);
            }
        } catch (Exception $e) {
            error_log("Erreur dans MatiereController::delete: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur serveur: ' . $e->getMessage()
            ]);
        }
    }
}
