import React, { useState, useCallback } from 'react';
import ConfirmationModal from '../components/ConfirmationModal';

interface ConfirmationOptions {
  title?: string;
  message?: string;
  confirmText?: string;
  cancelText?: string;
  type?: 'danger' | 'warning' | 'info';
  itemName?: string;
}

interface ConfirmationState extends ConfirmationOptions {
  isOpen: boolean;
  isLoading: boolean;
  onConfirm?: () => void | Promise<void>;
}

export const useConfirmation = () => {
  const [state, setState] = useState<ConfirmationState>({
    isOpen: false,
    isLoading: false,
  });

  const showConfirmation = useCallback((
    onConfirm: () => void | Promise<void>,
    options: ConfirmationOptions = {}
  ) => {
    setState({
      isOpen: true,
      isLoading: false,
      onConfirm,
      ...options,
    });
  }, []);

  const hideConfirmation = useCallback(() => {
    setState(prev => ({
      ...prev,
      isOpen: false,
      isLoading: false,
    }));
  }, []);

  const handleConfirm = useCallback(async () => {
    if (!state.onConfirm) return;

    setState(prev => ({ ...prev, isLoading: true }));

    try {
      await state.onConfirm();
      hideConfirmation();
    } catch (error) {
      console.error('Erreur lors de la confirmation:', error);
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, [state.onConfirm, hideConfirmation]);

  const ConfirmationComponent = useCallback(() => (
    <ConfirmationModal
      isOpen={state.isOpen}
      onClose={hideConfirmation}
      onConfirm={handleConfirm}
      title={state.title}
      message={state.message}
      confirmText={state.confirmText}
      cancelText={state.cancelText}
      type={state.type}
      itemName={state.itemName}
      isLoading={state.isLoading}
    />
  ), [state, hideConfirmation, handleConfirm]);

  return {
    showConfirmation,
    hideConfirmation,
    ConfirmationComponent,
    isLoading: state.isLoading,
  };
};

// Hook spécialisé pour les suppressions
export const useDeleteConfirmation = () => {
  const { showConfirmation, ConfirmationComponent, isLoading } = useConfirmation();

  const confirmDelete = useCallback((
    onDelete: () => void | Promise<void>,
    itemName?: string,
    customMessage?: string
  ) => {
    showConfirmation(onDelete, {
      title: "Confirmer la suppression",
      message: customMessage,
      confirmText: "Supprimer",
      cancelText: "Annuler",
      type: 'danger',
      itemName,
    });
  }, [showConfirmation]);

  return {
    confirmDelete,
    ConfirmationComponent,
    isLoading,
  };
};

export default useConfirmation;
