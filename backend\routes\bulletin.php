<?php

require_once __DIR__ . '/../controllers/BulletinController.php';

function handleBulletinRoutes($uri, $method, $pdo) {
    $bulletinController = new BulletinController($pdo);

    // GET /bulletins/eleve/{id_eleve}/{semestre} - Récupérer les données du bulletin d'un élève
    if (preg_match('/^\/bulletins\/eleve\/(\d+)\/(S1|S2)$/', $uri, $matches) && $method === 'GET') {
        $id_eleve = (int)$matches[1];
        $semestre = $matches[2];
        $bulletinController->getBulletinData($id_eleve, $semestre);
        return true;
    }

    // GET /bulletins/pdf/{id_eleve}/{semestre} - Télécharger le bulletin en PDF
    if (preg_match('/^\/bulletins\/pdf\/(\d+)\/(S1|S2)$/', $uri, $matches) && $method === 'GET') {
        $id_eleve = (int)$matches[1];
        $semestre = $matches[2];
        $bulletinController->generatePDF($id_eleve, $semestre);
        return true;
    }

    // GET /bulletins/classe/{id_classe}/{semestre} - Récupérer les bulletins d'une classe
    if (preg_match('/^\/bulletins\/classe\/(\d+)\/(S1|S2)$/', $uri, $matches) && $method === 'GET') {
        $id_classe = (int)$matches[1];
        $semestre = $matches[2];
        $bulletinController->getBulletinsClasse($id_classe, $semestre);
        return true;
    }

    return false;
}
?>
