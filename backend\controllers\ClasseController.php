<?php

require_once '../models/Classe.php';

class ClasseController
{
    private $classe;

    public function __construct($db)
    {
        $this->classe = new Classe($db);
    }

    public function create()
    {
        try {
            $data = json_decode(file_get_contents("php://input"), true);
            
            // Validation des données
            if (empty($data['id_niveau']) || empty($data['nom_classe'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Niveau et nom de classe sont requis']);
                return;
            }

            // Vérifier si la classe existe déjà pour ce niveau
            if ($this->classe->exists($data['id_niveau'], $data['nom_classe'])) {
                http_response_code(409);
                echo json_encode(['success' => false, 'message' => 'Cette classe existe déjà pour ce niveau']);
                return;
            }

            $result = $this->classe->create($data);
            if ($result) {
                echo json_encode([
                    'success' => true, 
                    'data' => ['id_classe' => $result], 
                    'message' => 'Classe créée avec succès'
                ]);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Erreur lors de la création de la classe']);
            }
        } catch (Exception $e) {
            error_log("Erreur dans ClasseController::create: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    public function getAll()
    {
        try {
            $result = $this->classe->getAll();
            if ($result !== false) {
                echo json_encode(['success' => true, 'data' => $result]);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Erreur lors de la récupération des classes']);
            }
        } catch (Exception $e) {
            error_log("Erreur dans ClasseController::getAll: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    public function getById($id)
    {
        try {
            $result = $this->classe->getById($id);
            if ($result) {
                echo json_encode(['success' => true, 'data' => $result]);
            } else {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Classe non trouvée']);
            }
        } catch (Exception $e) {
            error_log("Erreur dans ClasseController::getById: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    public function getByNiveau($idNiveau)
    {
        try {
            $result = $this->classe->getByNiveau($idNiveau);
            if ($result !== false) {
                echo json_encode(['success' => true, 'data' => $result]);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Erreur lors de la récupération des classes']);
            }
        } catch (Exception $e) {
            error_log("Erreur dans ClasseController::getByNiveau: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    public function update($id)
    {
        try {
            $data = json_decode(file_get_contents("php://input"), true);
            
            // Validation des données
            if (empty($data['id_niveau']) || empty($data['nom_classe'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Niveau et nom de classe sont requis']);
                return;
            }

            // Vérifier si la classe existe déjà (en excluant l'ID actuel)
            if ($this->classe->exists($data['id_niveau'], $data['nom_classe'], $id)) {
                http_response_code(409);
                echo json_encode(['success' => false, 'message' => 'Cette classe existe déjà pour ce niveau']);
                return;
            }

            $result = $this->classe->update($id, $data);
            if ($result) {
                echo json_encode(['success' => true, 'message' => 'Classe mise à jour avec succès']);
            } else {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Classe non trouvée ou aucune modification']);
            }
        } catch (Exception $e) {
            error_log("Erreur dans ClasseController::update: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    public function delete($id)
    {
        try {
            $result = $this->classe->delete($id);
            if ($result) {
                echo json_encode(['success' => true, 'message' => 'Classe supprimée avec succès']);
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Impossible de supprimer cette classe (des élèves y sont peut-être inscrits)']);
            }
        } catch (Exception $e) {
            error_log("Erreur dans ClasseController::delete: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    public function getEleves($id)
    {
        try {
            $result = $this->classe->getEleves($id);
            if ($result !== false) {
                echo json_encode(['success' => true, 'data' => $result]);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Erreur lors de la récupération des élèves']);
            }
        } catch (Exception $e) {
            error_log("Erreur dans ClasseController::getEleves: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }


}
