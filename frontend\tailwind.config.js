
/** @type {import('tailwindcss').Config} */
const defaultTheme = require('tailwindcss/defaultTheme');
export default {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    extend: {
      colors: {
        primary: '#1D4ED8', // '#005bac', #0540AD ,#0D47A1 
        secondary: '#0540AD', // '#004589', 2563EB
        grayish: '#98a3a9',
        background: '#F2F5F9',
        success: '#27AE60',
        warning: '#F1C40F',
        error: '#E74C3C',
      },
      // fontFamily: {
      //   poppins: ['Poppins', 'sans-serif'],
      // }
       fontFamily: {
        poppins: ['Poppins', ...defaultTheme.fontFamily.sans],
        myfont: ['MyFont', ...defaultTheme.fontFamily.sans],
      },
    },
  },
  plugins: [],
}
