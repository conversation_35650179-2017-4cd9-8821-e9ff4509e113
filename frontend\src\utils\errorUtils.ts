/**
 * Utilitaires pour la gestion des erreurs
 */

/**
 * Extrait le message d'erreur du backend à partir de la structure de réponse
 * @param error - L'objet erreur retourné par l'API
 * @param defaultMessage - Message par défaut si aucun message spécifique n'est trouvé
 * @returns Le message d'erreur à afficher
 */
export const extractErrorMessage = (error: any, defaultMessage: string): string => {
  console.log('Structure complète de l\'erreur:', error);

  // Vérifier différentes structures de réponse d'erreur
  if (error.response?.data?.message) {
    return error.response.data.message;
  } else if (error.response?.data?.data?.message) {
    return error.response.data.data.message;
  } else if (error.data?.message) {
    return error.data.message;
  } else if (error.response?.data?.error) {
    return error.response.data.error;
  } else if (error.response?.statusText && error.response.status !== 500) {
    return `Erreur ${error.response.status}: ${error.response.statusText}`;
  } else if (error.message && !error.message.includes('status code') && error.message !== 'Network Error') {
    return error.message;
  }

  // Messages spécifiques selon le code de statut
  if (error.response?.status) {
    switch (error.response.status) {
      case 400:
        return 'Données invalides. Veuillez vérifier les informations saisies.';
      case 401:
        return 'Accès non autorisé. Veuillez vous reconnecter.';
      case 403:
        return 'Vous n\'avez pas les permissions nécessaires pour cette action.';
      case 404:
        return 'Ressource non trouvée.';
      case 409:
        // Pour les conflits, utiliser le message spécifique du backend s'il existe
        if (error.response?.data?.message) {
          return error.response.data.message;
        }
        return 'Conflit de données. Cette ressource existe déjà.';
      case 422:
        return 'Données non valides. Veuillez corriger les erreurs.';
      case 500:
        return 'Erreur interne du serveur. Veuillez réessayer plus tard.';
      case 503:
        return 'Service temporairement indisponible. Veuillez réessayer plus tard.';
      default:
        return defaultMessage;
    }
  }

  return defaultMessage;
};

/**
 * Extrait le message de succès du backend
 * @param response - La réponse de l'API
 * @param defaultMessage - Message par défaut si aucun message spécifique n'est trouvé
 * @returns Le message de succès à afficher
 */
export const extractSuccessMessage = (response: any, defaultMessage: string): string => {
  if (response.data?.message) {
    return response.data.message;
  } else if (response.data?.data?.message) {
    return response.data.data.message;
  }

  return defaultMessage;
};

/**
 * Extrait les erreurs de validation du backend pour les afficher dans les inputs
 * @param error - L'objet erreur retourné par l'API
 * @returns Un objet avec les erreurs par champ
 */
export const extractValidationErrors = (error: any): { [key: string]: string } => {
  const errors: { [key: string]: string } = {};

  // Structure Laravel/Symfony standard
  if (error.response?.data?.errors) {
    Object.keys(error.response.data.errors).forEach(field => {
      const fieldErrors = error.response.data.errors[field];
      errors[field] = Array.isArray(fieldErrors) ? fieldErrors[0] : fieldErrors;
    });
    return errors;
  }

  // Structure personnalisée
  if (error.response?.data?.validation_errors) {
    return error.response.data.validation_errors;
  }

  // Analyser le message d'erreur pour extraire les champs (fallback)
  if (error.response?.data?.message) {
    const message = error.response.data.message.toLowerCase();

    // Patterns communs d'erreurs de validation
    if (message.includes('email') && message.includes('existe')) {
      errors.email = error.response.data.message;
    } else if (message.includes('nom') && message.includes('requis')) {
      errors.nom = error.response.data.message;
    } else if (message.includes('prénom') || message.includes('prenom')) {
      errors.prenom = error.response.data.message;
    } else if (message.includes('téléphone') || message.includes('telephone')) {
      errors.telephone = error.response.data.message;
    } else if (message.includes('mot de passe') || message.includes('password')) {
      errors.mot_de_passe = error.response.data.message;
    } else if (message.includes('date')) {
      if (message.includes('début')) {
        errors.date_debut = error.response.data.message;
      } else if (message.includes('fin')) {
        errors.date_fin = error.response.data.message;
      } else {
        errors.date = error.response.data.message;
      }
    } else if (message.includes('prix') && message.includes('négatif')) {
      errors.prix_mensuel = error.response.data.message;
      errors.frais_inscription = error.response.data.message;
    }
  }

  return errors;
};

/**
 * Gère les erreurs de manière globale avec logging et notification
 * @param error - L'objet erreur
 * @param context - Contexte de l'erreur (nom de la fonction, composant, etc.)
 * @param defaultMessage - Message par défaut
 * @returns Le message d'erreur formaté
 */
export const handleGlobalError = (error: any, context: string, defaultMessage: string): string => {
  // Logger l'erreur pour le debugging
  console.group(`🚨 Erreur dans ${context}`);
  console.error('Erreur complète:', error);
  console.error('Status:', error.response?.status);
  console.error('Data:', error.response?.data);
  console.error('Message:', error.message);
  console.groupEnd();

  // Extraire le message d'erreur
  const errorMessage = extractErrorMessage(error, defaultMessage);

  // Optionnel: Envoyer à un service de monitoring (Sentry, LogRocket, etc.)
  // sendToMonitoring(error, context);

  return errorMessage;
};

/**
 * Vérifie si une erreur est une erreur de réseau
 * @param error - L'objet erreur
 * @returns true si c'est une erreur de réseau
 */
export const isNetworkError = (error: any): boolean => {
  return error.message === 'Network Error' ||
         error.code === 'NETWORK_ERROR' ||
         !error.response;
};

/**
 * Vérifie si une erreur est une erreur d'authentification
 * @param error - L'objet erreur
 * @returns true si c'est une erreur d'authentification
 */
export const isAuthError = (error: any): boolean => {
  return error.response?.status === 401 || error.response?.status === 403;
};
