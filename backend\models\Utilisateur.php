<?php

require_once __DIR__ . '/../config/db.php';
require_once __DIR__ . '/../services/EmailService.php';
require_once __DIR__ . '/../services/SMTPEmailService.php';

class Utilisateur
{
    private $pdo;
    private $emailService;
    private $smtpEmailService;

    public function __construct($db)
    {
        $this->pdo = $db;
        $this->emailService = new EmailService();
        $this->smtpEmailService = new SMTPEmailService();
    }

    /**
     * Récupérer tous les utilisateurs
     */
    public function getAll()
    {
        try {
            $stmt = $this->pdo->prepare("SELECT * FROM utilisateur ORDER BY nom, prenom");
            $stmt->execute();
            $users = [];
            while ($user = $stmt->fetch(PDO::FETCH_ASSOC)) {
                unset($user['mot_de_passe']); // Ne pas retourner le mot de passe
                $users[] = $user;
            }
            return $users;
        } catch (PDOException $e) {
            error_log("Erreur lors de la récupération des utilisateurs: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Récupérer uniquement les utilisateurs admin
     */
    public function getAdmins()
    {
        try {
            $stmt = $this->pdo->prepare("SELECT * FROM utilisateur WHERE role = 'admin' ORDER BY nom, prenom");
            $stmt->execute();
            $users = [];
            while ($user = $stmt->fetch(PDO::FETCH_ASSOC)) {
                unset($user['mot_de_passe']); // Ne pas retourner le mot de passe
                $users[] = $user;
            }
            return $users;
        } catch (PDOException $e) {
            error_log("Erreur lors de la récupération des administrateurs: " . $e->getMessage());
            return [];
        }
    }

    public function getById($id)
    {
        $stmt = $this->pdo->prepare("SELECT * FROM utilisateur WHERE id_utilisateur = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($user) {
            unset($user['mot_de_passe']);
            return $user;
        }
        return false;
    }

    /**
     * Récupérer un utilisateur par email
     */
    public function getByEmail($email)
    {
        try {
            $stmt = $this->pdo->prepare("SELECT * FROM utilisateur WHERE email = :email");
            $stmt->bindParam(':email', $email, PDO::PARAM_STR);
            $stmt->execute();
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            return $user ? $user : false;
        } catch (PDOException $e) {
            error_log("Erreur lors de la récupération de l'utilisateur par email: " . $e->getMessage());
            return false;
        }
    }

    function update($id, $data)
    {
        try {
            error_log("Mise à jour utilisateur $id avec données : " . json_encode($data));

            // Construire la requête dynamiquement en fonction des champs fournis
            $fields = [];
            $params = [':id' => $id];

            // Champs obligatoires
            if (isset($data['nom'])) {
                $fields[] = "nom = :nom";
                $params[':nom'] = $data['nom'];
            }
            if (isset($data['prenom'])) {
                $fields[] = "prenom = :prenom";
                $params[':prenom'] = $data['prenom'];
            }
            if (isset($data['email'])) {
                $fields[] = "email = :email";
                $params[':email'] = $data['email'];
            }

            // Champs optionnels
            if (isset($data['role'])) {
                $fields[] = "role = :role";
                $params[':role'] = $data['role'];
            }
            if (isset($data['sexe'])) {
                $fields[] = "sexe = :sexe";
                $params[':sexe'] = $data['sexe'];
            }
            if (isset($data['date_naissance'])) {
                $fields[] = "date_naissance = :date_naissance";
                $params[':date_naissance'] = $data['date_naissance'];
            }
            if (isset($data['lieu_naissance'])) {
                $fields[] = "lieu_naissance = :lieu_naissance";
                $params[':lieu_naissance'] = $data['lieu_naissance'];
            }
            if (isset($data['nationalite'])) {
                $fields[] = "nationalite = :nationalite";
                $params[':nationalite'] = $data['nationalite'];
            }
            if (isset($data['telephone'])) {
                $fields[] = "telephone = :telephone";
                $params[':telephone'] = $data['telephone'];
            }
            if (isset($data['adresse'])) {
                $fields[] = "adresse = :adresse";
                $params[':adresse'] = $data['adresse'];
            }
            if (isset($data['photo'])) {
                $fields[] = "photo = :photo";
                $params[':photo'] = $data['photo'];
            }
            if (isset($data['est_valide'])) {
                $fields[] = "est_valide = :est_valide";
                $params[':est_valide'] = $data['est_valide'];
            }
            if (isset($data['est_actif'])) {
                $fields[] = "est_actif = :est_actif";
                $params[':est_actif'] = $data['est_actif'];
            }

            if (empty($fields)) {
                error_log("Aucun champ à mettre à jour pour l'utilisateur $id");
                return false;
            }

            $sql = "UPDATE utilisateur SET " . implode(', ', $fields) . " WHERE id_utilisateur = :id";
            error_log("Requête SQL : $sql");
            error_log("Paramètres : " . json_encode($params));

            $stmt = $this->pdo->prepare($sql);

            foreach ($params as $key => $value) {
                if ($key === ':id') {
                    $stmt->bindValue($key, $value, PDO::PARAM_INT);
                } elseif ($key === ':est_valide' || $key === ':est_actif') {
                    $stmt->bindValue($key, $value, PDO::PARAM_BOOL);
                } else {
                    $stmt->bindValue($key, $value, PDO::PARAM_STR);
                }
            }

            $result = $stmt->execute();
            error_log("Résultat de la mise à jour : " . ($result ? 'succès' : 'échec'));
            return $result;
        } catch (PDOException $e) {
            error_log("Erreur SQL dans Utilisateur::update : " . $e->getMessage());

            // Vérifier si c'est une erreur de contrainte d'unicité pour l'email
            if ($e->getCode() == 23000 && strpos($e->getMessage(), 'email') !== false) {
                throw new Exception("Cet email est déjà utilisé par un autre utilisateur");
            }

            // Autres erreurs de contrainte
            if ($e->getCode() == 23000) {
                throw new Exception("Cette donnée existe déjà dans le système");
            }

            throw new Exception("Erreur lors de la mise à jour : " . $e->getMessage());
        }
    }

    public function delete($id)
    {
        $stmt = $this->pdo->prepare("DELETE FROM utilisateur WHERE id_utilisateur = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        if ($stmt->rowCount() > 0) {
            return true;
        } else {
            return  false;
        }
    }

    /**
     * Génère un mot de passe mixte : lettres, chiffres et symboles
     * 8 caractères avec majuscules, minuscules, chiffres et symboles
     */
    private function generatePassword()
    {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-=';
        $password = '';
        for ($i = 0; $i < 8; $i++) {
            $password .= $chars[random_int(0, strlen($chars) - 1)];
        }
        return $password;
    }

    public function create($data)
    {
        try {
            error_log("Utilisateur::create - Début avec données : " . json_encode($data));

            // Générer un mot de passe temporaire si aucun n'est fourni
            $temporaryPassword = empty($data['mot_de_passe']) ? $this->generatePassword() : $data['mot_de_passe'];

            $sql = "INSERT INTO utilisateur (nom, prenom, email, mot_de_passe, role,
                    date_naissance, lieu_naissance, sexe, nationalite, telephone, adresse, est_valide, est_actif)
                VALUES (:nom, :prenom, :email, :password, :role,
                    :date_naissance, :lieu_naissance, :sexe, :nationalite, :telephone, :adresse, :est_valide, :est_actif)";

            $stmt = $this->pdo->prepare($sql);

            $passwordHash = password_hash($temporaryPassword, PASSWORD_DEFAULT);

            $stmt->bindValue(':nom', $data['nom']);
            $stmt->bindValue(':prenom', $data['prenom']);
            $stmt->bindValue(':email', $data['email']);
            $stmt->bindValue(':password', $passwordHash);
            $stmt->bindValue(':role', $data['role']);
            $stmt->bindValue(':date_naissance', !empty($data['date_naissance']) ? $data['date_naissance'] : null);
            $stmt->bindValue(':lieu_naissance', !empty($data['lieu_naissance']) ? $data['lieu_naissance'] : null);
            $stmt->bindValue(':sexe', $data['sexe']);
            $stmt->bindValue(':nationalite', $data['nationalite']);
            $stmt->bindValue(':telephone', $data['telephone']);
            $stmt->bindValue(':adresse', $data['adresse']);

            // Définir est_valide à false par défaut pour forcer le changement de mot de passe
            $est_valide = isset($data['est_valide']) ? $data['est_valide'] : false;
            $est_actif = isset($data['est_actif']) ? $data['est_actif'] : true;
            $stmt->bindValue(':est_valide', $est_valide, PDO::PARAM_BOOL);
            $stmt->bindValue(':est_actif', $est_actif, PDO::PARAM_BOOL);

            if ($stmt->execute()) {
                $userId = $this->pdo->lastInsertId();
                error_log("Utilisateur créé avec ID : " . $userId);

                // Envoyer le mot de passe temporaire par email si un mot de passe a été généré
                if (empty($data['mot_de_passe'])) {
                    $fullName = $data['prenom'] . ' ' . $data['nom'];

                    error_log("Tentative d'envoi email via SMTP pour: " . $data['email']);

                    // Essayer d'abord avec SMTP
                    $emailSent = $this->smtpEmailService->sendTemporaryPassword(
                        $data['email'],
                        $fullName,
                        $temporaryPassword
                    );

                    // Si SMTP échoue, essayer avec le service email classique
                    if (!$emailSent) {
                        error_log("Envoi SMTP échoué, tentative avec EmailService classique");
                        $emailSent = $this->emailService->sendTemporaryPassword(
                            $data['email'],
                            $fullName,
                            $temporaryPassword
                        );
                    }

                    if ($emailSent) {
                        error_log("Email envoyé avec succès pour: " . $data['email']);
                    } else {
                        error_log("Échec envoi email mot de passe temporaire pour: " . $data['email']);
                    }
                }

                return $userId;
            } else {
                error_log("Erreur SQL lors de l'insertion utilisateur : " . implode(", ", $stmt->errorInfo()));
                return false;
            }
        } catch (PDOException $e) {
            error_log("Exception PDO dans Utilisateur::create : " . $e->getMessage());
            error_log("Données reçues : " . json_encode($data));

            // Vérifier si c'est une erreur de contrainte d'unicité pour l'email
            if ($e->getCode() == 23000 && strpos($e->getMessage(), 'email') !== false) {
                throw new Exception("Cet email est déjà utilisé par un autre utilisateur");
            }

            // Autres erreurs de contrainte
            if ($e->getCode() == 23000) {
                throw new Exception("Cette donnée existe déjà dans le système");
            }

            throw new Exception("Erreur lors de la création : " . $e->getMessage());
        } catch (Exception $e) {
            error_log("Exception générale dans Utilisateur::create : " . $e->getMessage());
            throw $e; // Re-lancer l'exception pour qu'elle soit gérée par le contrôleur
        }
    }

    /**
     * Authentification d'un utilisateur
     */
    public function authenticate($data)
    {
        $sql = "SELECT * FROM utilisateur WHERE email = :email AND est_actif = 1";
        $stmt = $this->pdo->prepare($sql);
        $stmt->bindValue(':email', $data['email']);
        $stmt->execute();
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($user && password_verify($data['password'], $user['mot_de_passe'])) {
            // Ne pas retourner le mot de passe
            unset($user['mot_de_passe']);
            return $user;
        }

        return false;
    }

    /**
     * Change le mot de passe et active le compte (est_valide = true)
     */
    public function changePassword($userId, $newPassword)
    {
        try {
            error_log("Utilisateur::changePassword - Début pour userId: $userId");

            $passwordHash = password_hash($newPassword, PASSWORD_DEFAULT);
            error_log("Mot de passe hashé généré");

            $sql = "UPDATE utilisateur SET
                        mot_de_passe = :password,
                        est_valide = 1,
                        date_modification = NOW()
                    WHERE id_utilisateur = :id";

            $stmt = $this->pdo->prepare($sql);
            $stmt->bindValue(':password', $passwordHash);
            $stmt->bindValue(':id', $userId, PDO::PARAM_INT);

            error_log("Exécution de la requête UPDATE");
            $success = $stmt->execute();
            error_log("Résultat execute(): " . ($success ? 'true' : 'false'));
            error_log("Lignes affectées: " . $stmt->rowCount());

            if ($success && $stmt->rowCount() > 0) {
                // Envoyer un email de confirmation
                try {
                    $user = $this->getById($userId);
                    if ($user) {
                        $this->smtpEmailService->sendPasswordChangeConfirmation($user['email'], $user['prenom'] . ' ' . $user['nom']);
                    }
                } catch (Exception $emailError) {
                    // Log l'erreur email mais ne pas faire échouer le changement de mot de passe
                    error_log("Erreur envoi email confirmation changement mot de passe: " . $emailError->getMessage());
                }

                error_log("Utilisateur::changePassword - Succès");
                return true;
            } else {
                error_log("Échec de la requête UPDATE");
                $errorInfo = $stmt->errorInfo();
                error_log("Erreur SQL: " . json_encode($errorInfo));
                return false;
            }
        } catch (Exception $e) {
            error_log("Exception dans Utilisateur::changePassword: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * Vérifie si l'utilisateur doit changer son mot de passe (est_valide = false)
     */
    public function mustChangePassword($userId)
    {
        $stmt = $this->pdo->prepare("SELECT est_valide FROM utilisateur WHERE id_utilisateur = :id");
        $stmt->bindValue(':id', $userId, PDO::PARAM_INT);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        return $result ? !$result['est_valide'] : false;
    }
}

?>
