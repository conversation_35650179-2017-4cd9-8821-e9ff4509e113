<?php

class Cours {
    private $pdo;
    private $table = 'Cours';

    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    /**  R<PERSON>cup<PERSON>rer tous les cours avec leurs relations  */
    public function getAll() {
        try {
            $query = "SELECT c.id_cours, c.id_unite, c.id_enseignant, c.id_classe,
                c.id_salle, c.jour_semaine, c.heure_debut, c.heure_fin,
                u.nom_unite, m.nom_matiere_fr, m.nom_matiere_ar, cl.nom_classe,
                CONCAT(ut.prenom, ' ', ut.nom) as nom_enseignant, s.nom_salle, s.capacite
            FROM " . $this->table . " c
            JOIN Unite u ON c.id_unite = u.id_unite
            JOIN Matiere m ON u.id_matiere = m.id_matiere
            JOIN Classe cl ON c.id_classe = cl.id_classe
            JOIN Enseignant e ON c.id_enseignant = e.id_enseignant
            JOIN Utilisateur ut ON e.id_utilisateur = ut.id_utilisateur
            JOIN Salle s ON c.id_salle = s.id_salle
            ORDER BY FIELD(c.jour_semaine, '<PERSON>i', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'), c.heure_debut";
            
            $stmt = $this->pdo->prepare($query);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur lors de la récupération des cours: " . $e->getMessage());
            throw $e;
        }
    }

    /** Récupérer les cours par classe */
    public function getByClasse($id_classe) {
        try {
            $query = "SELECT c.*, u.nom_unite, m.nom_matiere_fr, m.nom_matiere_ar,
                CONCAT(ut.prenom, ' ', ut.nom) as nom_enseignant, s.nom_salle
            FROM " . $this->table . " c
            JOIN Unite u ON c.id_unite = u.id_unite
            JOIN Matiere m ON u.id_matiere = m.id_matiere
            JOIN Enseignant e ON c.id_enseignant = e.id_enseignant
            JOIN Utilisateur ut ON e.id_utilisateur = ut.id_utilisateur
            JOIN Salle s ON c.id_salle = s.id_salle
            WHERE c.id_classe = :id_classe
            ORDER BY FIELD(c.jour_semaine, 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'), c.heure_debut";
            
            $stmt = $this->pdo->prepare($query);
            $stmt->bindParam(':id_classe', $id_classe, PDO::PARAM_INT);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur lors de la récupération des cours par classe: " . $e->getMessage());
            throw $e;
        }
    }

    /** Récupérer les cours par enseignant */
    public function getByEnseignant($id_enseignant) {
        try {
            $query = "SELECT c.*, u.nom_unite, m.nom_matiere_fr, cl.nom_classe, s.nom_salle
            FROM " . $this->table . " c
            JOIN Unite u ON c.id_unite = u.id_unite
            JOIN Matiere m ON u.id_matiere = m.id_matiere
            JOIN Classe cl ON c.id_classe = cl.id_classe
            JOIN Salle s ON c.id_salle = s.id_salle
            WHERE c.id_enseignant = :id_enseignant
            ORDER BY FIELD(c.jour_semaine, 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'), c.heure_debut";
            
            $stmt = $this->pdo->prepare($query);
            $stmt->bindParam(':id_enseignant', $id_enseignant, PDO::PARAM_INT);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur lors de la récupération des cours par enseignant: " . $e->getMessage());
            throw $e;
        }
    }

    /**  Vérifier les conflits avant création/modification */
    public function checkConflits($data, $exclude_id = null) {
        try {
            $conflicts = [];
            
            // Conflit enseignant
            $query = "SELECT COUNT(*) as count FROM " . $this->table . " 
                     WHERE id_enseignant = :id_enseignant 
                     AND jour_semaine = :jour_semaine 
                     AND heure_debut < :heure_fin 
                     AND heure_fin > :heure_debut";
            
            if ($exclude_id) {
                $query .= " AND id_cours != :exclude_id";
            }
            
            $stmt = $this->pdo->prepare($query);
            $stmt->bindParam(':id_enseignant', $data['id_enseignant'], PDO::PARAM_INT);
            $stmt->bindParam(':jour_semaine', $data['jour_semaine'], PDO::PARAM_STR);
            $stmt->bindParam(':heure_debut', $data['heure_debut'], PDO::PARAM_STR);
            $stmt->bindParam(':heure_fin', $data['heure_fin'], PDO::PARAM_STR);
            if ($exclude_id) {
                $stmt->bindParam(':exclude_id', $exclude_id, PDO::PARAM_INT);
            }
            $stmt->execute();
            
            if ($stmt->fetch(PDO::FETCH_ASSOC)['count'] > 0) {
                $conflicts[] = "L'enseignant a déjà un cours à ce créneau";
            }
            
            // Conflit salle
            $query = "SELECT COUNT(*) as count FROM " . $this->table . " 
                     WHERE id_salle = :id_salle 
                     AND jour_semaine = :jour_semaine 
                     AND heure_debut < :heure_fin 
                     AND heure_fin > :heure_debut";
            
            if ($exclude_id) {
                $query .= " AND id_cours != :exclude_id";
            }
            
            $stmt = $this->pdo->prepare($query);
            $stmt->bindParam(':id_salle', $data['id_salle'], PDO::PARAM_INT);
            $stmt->bindParam(':jour_semaine', $data['jour_semaine'], PDO::PARAM_STR);
            $stmt->bindParam(':heure_debut', $data['heure_debut'], PDO::PARAM_STR);
            $stmt->bindParam(':heure_fin', $data['heure_fin'], PDO::PARAM_STR);
            if ($exclude_id) {
                $stmt->bindParam(':exclude_id', $exclude_id, PDO::PARAM_INT);
            }
            $stmt->execute();
            
            if ($stmt->fetch(PDO::FETCH_ASSOC)['count'] > 0) {
                $conflicts[] = "La salle est déjà occupée à ce créneau";
            }
            
            // Conflit classe
            $query = "SELECT COUNT(*) as count FROM " . $this->table . " 
                     WHERE id_classe = :id_classe 
                     AND jour_semaine = :jour_semaine 
                     AND heure_debut < :heure_fin 
                     AND heure_fin > :heure_debut";
            
            if ($exclude_id) {
                $query .= " AND id_cours != :exclude_id";
            }
            
            $stmt = $this->pdo->prepare($query);
            $stmt->bindParam(':id_classe', $data['id_classe'], PDO::PARAM_INT);
            $stmt->bindParam(':jour_semaine', $data['jour_semaine'], PDO::PARAM_STR);
            $stmt->bindParam(':heure_debut', $data['heure_debut'], PDO::PARAM_STR);
            $stmt->bindParam(':heure_fin', $data['heure_fin'], PDO::PARAM_STR);
            if ($exclude_id) {
                $stmt->bindParam(':exclude_id', $exclude_id, PDO::PARAM_INT);
            }
            $stmt->execute();
            
            if ($stmt->fetch(PDO::FETCH_ASSOC)['count'] > 0) {
                $conflicts[] = "La classe a déjà un cours à ce créneau";
            }
            
            return $conflicts;
        } catch (PDOException $e) {
            error_log("Erreur lors de la vérification des conflits: " . $e->getMessage());
            throw $e;
        }
    }

    /** Créer un nouveau cours */
    public function create($data) {
        try {
            // Vérifier les conflits
            $conflicts = $this->checkConflits($data);
            if (!empty($conflicts)) {
                return ['success' => false, 'conflicts' => $conflicts];
            }
            
            $query = "INSERT INTO " . $this->table . " 
                     (id_unite, id_enseignant, id_classe, id_salle, jour_semaine, heure_debut, heure_fin) 
                     VALUES (:id_unite, :id_enseignant, :id_classe, :id_salle, :jour_semaine, :heure_debut, :heure_fin)";
            
            $stmt = $this->pdo->prepare($query);
            $stmt->bindParam(':id_unite', $data['id_unite'], PDO::PARAM_INT);
            $stmt->bindParam(':id_enseignant', $data['id_enseignant'], PDO::PARAM_INT);
            $stmt->bindParam(':id_classe', $data['id_classe'], PDO::PARAM_INT);
            $stmt->bindParam(':id_salle', $data['id_salle'], PDO::PARAM_INT);
            $stmt->bindParam(':jour_semaine', $data['jour_semaine'], PDO::PARAM_STR);
            $stmt->bindParam(':heure_debut', $data['heure_debut'], PDO::PARAM_STR);
            $stmt->bindParam(':heure_fin', $data['heure_fin'], PDO::PARAM_STR);
            
            if ($stmt->execute()) {
                return ['success' => true, 'id' => $this->pdo->lastInsertId()];
            } else {
                return ['success' => false, 'message' => 'Erreur lors de la création du cours'];
            }
        } catch (PDOException $e) {
            error_log("Erreur lors de la création du cours: " . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /** Mettre à jour un cours */
    public function update($id, $data) {
        try {
            // Vérifier les conflits (en excluant le cours actuel)
            $conflicts = $this->checkConflits($data, $id);
            if (!empty($conflicts)) {
                return ['success' => false, 'conflicts' => $conflicts];
            }
            
            $query = "UPDATE " . $this->table . " SET 
                     id_unite = :id_unite,
                     id_enseignant = :id_enseignant,
                     id_classe = :id_classe,
                     id_salle = :id_salle,
                     jour_semaine = :jour_semaine,
                     heure_debut = :heure_debut,
                     heure_fin = :heure_fin
                     WHERE id_cours = :id";
            
            $stmt = $this->pdo->prepare($query);
            $stmt->bindParam(':id_unite', $data['id_unite'], PDO::PARAM_INT);
            $stmt->bindParam(':id_enseignant', $data['id_enseignant'], PDO::PARAM_INT);
            $stmt->bindParam(':id_classe', $data['id_classe'], PDO::PARAM_INT);
            $stmt->bindParam(':id_salle', $data['id_salle'], PDO::PARAM_INT);
            $stmt->bindParam(':jour_semaine', $data['jour_semaine'], PDO::PARAM_STR);
            $stmt->bindParam(':heure_debut', $data['heure_debut'], PDO::PARAM_STR);
            $stmt->bindParam(':heure_fin', $data['heure_fin'], PDO::PARAM_STR);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            
            if ($stmt->execute()) {
                return ['success' => true];
            } else {
                return ['success' => false, 'message' => 'Erreur lors de la mise à jour du cours'];
            }
        } catch (PDOException $e) {
            error_log("Erreur lors de la mise à jour du cours: " . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**  Supprimer un cours */
    public function delete($id) {
        try {
            $query = "DELETE FROM " . $this->table . " WHERE id_cours = :id";
            $stmt = $this->pdo->prepare($query);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Erreur lors de la suppression du cours: " . $e->getMessage());
            throw $e;
        }
    }

    /**  Récupérer un cours par ID */
    public function getById($id) {
        try {
            $query = "SELECT * FROM " . $this->table . " WHERE id_cours = :id";
            $stmt = $this->pdo->prepare($query);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            $stmt->execute();
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur lors de la récupération du cours: " . $e->getMessage());
            throw $e;
        }
    }
}

?>
