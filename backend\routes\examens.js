const express = require('express');
const router = express.Router();
const db = require('../config/database');
const { authenticateToken, requireRole } = require('../middleware/auth');

// Middleware d'authentification pour toutes les routes
router.use(authenticateToken);

// GET /examens - Récupérer tous les examens
router.get('/', async (req, res) => {
  try {
    const query = `
      SELECT 
        e.*,
        m.nom_matiere_fr,
        m.nom_matiere_ar,
        c.nom_classe,
        u.nom as nom_enseignant,
        u.prenom as prenom_enseignant,
        COUNT(n.id_eleve) as nombre_notes,
        AVG(n.note) as moyenne_classe
      FROM Examen e
      LEFT JOIN Matiere m ON e.id_matiere = m.id_matiere
      LEFT JOIN Classe c ON e.id_classe = c.id_classe
      LEFT JOIN Enseignant ens ON e.id_enseignant = ens.id_enseignant
      LEFT JOIN User u ON ens.id_user = u.id_user
      LEFT JOIN Note n ON e.id_examen = n.id_examen
      GROUP BY e.id_examen
      ORDER BY e.date_examen DESC, e.id_examen DESC
    `;

    const [examens] = await db.execute(query);

    // Formater les données
    const examensFormates = examens.map(examen => ({
      ...examen,
      matiere: {
        nom_matiere_fr: examen.nom_matiere_fr,
        nom_matiere_ar: examen.nom_matiere_ar
      },
      classe: {
        nom_classe: examen.nom_classe
      },
      enseignant: examen.nom_enseignant ? {
        nom: examen.nom_enseignant,
        prenom: examen.prenom_enseignant
      } : null,
      nombre_notes: parseInt(examen.nombre_notes) || 0,
      moyenne_classe: examen.moyenne_classe ? parseFloat(examen.moyenne_classe).toFixed(2) : null
    }));

    res.json({
      success: true,
      data: examensFormates,
      message: 'Examens récupérés avec succès'
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des examens:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des examens',
      error: error.message
    });
  }
});

// GET /examens/:id - Récupérer un examen spécifique
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const query = `
      SELECT 
        e.*,
        m.nom_matiere_fr,
        m.nom_matiere_ar,
        c.nom_classe,
        u.nom as nom_enseignant,
        u.prenom as prenom_enseignant
      FROM Examen e
      LEFT JOIN Matiere m ON e.id_matiere = m.id_matiere
      LEFT JOIN Classe c ON e.id_classe = c.id_classe
      LEFT JOIN Enseignant ens ON e.id_enseignant = ens.id_enseignant
      LEFT JOIN User u ON ens.id_user = u.id_user
      WHERE e.id_examen = ?
    `;

    const [examens] = await db.execute(query, [id]);

    if (examens.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Examen non trouvé'
      });
    }

    const examen = examens[0];
    const examenFormate = {
      ...examen,
      matiere: {
        nom_matiere_fr: examen.nom_matiere_fr,
        nom_matiere_ar: examen.nom_matiere_ar
      },
      classe: {
        nom_classe: examen.nom_classe
      },
      enseignant: examen.nom_enseignant ? {
        nom: examen.nom_enseignant,
        prenom: examen.prenom_enseignant
      } : null
    };

    res.json({
      success: true,
      data: examenFormate,
      message: 'Examen récupéré avec succès'
    });

  } catch (error) {
    console.error('Erreur lors de la récupération de l\'examen:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de l\'examen',
      error: error.message
    });
  }
});

// POST /examens - Créer un nouvel examen
router.post('/', requireRole(['admin', 'enseignant']), async (req, res) => {
  try {
    const {
      id_matiere,
      id_classe,
      id_enseignant,
      type_examen,
      date_examen,
      duree_examen,
      semestre,
      commentaire
    } = req.body;

    // Validation des données
    if (!id_matiere || !id_classe || !type_examen || !date_examen || !duree_examen || !semestre) {
      return res.status(400).json({
        success: false,
        message: 'Tous les champs obligatoires doivent être remplis'
      });
    }

    // Vérifier que la matière existe
    const [matieres] = await db.execute('SELECT id_matiere FROM Matiere WHERE id_matiere = ?', [id_matiere]);
    if (matieres.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Matière non trouvée'
      });
    }

    // Vérifier que la classe existe
    const [classes] = await db.execute('SELECT id_classe FROM Classe WHERE id_classe = ?', [id_classe]);
    if (classes.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Classe non trouvée'
      });
    }

    // Vérifier que l'enseignant existe (si spécifié)
    if (id_enseignant) {
      const [enseignants] = await db.execute('SELECT id_enseignant FROM Enseignant WHERE id_enseignant = ?', [id_enseignant]);
      if (enseignants.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Enseignant non trouvé'
        });
      }
    }

    const query = `
      INSERT INTO Examen (
        id_matiere, id_classe, id_enseignant, type_examen, 
        date_examen, duree_examen, semestre, commentaire
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const [result] = await db.execute(query, [
      id_matiere,
      id_classe,
      id_enseignant || null,
      type_examen,
      date_examen,
      duree_examen,
      semestre,
      commentaire || null
    ]);

    res.status(201).json({
      success: true,
      data: { id_examen: result.insertId },
      message: 'Examen créé avec succès'
    });

  } catch (error) {
    console.error('Erreur lors de la création de l\'examen:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la création de l\'examen',
      error: error.message
    });
  }
});

// PUT /examens/:id - Modifier un examen
router.put('/:id', requireRole(['admin', 'enseignant']), async (req, res) => {
  try {
    const { id } = req.params;
    const {
      id_matiere,
      id_classe,
      id_enseignant,
      type_examen,
      date_examen,
      duree_examen,
      semestre,
      commentaire
    } = req.body;

    // Vérifier que l'examen existe
    const [examens] = await db.execute('SELECT id_examen FROM Examen WHERE id_examen = ?', [id]);
    if (examens.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Examen non trouvé'
      });
    }

    const query = `
      UPDATE Examen SET 
        id_matiere = ?, id_classe = ?, id_enseignant = ?, type_examen = ?,
        date_examen = ?, duree_examen = ?, semestre = ?, commentaire = ?
      WHERE id_examen = ?
    `;

    await db.execute(query, [
      id_matiere,
      id_classe,
      id_enseignant || null,
      type_examen,
      date_examen,
      duree_examen,
      semestre,
      commentaire || null,
      id
    ]);

    res.json({
      success: true,
      message: 'Examen modifié avec succès'
    });

  } catch (error) {
    console.error('Erreur lors de la modification de l\'examen:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la modification de l\'examen',
      error: error.message
    });
  }
});

// DELETE /examens/:id - Supprimer un examen
router.delete('/:id', requireRole(['admin']), async (req, res) => {
  try {
    const { id } = req.params;

    // Vérifier que l'examen existe
    const [examens] = await db.execute('SELECT id_examen FROM Examen WHERE id_examen = ?', [id]);
    if (examens.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Examen non trouvé'
      });
    }

    // Supprimer d'abord les notes associées (CASCADE devrait le faire automatiquement)
    await db.execute('DELETE FROM Note WHERE id_examen = ?', [id]);

    // Supprimer l'examen
    await db.execute('DELETE FROM Examen WHERE id_examen = ?', [id]);

    res.json({
      success: true,
      message: 'Examen supprimé avec succès'
    });

  } catch (error) {
    console.error('Erreur lors de la suppression de l\'examen:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression de l\'examen',
      error: error.message
    });
  }
});

module.exports = router;
