<?php

class Eleve
{
    private $pdo;
    private $table = "Eleve";

    public function __construct($db)
    {
        $this->pdo = $db;
    }

   
    public function getAll($id_annee = null ,$id_niveau = null ,$id_classe = null)
    {
        $whereConditions = [];
        $params = [];

        // Condition pour l'année scolaire
        if ($id_annee !== null) {
            $whereConditions[] = "a.id_annee_scolaire = :id_annee_scolaire";
            $params[':id_annee_scolaire'] = $id_annee;
        } else {
            $whereConditions[] = "a.est_active = true";
        }

        // Condition pour la classe
        if ($id_classe) {
            $whereConditions[] = "i.id_classe = :id_classe";
            $params[':id_classe'] = $id_classe;
        }

        // Condition pour le niveau
        if ($id_niveau) {
            $whereConditions[] = "c.id_niveau = :id_niveau";
            $params[':id_niveau'] = $id_niveau;
        }

        $whereClause = implode(' AND ', $whereConditions);

        $query = "SELECT DISTINCT e.*, u.nom, u.prenom, u.email, u.telephone,
            u.date_naissance, u.lieu_naissance, u.sexe, u.nationalite, u.adresse,
            i.id_classe, c.id_niveau, c.nom_classe FROM Eleve e
        JOIN Utilisateur u ON e.id_utilisateur = u.id_utilisateur
        JOIN Inscription i ON e.id_eleve = i.id_eleve
        JOIN Annee_Scolaire a ON i.id_annee_scolaire = a.id_annee_scolaire
        JOIN Classe c ON i.id_classe = c.id_classe
        WHERE $whereClause
        ORDER BY u.nom, u.prenom;";

        $stmt = $this->pdo->prepare($query);

        // Lier tous les paramètres
        foreach ($params as $param => $value) {
            $stmt->bindParam($param, $value, PDO::PARAM_INT);
        }

        $stmt->execute();
        $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);


        return array_map(function ($row) {
            return [
                'id_eleve' => (int) $row['id_eleve'],
                'id_utilisateur' => (int) $row['id_utilisateur'],
                'code_massar' => $row['code_massar'],
                'code_gresa' => $row['code_gresa'],
                'nom_ar' => $row['nom_ar'],
                'prenom_ar' => $row['prenom_ar'],
                'lieu_naissance_ar' => $row['lieu_naissance_ar'],
                'id_classe' => isset($row['id_classe']) ? (int)$row['id_classe'] : null,
                'nom_classe' => isset($row['nom_classe']) ? $row['nom_classe'] : null,
                'id_niveau' => isset($row['id_niveau']) ? (int)$row['id_niveau'] : null,
                'user' => [
                    'id_utilisateur' => (int) $row['id_utilisateur'],
                    'nom' => $row['nom'],
                    'prenom' => $row['prenom'],
                    'email' => $row['email'],
                    'telephone' => $row['telephone'],
                    'date_naissance' => $row['date_naissance'],
                    'lieu_naissance' => $row['lieu_naissance'],
                    'sexe' => $row['sexe'],
                    'nationalite' => $row['nationalite'],
                    'adresse' => $row['adresse']
                ]
                ];
        }, $rows);
    }

    public function getById($id)
    {
        $query = "SELECT e.*, u.nom, u.prenom, u.email, u.telephone, u.date_naissance,
              u.lieu_naissance, u.sexe, u.nationalite, u.adresse, i.id_classe, c.nom_classe
              FROM " . $this->table . " e
              JOIN Utilisateur u ON e.id_utilisateur = u.id_utilisateur
              LEFT JOIN Inscription i ON e.id_eleve = i.id_eleve
              LEFT JOIN Classe c ON i.id_classe = c.id_classe
              WHERE e.id_eleve = :id";
        $stmt = $this->pdo->prepare($query);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$row) {
            return null;
        }

        return [
            'id_eleve' => (int) $row['id_eleve'],
            'id_utilisateur' => (int) $row['id_utilisateur'],
            'code_massar' => $row['code_massar'],
            'code_gresa' => $row['code_gresa'],
            'nom_ar' => $row['nom_ar'],
            'prenom_ar' => $row['prenom_ar'],
            'lieu_naissance_ar' => $row['lieu_naissance_ar'],
            'id_classe' => isset($row['id_classe']) ? (int)$row['id_classe'] : null,
            'nom_classe' => isset($row['nom_classe']) ? $row['nom_classe'] : null,
            'user' => [
                'id_utilisateur' => (int) $row['id_utilisateur'],
                'nom' => $row['nom'],
                'prenom' => $row['prenom'],
                'email' => $row['email'],
                'telephone' => $row['telephone'],
                'date_naissance' => $row['date_naissance'],
                'lieu_naissance' => $row['lieu_naissance'],
                'sexe' => $row['sexe'],
                'nationalite' => $row['nationalite'],
                'adresse' => $row['adresse']
            ]
        ];
    }

    public function create($data)
    {
        try {
            // Nettoyer les données - convertir les chaînes vides en NULL pour les champs optionnels
            $code_massar = !empty($data['code_massar']) ? $data['code_massar'] : null;
            $code_gresa = !empty($data['code_gresa']) ? $data['code_gresa'] : null;

            $query = "INSERT INTO " . $this->table . " (
                    id_utilisateur, code_massar, code_gresa, nom_ar, prenom_ar, lieu_naissance_ar
                ) VALUES (
                    :id_utilisateur, :code_massar, :code_gresa, :nom_ar, :prenom_ar, :lieu_naissance_ar
                )";
            $stmt = $this->pdo->prepare($query);

            $stmt->bindParam(':id_utilisateur', $data['id_utilisateur'], PDO::PARAM_INT);
            $stmt->bindParam(':code_massar', $code_massar, PDO::PARAM_STR);
            $stmt->bindParam(':code_gresa', $code_gresa, PDO::PARAM_STR);
            $stmt->bindParam(':nom_ar', $data['nom_ar'], PDO::PARAM_STR);
            $stmt->bindParam(':prenom_ar', $data['prenom_ar'], PDO::PARAM_STR);
            $stmt->bindParam(':lieu_naissance_ar', $data['lieu_naissance_ar'], PDO::PARAM_STR);

            if ($stmt->execute()) {
                return $this->pdo->lastInsertId();
            } else {
                error_log("Erreur SQL Eleve : " . implode(", ", $stmt->errorInfo()));
                return false;
            }
        } catch (PDOException $e) {
            error_log("Erreur PDO dans Eleve::create : " . $e->getMessage());
            error_log("Données reçues : " . json_encode($data));
            return false;
        }
    }

    public function update($id, $data)
    {
        try {
            // Nettoyer les données - convertir les chaînes vides en NULL pour les champs optionnels
            $code_massar = !empty($data['code_massar']) ? $data['code_massar'] : null;
            $code_gresa = !empty($data['code_gresa']) ? $data['code_gresa'] : null;

            $query = "UPDATE " . $this->table . " SET 
                     code_massar = :code_massar,
                     code_gresa = :code_gresa,
                     nom_ar = :nom_ar,
                     prenom_ar = :prenom_ar,
                     lieu_naissance_ar = :lieu_naissance_ar
                     WHERE id_eleve = :id";
            $stmt = $this->pdo->prepare($query);

            $stmt->bindParam(':code_massar', $code_massar, PDO::PARAM_STR);
            $stmt->bindParam(':code_gresa', $code_gresa, PDO::PARAM_STR);
            $stmt->bindParam(':nom_ar', $data['nom_ar'], PDO::PARAM_STR);
            $stmt->bindParam(':prenom_ar', $data['prenom_ar'], PDO::PARAM_STR);
            $stmt->bindParam(':lieu_naissance_ar', $data['lieu_naissance_ar'], PDO::PARAM_STR);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);

            if ($stmt->execute()) {
                return $stmt->rowCount() >= 0; // >= 0 car même sans changement, c'est un succès
            } else {
                error_log("Erreur SQL Update Eleve : " . implode(", ", $stmt->errorInfo()));
                return false;
            }
        } catch (PDOException $e) {
            error_log("Erreur PDO dans Eleve::update : " . $e->getMessage());
            error_log("Données reçues : " . json_encode($data));
            return false;
        }
    }

    public function delete($id)
    {
        $stmt = $this->pdo->prepare("DELETE FROM " . $this->table . " WHERE id_eleve = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        if ($stmt->rowCount() > 0) {
            return true;
        } else {
            return  false;
        }
    }

    public function getByParent($id_parent, $id_annee = null)
    {
        $query = "SELECT DISTINCT e.*, u.nom, u.prenom, u.email, u.telephone, 
            u.date_naissance, u.lieu_naissance, u.sexe, u.nationalite, u.adresse, 
            i.id_classe, c.id_niveau,c.nom_classe FROM Eleve e
        JOIN Parent_Eleve pe ON e.id_eleve = pe.id_eleve
        JOIN Utilisateur u ON e.id_utilisateur = u.id_utilisateur
        JOIN Inscription i ON e.id_eleve = i.id_eleve
        JOIN Annee_Scolaire a ON i.id_annee_scolaire = a.id_annee_scolaire
        JOIN Classe c ON i.id_classe = c.id_classe
        WHERE pe.id_parent = :id_parent
        " . ($id_annee ? " AND a.id_annee_scolaire = :id_annee_scolaire" : " AND a.est_active = true") . "
        ORDER BY u.nom, u.prenom;";

        $stmt = $this->pdo->prepare($query);
        $stmt->bindParam(':id_parent', $id_parent, PDO::PARAM_INT);
        if ($id_annee) {
            $stmt->bindParam(':id_annee_scolaire', $id_annee, PDO::PARAM_INT);
        }
        
        $stmt->execute();
        $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

        return array_map(function ($row) {
            return [
                'id_eleve' => (int) $row['id_eleve'],
                'id_utilisateur' => (int) $row['id_utilisateur'],
                'code_massar' => $row['code_massar'],
                'code_gresa' => $row['code_gresa'],
                'nom_ar' => $row['nom_ar'],
                'prenom_ar' => $row['prenom_ar'],
                'lieu_naissance_ar' => $row['lieu_naissance_ar'],
                'id_classe' => isset($row['id_classe']) ? (int)$row['id_classe'] : null,
                'nom_classe' => isset($row['nom_classe']) ? $row['nom_classe'] : null,
                'id_niveau' => isset($row['id_niveau']) ? (int)$row['id_niveau'] : null,
                'user' => [
                    'id_utilisateur' => (int) $row['id_utilisateur'],
                    'nom' => $row['nom'],
                    'prenom' => $row['prenom'],
                    'email' => $row['email'],
                    'telephone' => $row['telephone'],
                    'date_naissance' => $row['date_naissance'],
                    'lieu_naissance' => $row['lieu_naissance'],
                    'sexe' => $row['sexe'],
                    'nationalite' => $row['nationalite'],
                    'adresse' => $row['adresse']
                ]
            ];
        }, $rows);
    }

    public function getIdEleve($id_utilisateur) {
        
        $query = "SELECT id_eleve FROM " . $this->table . " WHERE id_utilisateur = :id_utilisateur LIMIT 1";
        $stmt = $this->pdo->prepare($query);
        $stmt->bindParam(':id_utilisateur', $id_utilisateur, PDO::PARAM_INT);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row ? (int)$row['id_eleve'] : null;
    }

    public function getNiveauWithPrices($id_eleve) {
        $query = "SELECT n.id_niveau, n.libelle, n.cycle, n.prix_mensuel, n.frais_inscription
                  FROM " . $this->table . " e
                  JOIN Inscription i ON e.id_eleve = i.id_eleve
                  JOIN Annee_Scolaire a ON i.id_annee_scolaire = a.id_annee_scolaire
                  JOIN Classe c ON i.id_classe = c.id_classe
                  JOIN Niveau n ON c.id_niveau = n.id_niveau
                  WHERE e.id_eleve = :id_eleve
                  AND a.est_active = 1
                  LIMIT 1";

        $stmt = $this->pdo->prepare($query);
        $stmt->bindParam(':id_eleve', $id_eleve, PDO::PARAM_INT);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($row) {
            return [
                'id_niveau' => (int)$row['id_niveau'],
                'libelle' => $row['libelle'],
                'cycle' => $row['cycle'],
                'prix_mensuel' => (float)$row['prix_mensuel'],
                'frais_inscription' => (float)$row['frais_inscription']
            ];
        }

        return null;
    }

    // Récupérer les élèves d'un enseignant spécifique
    public function getByEnseignant($id_enseignant)
    {
        $query = "SELECT DISTINCT e.*, u.nom, u.prenom, u.email, u.telephone,
            u.date_naissance, u.lieu_naissance, u.sexe, u.nationalite, u.adresse,
            i.id_classe, c.id_niveau, c.nom_classe FROM Eleve e
        JOIN Utilisateur u ON e.id_utilisateur = u.id_utilisateur
        JOIN Inscription i ON e.id_eleve = i.id_eleve
        JOIN Annee_Scolaire a ON i.id_annee_scolaire = a.id_annee_scolaire
        JOIN Classe c ON i.id_classe = c.id_classe
        JOIN Cours co ON c.id_classe = co.id_classe
        WHERE co.id_enseignant = :id_enseignant
        AND a.est_active = true
        ORDER BY u.nom, u.prenom;";

        $stmt = $this->pdo->prepare($query);
        $stmt->bindParam(':id_enseignant', $id_enseignant, PDO::PARAM_INT);
        $stmt->execute();
        $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

        return array_map(function ($row) {
            return [
                'id_eleve' => (int) $row['id_eleve'],
                'id_utilisateur' => (int) $row['id_utilisateur'],
                'code_massar' => $row['code_massar'],
                'code_gresa' => $row['code_gresa'],
                'nom_ar' => $row['nom_ar'],
                'prenom_ar' => $row['prenom_ar'],
                'lieu_naissance_ar' => $row['lieu_naissance_ar'],
                'id_classe' => isset($row['id_classe']) ? (int)$row['id_classe'] : null,
                'nom_classe' => isset($row['nom_classe']) ? $row['nom_classe'] : null,
                'id_niveau' => isset($row['id_niveau']) ? (int)$row['id_niveau'] : null,
                'user' => [
                    'id_utilisateur' => (int) $row['id_utilisateur'],
                    'nom' => $row['nom'],
                    'prenom' => $row['prenom'],
                    'email' => $row['email'],
                    'telephone' => $row['telephone'],
                    'date_naissance' => $row['date_naissance'],
                    'lieu_naissance' => $row['lieu_naissance'],
                    'sexe' => $row['sexe'],
                    'nationalite' => $row['nationalite'],
                    'adresse' => $row['adresse']
                ]
            ];
        }, $rows);
    }
}
