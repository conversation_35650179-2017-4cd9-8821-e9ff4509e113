<?php

class Enseignant
{
    private $pdo;
    private $table = "Enseignant";

    public function __construct($db)
    {
        $this->pdo = $db;
    }

    public function getAll()
    {
        $query = "SELECT en.*, u.*, c.statut as contrat_statut FROM Enseignant en
                  JOIN Utilisateur u ON en.id_utilisateur = u.id_utilisateur
                  LEFT JOIN Contrat c ON en.id_enseignant = c.id_enseignant
                  AND c.id_contrat = (SELECT id_contrat FROM Contrat c2
                            WHERE c2.id_enseignant = en.id_enseignant
                            ORDER BY c2.date_debut DESC  LIMIT 1 )";
        $stmt = $this->pdo->prepare($query);
        $stmt->execute();
        $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
        return array_map(function ($row) {
            return [
                'id_enseignant' => (int) $row['id_enseignant'],
                'id_utilisateur' => (int) $row['id_utilisateur'],
                'num_CIN' => $row['num_CIN'],
                'num_CNSS' => $row['num_CNSS'],
                'situation_familiale' => $row['situation_familiale'],
                'nombre_enfants' => $row['nombre_enfants'] !== null ? (int) $row['nombre_enfants'] : null,
                'date_embauche' => $row['date_embauche'],
                'banque' => $row['banque'],
                'rib' => $row['rib'],
                'statut' => $row['contrat_statut'], // Statut depuis le contrat (null si pas de contrat)
                'user' => [
                    'id_utilisateur' => (int) $row['id_utilisateur'],
                    'nom' => $row['nom'],
                    'prenom' => $row['prenom'],
                    'email' => $row['email'],
                    'mot_de_passe' => '',
                    'role' => $row['role'],
                    'sexe' => $row['sexe'],
                    'date_naissance' => $row['date_naissance'],
                    'lieu_naissance' => $row['lieu_naissance'],
                    'nationalite' => $row['nationalite'],
                    'telephone' => $row['telephone'],
                    'adresse' => $row['adresse'] ?? '',
                    'photo' => $row['photo'] ?? '',
                    'est_valide' => (bool) $row['est_valide'],
                    'est_actif' => (bool) $row['est_actif'],
                ]
            ];
        }, $rows);
    }

    public function getById($id)
    {
        $query = "SELECT en.*, u.*, c.statut as contrat_statut FROM Enseignant en
                  JOIN Utilisateur u ON en.id_utilisateur = u.id_utilisateur
                  LEFT JOIN Contrat c ON en.id_enseignant = c.id_enseignant
                  AND c.id_contrat = (SELECT id_contrat FROM Contrat c2
                        WHERE c2.id_enseignant = en.id_enseignant
                        ORDER BY c2.date_debut DESC LIMIT 1)
                  WHERE en.id_enseignant = :id";
        $stmt = $this->pdo->prepare($query);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$row) {
            return null;
        }

        return [
            'id_enseignant' => (int) $row['id_enseignant'],
            'id_utilisateur' => (int) $row['id_utilisateur'],
            'num_CIN' => $row['num_CIN'],
            'num_CNSS' => $row['num_CNSS'],
            'situation_familiale' => $row['situation_familiale'],
            'nombre_enfants' => $row['nombre_enfants'] !== null ? (int) $row['nombre_enfants'] : null,
            'date_embauche' => $row['date_embauche'],
            'banque' => $row['banque'],
            'rib' => $row['rib'],
            'statut' => $row['contrat_statut'], // Statut depuis le contrat (null si pas de contrat)
            'user' => [
                'id_utilisateur' => (int) $row['id_utilisateur'],
                'nom' => $row['nom'],
                'prenom' => $row['prenom'],
                'email' => $row['email'],
                'role' => $row['role'],
                'sexe' => $row['sexe'],
                'date_naissance' => $row['date_naissance'],
                'lieu_naissance' => $row['lieu_naissance'],
                'nationalite' => $row['nationalite'],
                'telephone' => $row['telephone'],
                'adresse' => $row['adresse'] ?? '',
                'photo' => $row['photo'] ?? '',
                'est_valide' => (bool) $row['est_valide'],
                'est_actif' => (bool) $row['est_actif'],
            ]
        ];
    }

    public function getByIdWithRelations($id)
    {
        // Récupérer les données de base de l'enseignant
        $enseignant = $this->getById($id);
        if (!$enseignant) {
            return null;
        }

        // Récupérer les diplômes
        $query = "SELECT * FROM Diplome WHERE id_enseignant = :id_enseignant";
        $stmt = $this->pdo->prepare($query);
        $stmt->bindParam(':id_enseignant', $id, PDO::PARAM_INT);
        $stmt->execute();
        $diplomes = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Récupérer les contrats
        $query = "SELECT * FROM Contrat WHERE id_enseignant = :id_enseignant ORDER BY date_debut DESC";
        $stmt = $this->pdo->prepare($query);
        $stmt->bindParam(':id_enseignant', $id, PDO::PARAM_INT);
        $stmt->execute();
        $contrats = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Ajouter les relations à l'enseignant
        $enseignant['diplomes'] = $diplomes;
        $enseignant['contrats'] = $contrats;

        return $enseignant;
    }

    public function create($data)
    {
        try {
            error_log("Données reçues pour Enseignant : " . json_encode($data));

            // Validation des données requises
            if (!isset($data['id_utilisateur']) || $data['id_utilisateur'] === null || $data['id_utilisateur'] === '') {
                throw new Exception("id_utilisateur est requis, reçu: " . var_export($data['id_utilisateur'], true));
            }
            if (empty($data['num_CIN'])) {
                throw new Exception("num_CIN est requis");
            }
            if (strlen($data['num_CIN']) > 10) {
                throw new Exception("num_CIN ne peut pas dépasser 10 caractères, reçu: " . $data['num_CIN'] . " (" . strlen($data['num_CIN']) . " caractères)");
            }
            if (empty($data['situation_familiale'])) {
                throw new Exception("situation_familiale est requis");
            }
            // Gérer nombre_enfants : null pour célibataire, sinon convertir en int
            if (!isset($data['nombre_enfants'])) {
                $data['nombre_enfants'] = null;
            }
            if (empty($data['date_embauche'])) {
                throw new Exception("date_embauche est requis");
            }

            // Conversion des types
            $data['id_utilisateur'] = (int)$data['id_utilisateur'];
            // Convertir nombre_enfants seulement si ce n'est pas null
            if ($data['nombre_enfants'] !== null) {
                $data['nombre_enfants'] = (int)$data['nombre_enfants'];
            }

            // Gérer les champs optionnels
            if (empty($data['num_CNSS'])) {
                $data['num_CNSS'] = null;
            }
            if (empty($data['banque'])) {
                $data['banque'] = null;
            }
            if (empty($data['rib'])) {
                $data['rib'] = null;
            }

            error_log("Données après validation : " . json_encode($data));

            $query = "INSERT INTO " . $this->table . " (
                    id_utilisateur, num_CIN, num_CNSS, situation_familiale,
                    nombre_enfants, date_embauche, banque, rib
                ) VALUES (
                    :id_utilisateur, :num_CIN, :num_CNSS, :situation_familiale,
                    :nombre_enfants, :date_embauche, :banque, :rib
                )";
            $stmt = $this->pdo->prepare($query);

            $stmt->bindParam(':id_utilisateur', $data['id_utilisateur'], PDO::PARAM_INT);
            $stmt->bindParam(':num_CIN', $data['num_CIN'], PDO::PARAM_STR);
            $stmt->bindParam(':num_CNSS', $data['num_CNSS'], PDO::PARAM_STR);
            $stmt->bindParam(':situation_familiale', $data['situation_familiale'], PDO::PARAM_STR);
            $stmt->bindParam(':nombre_enfants', $data['nombre_enfants'], $data['nombre_enfants'] !== null ? PDO::PARAM_INT : PDO::PARAM_NULL);
            $stmt->bindParam(':date_embauche', $data['date_embauche'], PDO::PARAM_STR);
            $stmt->bindParam(':banque', $data['banque'], PDO::PARAM_STR);
            $stmt->bindParam(':rib', $data['rib'], PDO::PARAM_STR);

            if ($stmt->execute()) {
                error_log("Enseignant créé avec succès, ID: " . $this->pdo->lastInsertId());
                return $this->pdo->lastInsertId();
            } else {
                $errorInfo = $stmt->errorInfo();
                error_log("Erreur SQL Enseignant : " . implode(", ", $errorInfo));
                throw new Exception("Erreur SQL: " . $errorInfo[2]);
            }
        } catch (Exception $e) {
            error_log("Exception dans Enseignant::create: " . $e->getMessage());
            throw $e;
        }
    }

    public function update($id, $data)
    {
        try {
            error_log("Mise à jour enseignant $id avec données : " . json_encode($data));

            // Validation des données requises
            if (empty($data['num_CIN'])) {
                throw new Exception("num_CIN est requis");
            }
            if (strlen($data['num_CIN']) > 10) {
                throw new Exception("num_CIN ne peut pas dépasser 10 caractères");
            }
            if (empty($data['situation_familiale'])) {
                throw new Exception("situation_familiale est requis");
            }
            // Gérer nombre_enfants : null pour célibataire, sinon convertir en int
            if (!isset($data['nombre_enfants'])) {
                $data['nombre_enfants'] = null;
            }
            if (empty($data['date_embauche'])) {
                throw new Exception("date_embauche est requis");
            }

            // Conversion des types
            // Convertir nombre_enfants seulement si ce n'est pas null
            if ($data['nombre_enfants'] !== null) {
                $data['nombre_enfants'] = (int)$data['nombre_enfants'];
            }

            // Gérer les champs optionnels
            if (empty($data['num_CNSS'])) {
                $data['num_CNSS'] = null;
            }
            if (empty($data['banque'])) {
                $data['banque'] = null;
            }
            if (empty($data['rib'])) {
                $data['rib'] = null;
            }

            // Ne pas mettre à jour id_utilisateur lors d'une modification
            $query = "UPDATE " . $this->table . " SET
                    num_CIN = :num_CIN,
                    num_CNSS = :num_CNSS,
                    situation_familiale = :situation_familiale,
                    nombre_enfants = :nombre_enfants,
                    date_embauche = :date_embauche,
                    banque = :banque,
                    rib = :rib
                WHERE id_enseignant = :id_enseignant";
            $stmt = $this->pdo->prepare($query);

            $stmt->bindParam(':num_CIN', $data['num_CIN'], PDO::PARAM_STR);
            $stmt->bindParam(':num_CNSS', $data['num_CNSS'], PDO::PARAM_STR);
            $stmt->bindParam(':situation_familiale', $data['situation_familiale'], PDO::PARAM_STR);
            $stmt->bindParam(':nombre_enfants', $data['nombre_enfants'], $data['nombre_enfants'] !== null ? PDO::PARAM_INT : PDO::PARAM_NULL);
            $stmt->bindParam(':date_embauche', $data['date_embauche'], PDO::PARAM_STR);
            $stmt->bindParam(':banque', $data['banque'], PDO::PARAM_STR);
            $stmt->bindParam(':rib', $data['rib'], PDO::PARAM_STR);
            $stmt->bindParam(':id_enseignant', $id, PDO::PARAM_INT);

            error_log("Données après validation : " . json_encode($data));

            if ($stmt->execute()) {
                $rowCount = $stmt->rowCount();
                error_log("Enseignant mis à jour avec succès, lignes affectées: $rowCount");
                return true; // Retourner true si la requête s'exécute, même si aucune ligne modifiée
            } else {
                $errorInfo = $stmt->errorInfo();
                error_log("Erreur SQL Update Enseignant : " . implode(", ", $errorInfo));
                throw new Exception("Erreur SQL: " . $errorInfo[2]);
            }
        } catch (Exception $e) {
            error_log("Exception dans Enseignant::update: " . $e->getMessage());
            throw $e;
        }
    }

    public function delete($id)
    {
        $query = "DELETE FROM " . $this->table . " WHERE id_enseignant = :id";
        $stmt = $this->pdo->prepare($query);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);

        if ($stmt->execute()) {
            return $stmt->rowCount() > 0;
        } else {
            error_log("Erreur SQL Delete Enseignant : " . implode(", ", $stmt->errorInfo()));
            return false;
        }
    }

    public function getAllWithRelations()
    {
        // Récupérer tous les enseignants
        $enseignants = $this->getAll();

        // Pour chaque enseignant, récupérer ses vraies matières et classes
        foreach ($enseignants as &$enseignant) {
            // Récupérer les matières réellement affectées à cet enseignant
            $query = "SELECT m.id_matiere, m.nom_matiere_fr, m.nom_matiere_ar, m.description
                      FROM Enseignant_Matiere em
                      JOIN Matiere m ON em.id_matiere = m.id_matiere
                      WHERE em.id_enseignant = :id_enseignant";
            $stmt = $this->pdo->prepare($query);
            $stmt->bindParam(':id_enseignant', $enseignant['id_enseignant'], PDO::PARAM_INT);
            $stmt->execute();
            $enseignant['matieres'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Récupérer uniquement les classes où cet enseignant enseigne réellement (via les cours)
            $query = "SELECT DISTINCT c.id_classe, c.nom_classe, c.id_niveau FROM Cours co
                      JOIN Classe c ON co.id_classe = c.id_classe
                      WHERE co.id_enseignant = :id_enseignant
                      ORDER BY c.nom_classe";
            $stmt = $this->pdo->prepare($query);
            $stmt->bindParam(':id_enseignant', $enseignant['id_enseignant'], PDO::PARAM_INT);
            $stmt->execute();
            $enseignant['classes'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }

        return $enseignants;
    }

    /** Récupérer les élèves d'un enseignant (via les classes qu'il enseigne)  */
    public function getElevesEnseignant($id_enseignant)
    {
        $query = "SELECT DISTINCT e.*, u.nom, u.prenom, u.email, u.telephone, u.date_naissance,
                  u.lieu_naissance, u.sexe, u.nationalite, u.adresse, c.nom_classe
                  FROM Eleve e
                  JOIN Utilisateur u ON e.id_utilisateur = u.id_utilisateur
                  JOIN Inscription i ON e.id_eleve = i.id_eleve
                  JOIN Classe c ON i.id_classe = c.id_classe
                  JOIN Cours co ON c.id_classe = co.id_classe
                  JOIN Annee_Scolaire a ON i.id_annee_scolaire = a.id_annee_scolaire
                  WHERE co.id_enseignant = :id_enseignant
                  AND a.est_active = 1
                  ORDER BY u.nom, u.prenom";

        $stmt = $this->pdo->prepare($query);
        $stmt->bindParam(':id_enseignant', $id_enseignant, PDO::PARAM_INT);
        $stmt->execute();

        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Restructurer les données pour correspondre au format attendu par le frontend
        $eleves = [];
        foreach ($results as $row) {
            $eleves[] = [
                'id_eleve' => (int) $row['id_eleve'],
                'id_utilisateur' => (int) $row['id_utilisateur'],
                'nom_ar' => $row['nom_ar'],
                'prenom_ar' => $row['prenom_ar'],
                'code_massar' => $row['code_massar'],
                'nom_classe' => $row['nom_classe'],
                'user' => [
                    'id_utilisateur' => (int) $row['id_utilisateur'],
                    'nom' => $row['nom'],
                    'prenom' => $row['prenom'],
                    'email' => $row['email'],
                    'telephone' => $row['telephone'],
                    'date_naissance' => $row['date_naissance'],
                    'lieu_naissance' => $row['lieu_naissance'],
                    'sexe' => $row['sexe'],
                    'nationalite' => $row['nationalite'],
                    'adresse' => $row['adresse']
                ]
            ];
        }

        return $eleves;
    }

    /** Récupérer les classes d'un enseignant (via les cours qu'il enseigne)*/
    public function getClassesEnseignant($id_enseignant)
    {
        $query = "SELECT DISTINCT c.id_classe, c.nom_classe, c.id_niveau, n.libelle as niveau_libelle, n.cycle,
                  COUNT(DISTINCT i.id_eleve) as nombre_eleves
                  FROM Classe c
                  JOIN Cours co ON c.id_classe = co.id_classe
                  JOIN Niveau n ON c.id_niveau = n.id_niveau
                  LEFT JOIN Inscription i ON c.id_classe = i.id_classe
                  LEFT JOIN Annee_Scolaire a ON i.id_annee_scolaire = a.id_annee_scolaire
                  WHERE co.id_enseignant = :id_enseignant
                  AND (a.est_active = 1 OR a.est_active IS NULL)
                  GROUP BY c.id_classe, c.nom_classe, c.id_niveau, n.libelle, n.cycle
                  ORDER BY n.cycle, c.nom_classe";

        $stmt = $this->pdo->prepare($query);
        $stmt->bindParam(':id_enseignant', $id_enseignant, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**Récupérer les cours d'un enseignant pour aujourd'hui  */
    public function getCoursAujourdhui($id_enseignant)
    {
        // Récupérer le jour de la semaine en français
        $joursMap = [
            'Monday' => 'Lundi',
            'Tuesday' => 'Mardi',
            'Wednesday' => 'Mercredi',
            'Thursday' => 'Jeudi',
            'Friday' => 'Vendredi',
            'Saturday' => 'Samedi',
            'Sunday' => 'Dimanche'
        ];

        $jourAujourdhui = $joursMap[date('l')];

        $query = "SELECT c.*, u.nom_unite, m.nom_matiere_fr, cl.nom_classe, s.nom_salle
                  FROM Cours c
                  JOIN Unite u ON c.id_unite = u.id_unite
                  JOIN Matiere m ON u.id_matiere = m.id_matiere
                  JOIN Classe cl ON c.id_classe = cl.id_classe
                  JOIN Salle s ON c.id_salle = s.id_salle
                  WHERE c.id_enseignant = :id_enseignant
                  AND c.jour_semaine = :jour_semaine
                  ORDER BY c.heure_debut";

        $stmt = $this->pdo->prepare($query);
        $stmt->bindParam(':id_enseignant', $id_enseignant, PDO::PARAM_INT);
        $stmt->bindParam(':jour_semaine', $jourAujourdhui, PDO::PARAM_STR);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
