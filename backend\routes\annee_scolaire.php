<?php

require_once __DIR__ . '/../controllers/AnneeScolaireController.php';

function handleAnneeScolaireRoutes($uri, $method, $pdo) {
    $anneeScolaireController = new AnneeScolaireController($pdo);

    // GET /annees-scolaires - Récupérer toutes les années scolaires
    if ($uri === '/annees-scolaires' && $method === 'GET') {
        $anneeScolaireController->getAll();
        return true;
    }

    // GET /annees-scolaires/active - Récupérer l'année scolaire active
    if ($uri === '/annees-scolaires/active' && $method === 'GET') {
        $anneeScolaireController->getActive();
        return true;
    }

    // GET /annees-scolaires/statistics - Récupérer les statistiques des années scolaires
    if ($uri === '/annees-scolaires/statistics' && $method === 'GET') {
        $anneeScolaireController->getStatistics();
        return true;
    }

    // POST /annees-scolaires/validate-dates - Valider les dates d'une année scolaire
    if ($uri === '/annees-scolaires/validate-dates' && $method === 'POST') {
        $anneeScolaireController->validateDates();
        return true;
    }

    // GET /annees-scolaires/{id} - Récupérer une année scolaire par ID
    if (preg_match('/^\/annees-scolaires\/(\d+)$/', $uri, $matches) && $method === 'GET') {
        $id = (int)$matches[1];
        $anneeScolaireController->getById($id);
        return true;
    }

    // POST /annees-scolaires - Créer une nouvelle année scolaire
    if ($uri === '/annees-scolaires' && $method === 'POST') {
        $anneeScolaireController->create();
        return true;
    }

    // PUT /annees-scolaires/{id} - Mettre à jour une année scolaire
    if (preg_match('/^\/annees-scolaires\/(\d+)$/', $uri, $matches) && $method === 'PUT') {
        $id = (int)$matches[1];
        $anneeScolaireController->update($id);
        return true;
    }

    // PATCH /annees-scolaires/{id}/toggle-active - Activer/désactiver une année scolaire
    if (preg_match('/^\/annees-scolaires\/(\d+)\/toggle-active$/', $uri, $matches) && $method === 'PATCH') {
        $id = (int)$matches[1];
        $anneeScolaireController->toggleActive($id);
        return true;
    }

    // DELETE /annees-scolaires/{id} - Supprimer une année scolaire
    if (preg_match('/^\/annees-scolaires\/(\d+)$/', $uri, $matches) && $method === 'DELETE') {
        $id = (int)$matches[1];
        $anneeScolaireController->delete($id);
        return true;
    }

    return false;
}
