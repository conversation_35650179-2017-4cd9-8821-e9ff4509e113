<?php

require_once __DIR__ . '/../models/Diplome.php';

class DiplomeController {
    private $diplome;

    public function __construct($db) {
        $this->diplome = new Diplome($db);
    }

    public function getDiplomes() {
        try {
            $diplomes = $this->diplome->getAll();
            echo json_encode(['success' => true, 'data' => $diplomes]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur lors de la récupération des diplômes']);
        }
    }

    public function getDiplome($id) {
        try {
            $diplome = $this->diplome->getById($id);
            if ($diplome) {
                echo json_encode(['success' => true, 'data' => $diplome]);
            } else {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Diplôme non trouvé']);
            }
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur lors de la récupération du diplôme']);
        }
    }

    public function getDiplomesByEnseignant($id_enseignant) {
        try {
            $diplomes = $this->diplome->getByEnseignantId($id_enseignant);
            echo json_encode(['success' => true, 'data' => $diplomes]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur lors de la récupération des diplômes']);
        }
    }

    public function addDiplome() {
        try {
            $data = json_decode(file_get_contents("php://input"), true);
            
            if (!$data) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Aucune donnée reçue']);
                return;
            }

            $id_diplome = $this->diplome->create($data);
            if ($id_diplome) {
                http_response_code(201);
                echo json_encode([
                    'success' => true, 
                    'id_diplome' => $id_diplome,
                    'message' => 'Diplôme créé avec succès'
                ]);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Erreur lors de la création du diplôme']);
            }
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur']);
        }
    }

    public function updateDiplome($id) {
        try {
            $data = json_decode(file_get_contents("php://input"), true);
            error_log("Mise à jour diplôme $id avec données : " . json_encode($data));

            if (!$data) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Aucune donnée reçue']);
                return;
            }

            $result = $this->diplome->update($id, $data);
            // Succès si aucune exception n'est levée
            echo json_encode(['success' => true, 'message' => 'Diplôme mis à jour avec succès']);
        } catch (Exception $e) {
            error_log("Exception dans updateDiplome: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    public function deleteDiplome($id) {
        try {
            if ($this->diplome->delete($id)) {
                echo json_encode(['success' => true, 'message' => 'Diplôme supprimé avec succès']);
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Erreur lors de la suppression du diplôme']);
            }
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur']);
        }
    }
}
