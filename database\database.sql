-- Création de la base de données
CREATE DATABASE IF NOT EXISTS ScolaNova;
USE ScolaNova;

-- Table Utilisateur (entité centrale)
CREATE TABLE Utilisateur (
    id_utilisateur INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(50) NOT NULL,
    prenom VARCHAR(50) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    mot_de_passe VARCHAR(255) NOT NULL, -- Chi<PERSON>ré 
    role ENUM('admin', 'enseignant', 'eleve', 'parent', 'personnel') NOT NULL,
    sexe ENUM('garçon','fille','homme', 'femme') NOT NULL,
    date_naissance DATE,
    lieu_naissance VARCHAR(100),
    nationalite VARCHAR(50) DEFAULT 'marocain',
    telephone VARCHAR(15),
    adresse TEXT,
    photo VARCHAR(255), -- Chemin vers la photo de profil
    est_valide BOOLEAN DEFAULT FALSE,
    est_actif BOOLEAN DEFAULT TRUE,
    date_creation DATETIME DEFAULT CURRENT_TIMESTAMP,
    date_maj DATETIME ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email), -- Index pour recherches fréquentes par email
    INDEX idx_nom_prenom (nom, prenom) -- Index pour recherches par nom/prénom
);

-- Table Parent 
CREATE TABLE Parent (
    id_parent INT AUTO_INCREMENT PRIMARY KEY,
    id_utilisateur INT UNIQUE NOT NULL,
    nom_ar VARCHAR(50) NOT NULL,
    prenom_ar VARCHAR(50) NOT NULL,
    num_CIN CHAR(10) NOT NULL UNIQUE,
    FOREIGN KEY (id_utilisateur) REFERENCES Utilisateur(id_utilisateur) ON DELETE CASCADE
);
-- Table Ancienne_Ecole
CREATE TABLE Ancienne_Ecole(
    code_gresa VARCHAR(10) PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    type ENUM('publique', 'privée') NOT NULL,
    cycle ENUM('maternelle', 'primaire', 'collège', 'lycée') NOT NULL,
    adresse TEXT 
);
-- Table Élève
CREATE TABLE Eleve (
    id_eleve INT AUTO_INCREMENT PRIMARY KEY,
    id_utilisateur INT UNIQUE NOT NULL,
    code_massar VARCHAR(10) UNIQUE,
    code_gresa CHAR(10) UNIQUE,
    nom_ar VARCHAR(50) NOT NULL,
    prenom_ar VARCHAR(50) NOT NULL,
    lieu_naissance_ar VARCHAR(100) NOT NULL,
    FOREIGN KEY (id_utilisateur) REFERENCES Utilisateur(id_utilisateur) ON DELETE CASCADE,
    FOREIGN KEY (code_gresa) REFERENCES Ancienne_Ecole(code_gresa) ON DELETE SET NULL
);

-- Table Relation Parent-Élève
CREATE TABLE Relation_Parent_Eleve (
    id_parent INT NOT NULL,
    id_eleve INT NOT NULL,
    type_relation ENUM('père', 'mère', 'tuteur') NOT NULL,
    PRIMARY KEY (id_parent, id_eleve),
    FOREIGN KEY (id_parent) REFERENCES Parent(id_parent) ON DELETE CASCADE,
    FOREIGN KEY (id_eleve) REFERENCES Eleve(id_eleve) ON DELETE CASCADE
);

-- Table Enseignant 
CREATE TABLE Enseignant (
    id_enseignant INT AUTO_INCREMENT PRIMARY KEY,
    id_utilisateur INT UNIQUE NOT NULL,
    num_CIN CHAR(10) NOT NULL UNIQUE,
    num_CNSS CHAR(10) UNIQUE,
    situation_familiale ENUM('C', 'M', 'D', 'V') NOT NULL,
    nombre_enfants INT DEFAULT 0 CHECK (nombre_enfants >= 0),
    date_embauche DATE NOT NULL,
    banque VARCHAR(50),
    rib CHAR(24),
    FOREIGN KEY (id_utilisateur) REFERENCES Utilisateur(id_utilisateur) ON DELETE CASCADE
);

-- Table Annee_Scolaire
CREATE TABLE Annee_Scolaire (
    id_annee_scolaire INT AUTO_INCREMENT PRIMARY KEY,
    libelle VARCHAR(9) NOT NULL UNIQUE, -- Ex. : "2024-2025"
    date_debut DATE,
    date_fin DATE CHECK (date_fin > date_debut),
    est_active BOOLEAN DEFAULT FALSE,
    INDEX idx_libelle (libelle) -- Index pour recherches par libellé
);

-- Table Niveau
CREATE TABLE Niveau (
    id_niveau INT AUTO_INCREMENT PRIMARY KEY,
    cycle ENUM('maternelle', 'primaire', 'collège', 'lycée') NOT NULL,
    libelle VARCHAR(50) NOT NULL, -- Ex. : "Petite Section", "6ème", "1ère"
    prix_mensuel DECIMAL(6,2) NOT NULL, 
    frais_inscription DECIMAL(6,2) NOT NULL,
    UNIQUE (cycle, libelle)
);

-- Table Classe 
CREATE TABLE Classe (
    id_classe INT AUTO_INCREMENT PRIMARY KEY,
    id_niveau INT NOT NULL,
    nom_classe VARCHAR(20) NOT NULL, -- Ex. : "6ème A", "1ère S1"
    FOREIGN KEY (id_niveau) REFERENCES Niveau(id_niveau) ON DELETE RESTRICT,
    UNIQUE (id_annee_scolaire, id_niveau, nom_classe) -- (id_annee_scolaire a voir)
);

-- Table Inscription
CREATE TABLE Inscription (
    id_eleve INT NOT NULL,
    id_annee_scolaire INT NOT NULL,
    id_classe INT NOT NULL,
    date_inscription TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    statut VARCHAR(50) DEFAULT 'Inscrit',  -- ou 'Transféré', 'Abandonné'
    PRIMARY KEY (id_eleve, id_annee_scolaire, id_classe),
    FOREIGN KEY (id_classe) REFERENCES Classe(id_classe) ON DELETE CASCADE,
    FOREIGN KEY (id_eleve) REFERENCES Eleve(id_eleve) ON DELETE CASCADE,
    FOREIGN KEY (id_annee_scolaire) REFERENCES Annee_Scolaire(id_annee_scolaire) ON DELETE CASCADE
);

-- Table Matiere
CREATE TABLE Matiere (
    id_matiere INT AUTO_INCREMENT PRIMARY KEY,
    nom_matiere_fr VARCHAR(50) NOT NULL UNIQUE,
    nom_matiere_ar VARCHAR(50) NOT NULL UNIQUE,
    description TEXT
);

-- Table Unite
CREATE TABLE Unite (
    id_unite INT AUTO_INCREMENT PRIMARY KEY,
    id_matiere INT NOT NULL,
    nom_unite VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    FOREIGN KEY (id_matiere) REFERENCES Matiere(id_matiere) ON DELETE CASCADE
);

-- Table Niveau_Matiere
CREATE TABLE Niveau_Matiere (
    id_niveau INT NOT NULL,
    id_matiere INT NOT NULL,
    coefficient INT NOT NULL DEFAULT 1 CHECK (coefficient > 0),
    Nb_heures INT CHECK (nb_heures >= 0),
    PRIMARY KEY (id_niveau, id_matiere),
    FOREIGN KEY (id_niveau) REFERENCES Niveau(id_niveau) ON DELETE CASCADE,
    FOREIGN KEY (id_matiere) REFERENCES Matiere(id_matiere) ON DELETE CASCADE
);

-- Table Salle
CREATE TABLE Salle (
    id_salle INT AUTO_INCREMENT PRIMARY KEY,
    nom_salle VARCHAR(50) NOT NULL UNIQUE,
    capacite INT NOT NULL CHECK (capacite > 0)
);

-- Table Cours 
CREATE TABLE Cours (
    id_cours INT AUTO_INCREMENT PRIMARY KEY,
    id_unite INT NOT NULL,
    id_enseignant INT NOT NULL,
    id_classe INT NOT NULL,
    id_salle INT NOT NULL,
    jour_semaine ENUM('Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi') NOT NULL,
    -- date_cours DATE NOT NULL,
    heure_debut TIME NOT NULL,
    heure_fin TIME NOT NULL CHECK (heure_fin > heure_debut),
    FOREIGN KEY (id_unite) REFERENCES Unite(id_unite) ON DELETE RESTRICT,
    FOREIGN KEY (id_enseignant) REFERENCES Enseignant(id_enseignant) ON DELETE RESTRICT,
    FOREIGN KEY (id_classe) REFERENCES Classe(id_classe) ON DELETE RESTRICT,
    FOREIGN KEY (id_salle) REFERENCES Salle(id_salle) ON DELETE RESTRICT,
    UNIQUE (id_enseignant, id_classe, jour_semaine, heure_debut, heure_fin) 
);
-- Le champ date_cours est commenté. Si vous souhaitez gérer des cours planifiés à des dates spécifiques,
--  vous pourriez le réactiver. Sinon, la structure actuelle (basée sur jour_semaine, heure_debut, heure_fin) convient pour un emploi du temps hebdomadaire récurrent.

-- Ajouter une contrainte pour éviter les conflits horaires pour une salle ou un enseignant pourrait être utile. Par exemple, une contrainte ou un trigger pour vérifier qu’un enseignant ou une salle n’est pas réservé deux fois au même moment.


-- ************************* Gestion des examens et resultats *************************

-- Table Examen 
CREATE TABLE Examen (
    id_examen INT AUTO_INCREMENT PRIMARY KEY,
    id_matiere INT NOT NULL,
    id_classe INT NOT NULL,
    id_enseignant INT,
    type_examen ENUM('examen', 'devoir', 'contrôle', 'participation') NOT NULL,
    date_examen DATE NOT NULL,
    duree_examen INT NOT NULL CHECK (duree_examen > 0), -- En minutes 
    semestre ENUM('S1', 'S2') NOT NULL,
    commentaire TEXT,
    FOREIGN KEY (id_matiere) REFERENCES Matiere(id_matiere) ON DELETE RESTRICT,
    FOREIGN KEY (id_classe) REFERENCES Classe(id_classe) ON DELETE RESTRICT,
    FOREIGN KEY (id_enseignant) REFERENCES Enseignant(id_enseignant) ON DELETE SET NULL,
    INDEX idx_date_examen (date_examen)
);

CREATE TABLE Note (
    id_eleve INT NOT NULL,
    id_examen INT NOT NULL,
    note DECIMAL(4,2) NOT NULL CHECK (note >= 0 AND note <= 20),
    PRIMARY KEY (id_eleve, id_examen),
    FOREIGN KEY (id_eleve) REFERENCES Eleve(id_eleve) ON DELETE CASCADE,
    FOREIGN KEY (id_examen) REFERENCES Examen(id_examen) ON DELETE CASCADE
);

CREATE TABLE Bulletin (
    id_bulletin INT AUTO_INCREMENT PRIMARY KEY,
    id_eleve INT NOT NULL,
    id_annee_scolaire INT NOT NULL,
    semestre ENUM('S1', 'S2') NOT NULL,
    moyenne_generale DECIMAL(4,2) NOT NULL CHECK (moyenne_generale >= 0 AND moyenne_generale <= 20),
    -- pourrait être calculée dynamiquement à partir des notes dans Bulletin_Matiere
    appreciation_generale TEXT,
    date_creation DATETIME DEFAULT CURRENT_TIMESTAMP,
    date_maj DATETIME ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (id_eleve) REFERENCES Eleve(id_eleve) ON DELETE CASCADE,
    FOREIGN KEY (id_annee_scolaire) REFERENCES Annee_Scolaire(id_annee_scolaire) ON DELETE CASCADE,
    UNIQUE (id_eleve, id_annee_scolaire, semestre)
);

CREATE TABLE Bulletin_Matiere (
    id_bulletin INT NOT NULL,
    id_matiere INT NOT NULL,
    moyenne_matiere DECIMAL(4,2) NOT NULL CHECK (moyenne_matiere >= 0 AND moyenne_matiere <= 20),
    appreciation TEXT,
    PRIMARY KEY (id_bulletin, id_matiere),
    FOREIGN KEY (id_bulletin) REFERENCES Bulletin(id_bulletin) ON DELETE CASCADE,
    FOREIGN KEY (id_matiere) REFERENCES Matiere(id_matiere) ON DELETE RESTRICT
);

-- ************************* Absence, Paiement, Transport et Activites *************************
-- Table Absence 
CREATE TABLE Absence_Eleve (
    id_absence_eleve INT AUTO_INCREMENT PRIMARY KEY,
    id_eleve INT NOT NULL,
    id_classe INT NOT NULL,
    date_debut DATETIME NOT NULL,
    date_fin DATETIME NOT NULL CHECK (date_fin >= date_debut),
    duree INT GENERATED ALWAYS AS (TIMESTAMPDIFF(MINUTE, date_debut, date_fin)) STORED,
    motif ENUM('maladie', 'familial', 'autre') NOT NULL,
    justifiee BOOLEAN DEFAULT FALSE,
    commentaire TEXT,
    FOREIGN KEY (id_eleve) REFERENCES Eleve(id_eleve) ON DELETE CASCADE,
    FOREIGN KEY (id_classe) REFERENCES Classe(id_classe) ON DELETE CASCADE,
    UNIQUE (id_eleve, date_debut), -- Un étudiant ne peut pas avoir deux absences le même jour;
    INDEX idx_date_debut (date_debut) -- Index pour recherches par date
);

-- Table Paiement
CREATE TABLE Paiement (
    id_paiement INT AUTO_INCREMENT PRIMARY KEY,
    id_eleve INT NOT NULL,
    id_annee_scolaire INT NOT NULL,
    montant DECIMAL(6,2) NOT NULL CHECK (montant >= 0),
    -- periode VARCHAR(20) NOT NULL, -- Ex. : "Septembre 2024"
    mois ENUM('Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', 'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre') NOT NULL,
    -- annee INT NOT NULL,
    date_paiement DATETIME DEFAULT CURRENT_TIMESTAMP,
    date_echeance DATE,
    type_paiement ENUM('scolarité', 'transport', 'cantine', "inscription", 'autre') NOT NULL,
    mode_paiement ENUM('espèces', 'chèque', 'virement', 'carte') NOT NULL,
    statut ENUM('en attente', 'payé', 'retard', 'annule') DEFAULT 'en attente',
    description TEXT,
    FOREIGN KEY (id_eleve) REFERENCES Eleve(id_eleve) ON DELETE CASCADE,
    FOREIGN KEY (id_annee_scolaire) REFERENCES Annee_Scolaire(id_annee_scolaire) ON DELETE CASCADE,
    UNIQUE (id_eleve, id_annee_scolaire, type_paiement, periode), 
    INDEX idx_date_paiement (date_paiement) -- Index pour recherches par date
);

-- Table Transport
CREATE TABLE Transport (
    id_transport INT AUTO_INCREMENT PRIMARY KEY,
    prix DECIMAL(6,2) NOT NULL CHECK (prix >= 0),
    trajet VARCHAR(100) NOT NULL,
    matricule VARCHAR(50) NOT NULL UNIQUE,
    capacite INT DEFAULT 25 CHECK (capacite > 0)
);

-- Table Beneficier_Transport 
CREATE TABLE Beneficier_Transport (
    id_eleve INT NOT NULL,
    id_transport INT NOT NULL,
    id_annee_scolaire INT NOT NULL,
    PRIMARY KEY (id_eleve, id_transport, id_annee_scolaire),
    FOREIGN KEY (id_eleve) REFERENCES Eleve(id_eleve) ON DELETE CASCADE,
    FOREIGN KEY (id_transport) REFERENCES Transport(id_transport) ON DELETE CASCADE,
    FOREIGN KEY (id_annee_scolaire) REFERENCES Annee_Scolaire(id_annee_scolaire) ON DELETE CASCADE
);

CREATE TABLE Activite (
    id_activite INT AUTO_INCREMENT PRIMARY KEY,
    id_annee_scolaire INT NOT NULL,
    nom_activite VARCHAR(50) NOT NULL, -- Ex. : "Sortie musée"
    type_activite ENUM('sportive', 'culturelle', 'autre') NOT NULL,
    date_debut DATE,
    date_fin DATE CHECK (date_fin >= date_debut),
    prix DECIMAL(6,2) NOT NULL CHECK (prix >= 0),
    description TEXT,
    FOREIGN KEY (id_annee_scolaire) REFERENCES Annee_Scolaire(id_annee_scolaire) ON DELETE CASCADE
);

CREATE TABLE Participation_Activite (
    id_eleve INT NOT NULL,
    id_activite INT NOT NULL,
    PRIMARY KEY (id_eleve, id_activite),
    FOREIGN KEY (id_eleve) REFERENCES Eleve(id_eleve) ON DELETE CASCADE,
    FOREIGN KEY (id_activite) REFERENCES Activite(id_activite) ON DELETE CASCADE
);

CREATE TABLE Classe_Activite (
    id_classe INT NOT NULL,
    id_activite INT NOT NULL,
    -- date_participation DATE, 
    PRIMARY KEY (id_classe, id_activite),
    FOREIGN KEY (id_classe) REFERENCES Classe(id_classe) ON DELETE CASCADE,
    FOREIGN KEY (id_activite) REFERENCES Activite(id_activite) ON DELETE CASCADE
);

-- Table Message
CREATE TABLE Message(
    id_message INT AUTO_INCREMENT PRIMARY KEY,
    id_emetteur INT NOT NULL,
    id_recepteur INT NOT NULL,
    objet VARCHAR(100) NOT NULL,
    contenu TEXT NOT NULL,
    date_envoi DATETIME DEFAULT CURRENT_TIMESTAMP,
    lu BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (id_emetteur) REFERENCES Utilisateur(id_utilisateur) ON DELETE CASCADE,
    FOREIGN KEY (id_recepteur) REFERENCES Utilisateur(id_utilisateur) ON DELETE CASCADE,
    CHECK (id_emetteur <> id_recepteur)
);
--******************** Gestion Enseignant *********************
-- Table Enseignant_Activite 
CREATE TABLE Enseignant_Activite (
    id_enseignant INT NOT NULL,
    id_activite INT NOT NULL,
    PRIMARY KEY (id_enseignant, id_activite),
    FOREIGN KEY (id_enseignant) REFERENCES Enseignant(id_enseignant) ON DELETE CASCADE,
    FOREIGN KEY (id_activite) REFERENCES Activite(id_activite) ON DELETE CASCADE
);

-- Table Enseignant_Matiere
CREATE TABLE Enseignant_Matiere (
    id_enseignant INT NOT NULL,
    id_matiere INT NOT NULL,
    PRIMARY KEY (id_enseignant, id_matiere),
    FOREIGN KEY (id_enseignant) REFERENCES Enseignant(id_enseignant) ON DELETE CASCADE,
    FOREIGN KEY (id_matiere) REFERENCES Matiere(id_matiere) ON DELETE CASCADE
);

-- Table Absence_Enseignant
CREATE TABLE Absence_Enseignant (
    id_absence_enseignant INT AUTO_INCREMENT PRIMARY KEY,
    id_enseignant INT NOT NULL,
    date_debut DATE NOT NULL,
    date_fin DATE NOT NULL,
    type ENUM('absence', 'retard') NOT NULL,
    justifiee BOOLEAN DEFAULT FALSE,
    motif ENUM('maladie', 'congé personnel', 'formation', 'autre') NOT NULL,
    commentaire TEXT,
    -- piece_jointe VARCHAR(255), -- Pour un certificat médical ou autre
    -- id_remplacant INT DEFAULT NULL, -- Référence à un autre enseignant ou suppléant
    FOREIGN KEY (id_enseignant) REFERENCES Enseignant(id_enseignant) ON DELETE CASCADE,
    FOREIGN KEY (id_remplacant) REFERENCES Enseignant(id_enseignant) ON DELETE SET NULL,
    UNIQUE (id_enseignant, date_debut)
);

-- TABLE PaieEnseignant
CREATE TABLE Paie_Enseignant (
    id_paie INT AUTO_INCREMENT PRIMARY KEY,
    id_enseignant INT NOT NULL,
    salaire_brut DECIMAL(10,2) NOT NULL CHECK (salaire_brut >= 0), -- Base + Prime + Heures sup
    total_cotisation DECIMAL(10,2) CHECK (total_cotisation >= 0),-- Total des cotisations (CNSS, CIMR, etc.)
    salaire_net_imposable DECIMAL(10,2) NOT NULL CHECK (salaire_net_imposable >= 0),
    IGR DECIMAL(10,2) NOT NULL CHECK (impot >= 0),
    salaire_net_paye DECIMAL(10,2) NOT NULL CHECK (salaire_net_paye >= 0),
    mois_paie ENUM('Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', 'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre') NOT NULL,
    annee INT NOT NULL CHECK (annee >= 2025),
    date_paie DATE DEFAULT CURRENT_DATE,
    mode_paie ENUM('virement', 'chèque', 'espèces') NOT NULL,
    statut ENUM('en attente', 'validé', 'payé') DEFAULT 'en attente',
    FOREIGN KEY (id_enseignant) REFERENCES Enseignant(id_enseignant) ON DELETE CASCADE,
    UNIQUE (id_enseignant, mois_paie, annee),
    INDEX idx_date_paie (date_paie)
);
   
-- TABLE Diplome_Enseignant
CREATE TABLE Diplome (
    id_diplome INT AUTO_INCREMENT PRIMARY KEY,
    id_enseignant INT NOT NULL,
    intitule VARCHAR(100) NOT NULL,
    institut VARCHAR(100) NOT NULL,
    specialite VARCHAR(50), -- Ex. : Mathématiques, Littérature
    date_promotion DATE NOT NULL,
    FOREIGN KEY (id_enseignant) REFERENCES Enseignant(id_enseignant) ON DELETE CASCADE,
    UNIQUE (id_enseignant, intitule, institut)
);
 
-- TABLE Contrat
CREATE TABLE Contrat (
    id_contrat INT AUTO_INCREMENT PRIMARY KEY,
    id_enseignant INT NOT NULL,
    type_contrat ENUM('CDI', 'CDD', 'Vacataire', 'Autre') NOT NULL, 
    poste ENUM('enseignant', 'administratif', 'autre') NOT NULL,
    date_debut DATE NOT NULL, 
    date_fin DATE DEFAULT NULL, -- Date de fin (NULL pour CDI)
    salaire_base DECIMAL(10,2) NOT NULL CHECK (salaire_base >= 0),
    statut ENUM('actif', 'terminé', 'suspendu') DEFAULT 'actif', 
    description TEXT,
    FOREIGN KEY (id_enseignant) REFERENCES Enseignant(id_enseignant) ON DELETE CASCADE,
    CHECK (date_fin IS NULL OR date_fin > date_debut),
    UNIQUE (id_enseignant, date_debut, type_contrat)
);

-- Table Type_Rubrique (Type de rubriques de paie)
CREATE TABLE Type_Rubrique(
    code_rubrique INT PRIMARY KEY,
    libelle VARCHAR(50) NOT NULL,
    type_rubrique ENUM('gain', 'retenue') NOT NULL,
    imposable  BOOLEAN NOT NULL,
    formule_calcul VARCHAR(100) NOT NULL
);

-- Table Rubrique_Paie (Détail des rubriques associées à une paie)
CREATE TABLE Rubrique_Paie (
    id_rubrique_paie INT AUTO_INCREMENT PRIMARY KEY,
    id_paie INT NOT NULL,
    code_rubrique INT NOT NULL,
    montant DECIMAL(10,2) NOT NULL CHECK (montant >= 0),
    FOREIGN KEY (id_paie) REFERENCES Paie_Enseignant(id_paie) ON DELETE CASCADE,
    FOREIGN KEY (code_rubrique) REFERENCES Type_Rubrique(code_rubrique) ON DELETE RESTRICT,
    UNIQUE (id_paie, code_rubrique)
);