import React, { useState, useEffect } from 'react';
import { BookOpen, Calendar, Clock, Bell, TrendingUp, Activity, User, AlertCircle, Award } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import Card from '../../components/Card';
import Button from '../../components/Button';
import { getActivites, getEleve, getIdEleveByUtilisateur, getCours } from '../../services/api';
import type { Activite } from '../../types';

interface DashboardStats {
  moyenne_generale: number;
  taux_presence: number;
  activites_inscrites: number;
  prochains_examens: number;
}

const DashboardEleve: React.FC = () => {
  const navigate = useNavigate();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [activites, setActivites] = useState<Activite[]>([]);
  const [coursAujourdhui, setCoursAujourdhui] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>("");
  const [classe, setClasse] = useState<string>("");

  // Données simulées de l'élève connecté
  const user = JSON.parse(localStorage.getItem("user") || '{}');

  useEffect(() => {
    loadDashboardData();
    loadClasse();
  }, []);

  const loadClasse = async () => {
    try {
console.log(user.id_utilisateur);

      // On suppose que user.id_eleve est stocké dans le localStorage après login
      const id_eleveRes = await getIdEleveByUtilisateur(user.id_utilisateur);
const id_eleve = id_eleveRes.data.id_eleve;
console.log(id_eleve);

      const res = await getEleve(id_eleve);
      console.log("clss",res.data.data);
      
      // Récupérer le nom de la classe au lieu de l'ID
      setClasse(res.data.data?.nom_classe || "Non défini");
    } catch (e) {
      setClasse("Non défini");
    }
  };

  const loadDashboardData = async () => {
    setLoading(true);
    setError("");

    try {
      // Charger les activités
      const activitesRes = await getActivites();
      const activitesData = activitesRes.data?.data || [];
      setActivites(activitesData.slice(0, 4));

      // Charger les cours d'aujourd'hui
      await loadCoursAujourdhui();

      // Statistiques réelles (à remplacer par des appels API)
      setStats({
        moyenne_generale: 0, // À récupérer via API
        taux_presence: 0, // À récupérer via API
        activites_inscrites: activitesData.length,
        prochains_examens: 0 // À récupérer via API
      });

    } catch (error) {
      console.error("Erreur lors du chargement du dashboard:", error);
      setError("Erreur lors du chargement des données");
    } finally {
      setLoading(false);
    }
  };

  const loadCoursAujourdhui = async () => {
    try {
      console.log("🔄 Chargement des cours d'aujourd'hui");

      // Récupérer tous les cours
      const coursResponse = await getCours();
      console.log("📚 Cours API response:", coursResponse);

      if (coursResponse.data.success) {
        const tousCours = coursResponse.data.data;

        // Filtrer les cours d'aujourd'hui
        const aujourdhui = new Date();
        const joursMap = ['Dimanche', 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'];
        const jourAujourdhui = joursMap[aujourdhui.getDay()];

        console.log("📅 Jour détecté:", jourAujourdhui);
        console.log("📚 Tous les cours:", tousCours.map(c => ({ jour: c.jour_semaine, matiere: c.nom_matiere_fr })));

        const coursAujourdhui = tousCours.filter((cours: any) => {
          // Essayer différents formats de comparaison
          const jourCours = cours.jour_semaine;
          return jourCours === jourAujourdhui ||
                 jourCours === jourAujourdhui.toLowerCase() ||
                 jourCours.toLowerCase() === jourAujourdhui.toLowerCase();
        });

        // Trier par heure de début
        coursAujourdhui.sort((a: any, b: any) =>
          a.heure_debut.localeCompare(b.heure_debut)
        );

        console.log(`📅 Cours d'aujourd'hui (${jourAujourdhui}):`, coursAujourdhui);
        setCoursAujourdhui(coursAujourdhui);
      } else {
        console.log("⚠️ Pas de cours trouvés");
        setCoursAujourdhui([]);
      }
    } catch (error) {
      console.error("💥 Erreur lors du chargement des cours:", error);
      setCoursAujourdhui([]);
    }
  };

  const StatCard = ({ title, value, subtitle, icon: Icon, color, loading }: {
    title: string;
    value: string | number;
    subtitle?: string;
    icon: any;
    color: 'blue' | 'green' | 'purple' | 'orange';
    loading?: boolean;
  }) => {
    const colorClasses = {
      blue: 'bg-blue-50 text-blue-600',
      green: 'bg-green-50 text-green-600',
      purple: 'bg-purple-50 text-purple-600',
      orange: 'bg-orange-50 text-orange-600'
    };

    if (loading) {
      return (
        <Card>
          <div className="animate-pulse">
            <div className="flex items-center justify-between">
              <div>
                <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-16 mb-1"></div>
                <div className="h-3 bg-gray-200 rounded w-20"></div>
              </div>
              <div className="p-3 rounded-lg bg-gray-100">
                <div className="w-6 h-6 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>
        </Card>
      );
    }

    return (
      <Card>
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
            <p className="text-2xl font-bold text-gray-900 mb-1">
              {typeof value === 'number' ? value.toLocaleString() : value}
            </p>
            {subtitle && (
              <p className="text-sm text-gray-500">{subtitle}</p>
            )}
          </div>
          <div className={`p-3 rounded-lg ${colorClasses[color]}`}>
            <Icon className="w-6 h-6" />
          </div>
        </div>
      </Card>
    );
  };

  if (error) {
    return (
      <div className="p-6">
        <Card>
          <div className="flex items-center justify-center py-8 text-red-600">
            <AlertCircle className="w-8 h-8 mr-2" />
            <span>{error}</span>
          </div>
        </Card>
      </div>
    );
  }

  const today = new Date().toLocaleDateString('fr-FR', { 
    weekday: 'long', 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  });

  return (
    <div className="p-6 space-y-6">
      {/* En-tête de bienvenue */}
      <Card className="bg-gradient-to-r from-[#005bac] to-blue-600 text-white">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-2xl font-bold">Bonjour {user.prenom?.charAt(0).toUpperCase() +  user.prenom?.slice(1).toLowerCase()} !</h1>
            <p className="mt-1 text-blue-100">
              {today}
            </p>
          </div>
          
          <div className="mt-4 md:mt-0 flex flex-wrap gap-2">
            <div className="bg-white/10 rounded-md px-4 py-2 backdrop-blur-sm text-center">
              <p className="text-xs text-blue-100">Classe</p>
              <p className="font-bold">{classe}</p>
            </div>
            <div className="bg-white/10 rounded-md px-4 py-2 backdrop-blur-sm text-center">
              <p className="text-xs text-blue-100">Moyenne</p>
              <p className="font-bold">{stats?.moyenne_generale || 0}/20</p>
            </div>
            <div className="bg-white/10 rounded-md px-4 py-2 backdrop-blur-sm text-center">
              <p className="text-xs text-blue-100">Présence</p>
              <p className="font-bold">{stats?.taux_presence || 0}%</p>
            </div>
          </div>
        </div>
      </Card>

      {/* Cartes de statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Moyenne Générale"
          value={stats?.moyenne_generale ? `${stats.moyenne_generale}/20` : 0}
          subtitle="Dernière période"
          icon={TrendingUp}
          color="blue"
          loading={loading}
        />

        <StatCard
          title="Assiduité"
          value={stats?.taux_presence ? `${stats.taux_presence}%` : 0}
          subtitle="Taux de présence"
          icon={Clock}
          color="green"
          loading={loading}
        />
        
        <StatCard
          title="Activités"
          value={stats?.activites_inscrites || 0}
          subtitle="Inscriptions actives"
          icon={Activity}
          color="purple"
          loading={loading}
        />
        
        <StatCard
          title="Examens"
          value={stats?.prochains_examens || 0}
          subtitle="À venir cette semaine"
          icon={Award}
          color="orange"
          loading={loading}
        />
      </div>

      {/* Emploi du temps et notes */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Emploi du temps du jour */}
        <Card>
          <div className="flex items-center gap-2 mb-4">
            <Calendar className="w-5 h-5 text-blue-600" />
            <h3 className="text-lg font-medium text-gray-900">Mes cours d'aujourd'hui</h3>
          </div>

          <div className="space-y-3">
            {loading ? (
              // Skeleton loading
              [1, 2, 3, 4].map((i) => (
                <div key={i} className="animate-pulse">
                  <div className="border-l-4 border-gray-200 bg-gray-50 p-3 rounded-r-lg">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="h-4 bg-gray-200 rounded w-32 mb-2"></div>
                        <div className="h-3 bg-gray-200 rounded w-48"></div>
                      </div>
                      <div className="h-4 bg-gray-200 rounded w-20"></div>
                    </div>
                  </div>
                </div>
              ))
            ) : coursAujourdhui.length === 0 ? (
              // Aucun cours aujourd'hui
              <div className="text-center py-8 text-gray-500">
                <Calendar className="w-12 h-12 mx-auto mb-2 text-gray-300" />
                <p className="font-medium">Aucun cours aujourd'hui</p>
                <p className="text-sm">Profitez de votre journée libre !</p>
              </div>
            ) : (
              // Affichage des cours dynamiques
              coursAujourdhui.map((cours, index) => (
                <div key={cours.id_cours || index} className="border-l-4 border-blue-500 bg-blue-50 p-3 rounded-r-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-gray-900">
                        {cours.nom_matiere_fr || cours.nom_unite || 'Matière'}
                      </h4>
                      <p className="text-sm text-gray-600">
                        {cours.nom_enseignant || 'Enseignant'} • Salle {cours.nom_salle || 'N/A'}
                      </p>
                    </div>
                    <div className="text-sm font-medium text-blue-600">
                      {cours.heure_debut?.substring(0, 5)} - {cours.heure_fin?.substring(0, 5)}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Bouton pour voir l'emploi du temps complet */}
          {!loading && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <Button
                variant="outline"
                className="w-full"
                onClick={() => navigate('/eleve/emploi-du-temps')}
              >
                Voir l'emploi du temps complet
              </Button>
            </div>
          )}
        </Card>

        {/* Notes récentes */}
        <Card>
          <div className="flex items-center gap-2 mb-4">
            <TrendingUp className="w-5 h-5 text-green-600" />
            <h3 className="text-lg font-medium text-gray-900">Notes récentes</h3>
          </div>
          
          <div className="space-y-3">
            {[
              { matiere: "Mathématiques", note: 16, max: 20, type: "Contrôle", date: "2024-01-15" },
              { matiere: "Français", note: 14, max: 20, type: "Rédaction", date: "2024-01-12" },
              { matiere: "Histoire", note: 18, max: 20, type: "Exposé", date: "2024-01-10" },
              { matiere: "Anglais", note: 15, max: 20, type: "Oral", date: "2024-01-08" }
            ].map((note, index) => (
              <div key={index} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                <div>
                  <h4 className="font-medium text-gray-900">{note.matiere}</h4>
                  <p className="text-sm text-gray-600">{note.type} • {note.date}</p>
                </div>
                <div className="text-right">
                  <div className={`text-lg font-bold ${
                    note.note >= 16 ? 'text-green-600' :
                    note.note >= 12 ? 'text-blue-600' :
                    note.note >= 10 ? 'text-yellow-600' : 'text-red-600'
                  }`}>
                    {note.note}/{note.max}
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-4">
            <Button variant="outline" className="w-full">
              Voir toutes les notes
            </Button>
          </div>
        </Card>
      </div>

      {/* Activités disponibles */}
      <Card>
        <div className="flex items-center gap-2 mb-4">
          <Activity className="w-5 h-5 text-purple-600" />
          <h3 className="text-lg font-medium text-gray-900">Activités disponibles</h3>
        </div>
        
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="h-24 bg-gray-200 rounded-lg"></div>
              </div>
            ))}
          </div>
        ) : activites.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Activity className="w-12 h-12 mx-auto mb-2 text-gray-300" />
            <p>Aucune activité disponible</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {activites.map((activite) => (
              <div key={activite.id_activite} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{activite.nom_activite}</h4>
                    <p className="text-sm text-gray-600 capitalize">{activite.type_activite}</p>
                    <p className="text-xs text-gray-500 mt-1">
                      {activite.date_debut ? 
                        `Début: ${new Date(activite.date_debut).toLocaleDateString('fr-FR')}` : 
                        'Date à définir'
                      }
                    </p>
                  </div>
                  <div className="text-right ml-4">
                    <div className="text-sm font-medium text-gray-900">{activite.prix} DH</div>
                    <Button size="sm" variant="outline" className="mt-2">
                      S'inscrire
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </Card>

      {/* Actions rapides */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card
          className="cursor-pointer hover:shadow-md transition-shadow"
          onClick={() => navigate('/eleve/emploi-du-temps')}
        >
          <div className="flex flex-col items-center text-center p-4">
            <div className="p-3 rounded-full bg-blue-100 mb-3">
              <Calendar className="w-6 h-6 text-blue-600" />
            </div>
            <h3 className="font-medium text-gray-900">Emploi du temps</h3>
            <p className="text-xs text-gray-500 mt-1">Consulter la semaine</p>
          </div>
        </Card>
        
        <Card>
          <div className="flex flex-col items-center text-center p-4">
            <div className="p-3 rounded-full bg-green-100 mb-3">
              <TrendingUp className="w-6 h-6 text-green-600" />
            </div>
            <h3 className="font-medium text-gray-900">Mes notes</h3>
            <p className="text-xs text-gray-500 mt-1">Voir mes résultats</p>
          </div>
        </Card>
        
        <Card>
          <div className="flex flex-col items-center text-center p-4">
            <div className="p-3 rounded-full bg-purple-100 mb-3">
              <BookOpen className="w-6 h-6 text-purple-600" />
            </div>
            <h3 className="font-medium text-gray-900">Devoirs</h3>
            <p className="text-xs text-gray-500 mt-1">À faire cette semaine</p>
          </div>
        </Card>
        
        <Card>
          <div className="flex flex-col items-center text-center p-4">
            <div className="p-3 rounded-full bg-orange-100 mb-3">
              <Bell className="w-6 h-6 text-orange-600" />
            </div>
            <h3 className="font-medium text-gray-900">Notifications</h3>
            <p className="text-xs text-gray-500 mt-1">Messages importants</p>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default DashboardEleve;
