import { useState, useCallback } from 'react';
import { extractErrorMessage, extractValidationErrors, handleGlobalError } from '../utils/errorUtils';

interface UseErrorHandlerReturn {
  error: string | null;
  validationErrors: { [key: string]: string };
  isLoading: boolean;
  clearError: () => void;
  clearValidationErrors: () => void;
  clearFieldError: (field: string) => void;
  handleError: (error: any, context?: string) => void;
  executeWithErrorHandling: <T>(
    asyncFn: () => Promise<T>,
    context?: string,
    onSuccess?: (result: T) => void
  ) => Promise<T | null>;
}

/**
 * Hook personnalisé pour gérer les erreurs de manière cohérente
 */
export const useErrorHandler = (): UseErrorHandlerReturn => {
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<{ [key: string]: string }>({});
  const [isLoading, setIsLoading] = useState(false);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const clearValidationErrors = useCallback(() => {
    setValidationErrors({});
  }, []);

  const clearFieldError = useCallback((field: string) => {
    setValidationErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
  }, []);

  const handleError = useCallback((error: any, context: string = 'Opération') => {
    console.error(`Erreur dans ${context}:`, error);

    // Extraire les erreurs de validation
    const fieldErrors = extractValidationErrors(error);
    if (Object.keys(fieldErrors).length > 0) {
      setValidationErrors(fieldErrors);
      setError(null); // Pas de message d'erreur global si on a des erreurs de champs
    } else {
      // Message d'erreur global
      const errorMessage = error.userMessage || extractErrorMessage(error, `Erreur lors de ${context.toLowerCase()}`);
      setError(errorMessage);
      setValidationErrors({});
    }
  }, []);

  const executeWithErrorHandling = useCallback(async <T>(
    asyncFn: () => Promise<T>,
    context: string = 'l\'opération',
    onSuccess?: (result: T) => void
  ): Promise<T | null> => {
    setIsLoading(true);
    setError(null);
    setValidationErrors({});

    try {
      const result = await asyncFn();
      if (onSuccess) {
        onSuccess(result);
      }
      return result;
    } catch (error: any) {
      handleError(error, context);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [handleError]);

  return {
    error,
    validationErrors,
    isLoading,
    clearError,
    clearValidationErrors,
    clearFieldError,
    handleError,
    executeWithErrorHandling
  };
};

/**
 * Hook spécialisé pour les formulaires
 */
export const useFormErrorHandler = () => {
  const errorHandler = useErrorHandler();

  const handleInputChange = useCallback((field: string) => {
    // Effacer l'erreur du champ quand l'utilisateur tape
    if (errorHandler.validationErrors[field]) {
      errorHandler.clearFieldError(field);
    }
    // Effacer l'erreur globale aussi
    if (errorHandler.error) {
      errorHandler.clearError();
    }
  }, [errorHandler]);

  return {
    ...errorHandler,
    handleInputChange
  };
};
