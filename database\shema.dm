# ScolaNova
👉 une application scolaire avec :

✅ Frontend : React (consomme une API REST)

✅ Backend PHP : organise avec MVC + expose une API REST

/ScolaNova/
│
├── /backend/                 # Code backend (PHP)
│   ├── /config/              # Configuration de la base de données et autres paramètres
│   ├── /controllers/         # Logique de traitement des requêtes
│   │   ├── UtilisateurController.php
│   │   └── EleveController.php
│   ├── /models/              # Modèles de données (représentation des entités de la BDD)
│   │   ├── User.php
│   │   └── Eleve.php
│   ├── /routes/              # Définition des routes API (les endpoints et leurs actions)
│   │   └── api.php
│   │   └── eleve.php
│   ├── /utils/               # Fonctions utilitaires (la gestion de l'authentification)
│   │   └── auth.php
│   ├── /public/              # Point d'entrée public (pour l'API)
│   │   └── index.php         # Point d'accès à l'API
│
├── /frontend/                # Code frontend (React)
│   ├── /public/              # Fichiers statiques (index.html, images, etc.)
│   ├── /src/                 # Code source React
│   │   ├── /components/      # Composants réutilisables (Header, Sidebar, etc.)
│   │   │   ├── Header.tsx
│   │   │   └── Sidebar.tsx
│   │   ├── /context/ 
│   │   │   └── AuthContext.ts    
│   │   ├── /pages/           # Pages (connexion, dashboard, etc.)
│   │   │   ├── /admin/
│   │   │   ├── /eleve/
│   │   │   ├── /enseignant/
│   │   │   ├── /parent/
│   │   │   ├── Login.tsx
│   │   │   ├── Layout.tsx
│   │   │   └── ChangePassword.tsx
│   │   ├── /services/        # Services API (communication avec le backend)
│   │   │   └── api.ts
│   │   ├── /utils/           # Fonctions utilitaires
│   │   │   └── auth.ts
│   │   ├── App.tsx           # Composant principal de l'application
│   │   └── index.tsx         # Point d'entrée React
│   │   └── types.tsx         # Definition des interfaces
│   ├── /tailwind.config.js   # Configuration de TailwindCSS
│   └── package.json          # Dépendances et scripts du frontend
│
├── .gitignore                # Fichiers à ignorer par Git
├── README.md                 # Documentation du projet
