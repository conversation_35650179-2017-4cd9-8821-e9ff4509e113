import React, { useState } from 'react';
import Select from './Select';

/**
 * Composant de test pour vérifier le comportement du select de relation
 */
const RelationSelectTest: React.FC = () => {
  const [selectedRelation, setSelectedRelation] = useState<string>('');

  const relationOptions = [
    { value: "père", label: "Père" },
    { value: "mère", label: "<PERSON><PERSON>" },
    { value: "tuteur", label: "<PERSON>te<PERSON>" },
  ];

  return (
    <div className="max-w-md mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-xl font-bold mb-4">Test Select Relation</h2>
      
      <div className="space-y-4">
        <Select
          name="type_relation"
          label="Type de relation"
          options={relationOptions}
          value={selectedRelation}
          onChange={(e) => setSelectedRelation(e.target.value)}
          placeholder="Sélectionnez le type de la relation"
        />
        
        <div className="p-4 bg-gray-50 rounded-lg">
          <h3 className="font-semibold mb-2">Valeur sélectionnée :</h3>
          <p className="text-gray-700">
            {selectedRelation || '(Aucune sélection)'}
          </p>
        </div>
        
        <button
          onClick={() => setSelectedRelation('')}
          className="w-full px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600"
        >
          Réinitialiser
        </button>
      </div>
      
      <div className="mt-6 text-sm text-gray-600">
        <h3 className="font-semibold mb-2">Comportement attendu :</h3>
        <ul className="list-disc list-inside space-y-1">
          <li>Par défaut : Affiche "Sélectionnez le type de la relation"</li>
          <li>Pas de valeur pré-sélectionnée</li>
          <li>L'utilisateur doit faire un choix explicite</li>
          <li>Après réinitialisation : Retour au placeholder</li>
        </ul>
      </div>
    </div>
  );
};

export default RelationSelectTest;
