import React from 'react';
import { AlertCircle, X, Wifi, RefreshCw } from 'lucide-react';

interface ErrorDisplayProps {
  error: string | null;
  onClose?: () => void;
  onRetry?: () => void;
  className?: string;
  variant?: 'error' | 'warning' | 'info';
  showIcon?: boolean;
  dismissible?: boolean;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  onClose,
  onRetry,
  className = '',
  variant = 'error',
  showIcon = true,
  dismissible = true
}) => {
  if (!error) return null;

  const getVariantStyles = () => {
    switch (variant) {
      case 'warning':
        return 'bg-yellow-50 text-yellow-800 border-yellow-200';
      case 'info':
        return 'bg-blue-50 text-blue-800 border-blue-200';
      default:
        return 'bg-red-50 text-red-800 border-red-200';
    }
  };

  const getIcon = () => {
    if (!showIcon) return null;
    
    if (error.toLowerCase().includes('connexion') || error.toLowerCase().includes('réseau')) {
      return <Wifi className="w-5 h-5 flex-shrink-0" />;
    }
    
    return <AlertCircle className="w-5 h-5 flex-shrink-0" />;
  };

  return (
    <div className={`p-4 rounded-lg border ${getVariantStyles()} ${className}`}>
      <div className="flex items-start">
        {getIcon()}
        <div className="flex-1 ml-3">
          <p className="text-sm font-medium">{error}</p>
        </div>
        <div className="flex items-center space-x-2 ml-4">
          {onRetry && (
            <button
              onClick={onRetry}
              className="text-gray-400 hover:text-gray-600 transition-colors"
              title="Réessayer"
            >
              <RefreshCw className="w-4 h-4" />
            </button>
          )}
          {dismissible && onClose && (
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
              title="Fermer"
            >
              <X className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ErrorDisplay;
