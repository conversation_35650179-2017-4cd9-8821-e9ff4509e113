<?php

require_once __DIR__ . '/../controllers/ClasseController.php';

function handleClasseRoutes($uri, $method, $pdo) {
    $classeController = new ClasseController($pdo);

    // GET /classes - Récupérer toutes les classes
    if ($uri === '/classes' && $method === 'GET') {
        $classeController->getAll();
        return true;
    }



    // GET /classes/{id} - Récupérer une classe par ID
    if (preg_match('/^\/classes\/(\d+)$/', $uri, $matches) && $method === 'GET') {
        $id = (int)$matches[1];
        $classeController->getById($id);
        return true;
    }

    // GET /classes/{id}/eleves - Récupérer les élèves d'une classe
    if (preg_match('/^\/classes\/(\d+)\/eleves$/', $uri, $matches) && $method === 'GET') {
        $id = (int)$matches[1];
        $classeController->getEleves($id);
        return true;
    }

    // GET /classes/niveau/{id} - Récupérer les classes par niveau
    if (preg_match('/^\/classes\/niveau\/(\d+)$/', $uri, $matches) && $method === 'GET') {
        $idNiveau = (int)$matches[1];
        $classeController->getByNiveau($idNiveau);
        return true;
    }

    // POST /classes - Créer une nouvelle classe
    if ($uri === '/classes' && $method === 'POST') {
        $classeController->create();
        return true;
    }

    // PUT /classes/{id} - Mettre à jour une classe
    if (preg_match('/^\/classes\/(\d+)$/', $uri, $matches) && $method === 'PUT') {
        $id = (int)$matches[1];
        $classeController->update($id);
        return true;
    }

    // DELETE /classes/{id} - Supprimer une classe
    if (preg_match('/^\/classes\/(\d+)$/', $uri, $matches) && $method === 'DELETE') {
        $id = (int)$matches[1];
        $classeController->delete($id);
        return true;
    }

    return false;
}
