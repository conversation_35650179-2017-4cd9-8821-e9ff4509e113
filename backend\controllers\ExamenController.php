<?php
// controllers/ExamenController.php
require_once __DIR__ . '/../models/Examen.php';
require_once __DIR__ . '/../config/db.php';

class ExamenController {
    private $pdo;
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    public function getAll() {
        $stmt = $this->pdo->query('SELECT * FROM Examen');
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getById($id) {
        $stmt = $this->pdo->prepare('SELECT * FROM Examen WHERE id_examen = ?');
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function create($data) {
        $sql = 'INSERT INTO Examen (id_matiere, id_classe, id_enseignant, type_examen, date_examen, duree_examen, semestre, commentaire) VALUES (?, ?, ?, ?, ?, ?, ?, ?)';
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([
            $data['id_matiere'],
            $data['id_classe'],
            $data['id_enseignant'],
            $data['type_examen'],
            $data['date_examen'],
            $data['duree_examen'],
            $data['semestre'],
            $data['commentaire'] ?? null
        ]);
        return $this->pdo->lastInsertId();
    }

    public function update($id, $data) {
        $sql = 'UPDATE Examen SET id_matiere=?, id_classe=?, id_enseignant=?, type_examen=?, date_examen=?, duree_examen=?, semestre=?, commentaire=? WHERE id_examen=?';
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([
            $data['id_matiere'],
            $data['id_classe'],
            $data['id_enseignant'],
            $data['type_examen'],
            $data['date_examen'],
            $data['duree_examen'],
            $data['semestre'],
            $data['commentaire'] ?? null,
            $id
        ]);
        return $stmt->rowCount();
    }

    public function delete($id) {
        $stmt = $this->pdo->prepare('DELETE FROM Examen WHERE id_examen = ?');
        $stmt->execute([$id]);
        return $stmt->rowCount();
    }
}
