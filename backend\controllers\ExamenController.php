<?php
// controllers/ExamenController.php
require_once __DIR__ . '/../models/Examen.php';
require_once __DIR__ . '/../config/db.php';
require_once __DIR__ . '/../utils/auth.php';

class ExamenController {
    private $pdo;
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    public function getAll() {
        try {
            $query = "
                SELECT
                    e.*,
                    m.nom_matiere_fr,
                    m.nom_matiere_ar,
                    c.nom_classe,
                    u.nom as nom_enseignant,
                    u.prenom as prenom_enseignant,
                    COUNT(n.id_eleve) as nombre_notes,
                    AVG(n.note) as moyenne_classe
                FROM Examen e
                LEFT JOIN Matiere m ON e.id_matiere = m.id_matiere
                LEFT JOIN Classe c ON e.id_classe = c.id_classe
                LEFT JOIN Enseignant ens ON e.id_enseignant = ens.id_enseignant
                LEFT JOIN Utilisateur u ON ens.id_user = u.id_user
                LEFT JOIN Note n ON e.id_examen = n.id_examen
                GROUP BY e.id_examen
                ORDER BY e.date_examen DESC, e.id_examen DESC
            ";

            $stmt = $this->pdo->prepare($query);
            $stmt->execute();
            $examens = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Formater les données
            $examensFormates = array_map(function($examen) {
                return [
                    'id_examen' => (int)$examen['id_examen'],
                    'id_matiere' => (int)$examen['id_matiere'],
                    'id_classe' => (int)$examen['id_classe'],
                    'id_enseignant' => $examen['id_enseignant'] ? (int)$examen['id_enseignant'] : null,
                    'type_examen' => $examen['type_examen'],
                    'date_examen' => $examen['date_examen'],
                    'duree_examen' => (int)$examen['duree_examen'],
                    'semestre' => $examen['semestre'],
                    'commentaire' => $examen['commentaire'],
                    'matiere' => [
                        'nom_matiere_fr' => $examen['nom_matiere_fr'],
                        'nom_matiere_ar' => $examen['nom_matiere_ar']
                    ],
                    'classe' => [
                        'nom_classe' => $examen['nom_classe']
                    ],
                    'enseignant' => $examen['nom_enseignant'] ? [
                        'nom' => $examen['nom_enseignant'],
                        'prenom' => $examen['prenom_enseignant']
                    ] : null,
                    'nombre_notes' => (int)$examen['nombre_notes'],
                    'moyenne_classe' => $examen['moyenne_classe'] ? round((float)$examen['moyenne_classe'], 2) : null
                ];
            }, $examens);

            http_response_code(200);
            echo json_encode([
                'success' => true,
                'data' => $examensFormates,
                'message' => 'Examens récupérés avec succès'
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la récupération des examens',
                'error' => $e->getMessage()
            ]);
        }
    }

    public function getById($id) {
        $stmt = $this->pdo->prepare('SELECT * FROM Examen WHERE id_examen = ?');
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function create($data) {
        $sql = 'INSERT INTO Examen (id_matiere, id_classe, id_enseignant, type_examen, date_examen, duree_examen, semestre, commentaire) VALUES (?, ?, ?, ?, ?, ?, ?, ?)';
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([
            $data['id_matiere'],
            $data['id_classe'],
            $data['id_enseignant'],
            $data['type_examen'],
            $data['date_examen'],
            $data['duree_examen'],
            $data['semestre'],
            $data['commentaire'] ?? null
        ]);
        return $this->pdo->lastInsertId();
    }

    public function update($id, $data) {
        $sql = 'UPDATE Examen SET id_matiere=?, id_classe=?, id_enseignant=?, type_examen=?, date_examen=?, duree_examen=?, semestre=?, commentaire=? WHERE id_examen=?';
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([
            $data['id_matiere'],
            $data['id_classe'],
            $data['id_enseignant'],
            $data['type_examen'],
            $data['date_examen'],
            $data['duree_examen'],
            $data['semestre'],
            $data['commentaire'] ?? null,
            $id
        ]);
        return $stmt->rowCount();
    }

    public function delete($id) {
        $stmt = $this->pdo->prepare('DELETE FROM Examen WHERE id_examen = ?');
        $stmt->execute([$id]);
        return $stmt->rowCount();
    }
}
