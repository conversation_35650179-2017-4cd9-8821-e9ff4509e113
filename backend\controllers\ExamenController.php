<?php
// controllers/ExamenController.php
require_once __DIR__ . '/../models/Examen.php';
require_once __DIR__ . '/../config/db.php';
require_once __DIR__ . '/../utils/auth.php';

class ExamenController {
    private $pdo;
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    public function getAll() {
        try {
            $query = "
                SELECT
                    e.*,
                    m.nom_matiere_fr,
                    m.nom_matiere_ar,
                    c.nom_classe,
                    u.nom as nom_enseignant,
                    u.prenom as prenom_enseignant,
                    COUNT(n.id_eleve) as nombre_notes,
                    AVG(n.note) as moyenne_classe
                FROM Examen e
                LEFT JOIN Matiere m ON e.id_matiere = m.id_matiere
                LEFT JOIN Classe c ON e.id_classe = c.id_classe
                LEFT JOIN Enseignant ens ON e.id_enseignant = ens.id_enseignant
                LEFT JOIN Utilisateur u ON ens.id_utilisateur = u.id_utilisateur
                LEFT JOIN Note n ON e.id_examen = n.id_examen
                GROUP BY e.id_examen
                ORDER BY e.date_examen DESC, e.id_examen DESC
            ";

            $stmt = $this->pdo->prepare($query);
            $stmt->execute();
            $examens = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Formater les données
            $examensFormates = array_map(function($examen) {
                return [
                    'id_examen' => (int)$examen['id_examen'],
                    'id_matiere' => (int)$examen['id_matiere'],
                    'id_classe' => (int)$examen['id_classe'],
                    'id_enseignant' => $examen['id_enseignant'] ? (int)$examen['id_enseignant'] : null,
                    'type_examen' => $examen['type_examen'],
                    'date_examen' => $examen['date_examen'],
                    'duree_examen' => (int)$examen['duree_examen'],
                    'semestre' => $examen['semestre'],
                    'commentaire' => $examen['commentaire'],
                    'matiere' => [
                        'nom_matiere_fr' => $examen['nom_matiere_fr'],
                        'nom_matiere_ar' => $examen['nom_matiere_ar']
                    ],
                    'classe' => [
                        'nom_classe' => $examen['nom_classe']
                    ],
                    'enseignant' => $examen['nom_enseignant'] ? [
                        'nom' => $examen['nom_enseignant'],
                        'prenom' => $examen['prenom_enseignant']
                    ] : null,
                    'nombre_notes' => (int)$examen['nombre_notes'],
                    'moyenne_classe' => $examen['moyenne_classe'] ? round((float)$examen['moyenne_classe'], 2) : null
                ];
            }, $examens);

            http_response_code(200);
            echo json_encode([
                'success' => true,
                'data' => $examensFormates,
                'message' => 'Examens récupérés avec succès'
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la récupération des examens',
                'error' => $e->getMessage()
            ]);
        }
    }

    public function getById($id) {
        $stmt = $this->pdo->prepare('SELECT * FROM Examen WHERE id_examen = ?');
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function create() {
        try {
            $input = json_decode(file_get_contents('php://input'), true);

            // Validation des données
            if (!isset($input['id_matiere']) || !isset($input['id_classe']) ||
                !isset($input['type_examen']) || !isset($input['date_examen']) ||
                !isset($input['duree_examen']) || !isset($input['semestre'])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Tous les champs obligatoires doivent être remplis'
                ]);
                return;
            }

            // Validation des types
            $id_matiere = (int)$input['id_matiere'];
            $id_classe = (int)$input['id_classe'];
            $id_enseignant = isset($input['id_enseignant']) && $input['id_enseignant'] ? (int)$input['id_enseignant'] : null;
            $type_examen = $input['type_examen'];
            $date_examen = $input['date_examen'];
            $duree_examen = (int)$input['duree_examen'];
            $semestre = $input['semestre'];
            $commentaire = isset($input['commentaire']) ? $input['commentaire'] : null;

            // Validation des valeurs
            if (!in_array($type_examen, ['examen', 'devoir', 'contrôle', 'participation'])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Type d\'examen invalide'
                ]);
                return;
            }

            if (!in_array($semestre, ['S1', 'S2'])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Semestre invalide'
                ]);
                return;
            }

            if ($duree_examen <= 0 || $duree_examen > 480) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'La durée doit être entre 1 et 480 minutes'
                ]);
                return;
            }

            // Vérifier que la matière existe
            $stmt = $this->pdo->prepare('SELECT id_matiere FROM Matiere WHERE id_matiere = ?');
            $stmt->execute([$id_matiere]);
            if (!$stmt->fetch()) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Matière non trouvée'
                ]);
                return;
            }

            // Vérifier que la classe existe
            $stmt = $this->pdo->prepare('SELECT id_classe FROM Classe WHERE id_classe = ?');
            $stmt->execute([$id_classe]);
            if (!$stmt->fetch()) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Classe non trouvée'
                ]);
                return;
            }

            // Vérifier que l'enseignant existe (si spécifié)
            if ($id_enseignant) {
                $stmt = $this->pdo->prepare('SELECT id_enseignant FROM Enseignant WHERE id_enseignant = ?');
                $stmt->execute([$id_enseignant]);
                if (!$stmt->fetch()) {
                    http_response_code(400);
                    echo json_encode([
                        'success' => false,
                        'message' => 'Enseignant non trouvé'
                    ]);
                    return;
                }
            }

            $sql = 'INSERT INTO Examen (id_matiere, id_classe, id_enseignant, type_examen, date_examen, duree_examen, semestre, commentaire) VALUES (?, ?, ?, ?, ?, ?, ?, ?)';
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([
                $id_matiere,
                $id_classe,
                $id_enseignant,
                $type_examen,
                $date_examen,
                $duree_examen,
                $semestre,
                $commentaire
            ]);

            $id_examen = $this->pdo->lastInsertId();

            http_response_code(201);
            echo json_encode([
                'success' => true,
                'data' => ['id_examen' => $id_examen],
                'message' => 'Examen créé avec succès'
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la création de l\'examen',
                'error' => $e->getMessage()
            ]);
        }
    }

    public function update($id) {
        try {
            $input = json_decode(file_get_contents('php://input'), true);

            // Vérifier que l'examen existe
            $stmt = $this->pdo->prepare('SELECT id_examen FROM Examen WHERE id_examen = ?');
            $stmt->execute([$id]);
            if (!$stmt->fetch()) {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'message' => 'Examen non trouvé'
                ]);
                return;
            }

            // Validation des données (même que create)
            if (!isset($input['id_matiere']) || !isset($input['id_classe']) ||
                !isset($input['type_examen']) || !isset($input['date_examen']) ||
                !isset($input['duree_examen']) || !isset($input['semestre'])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Tous les champs obligatoires doivent être remplis'
                ]);
                return;
            }

            $id_matiere = (int)$input['id_matiere'];
            $id_classe = (int)$input['id_classe'];
            $id_enseignant = isset($input['id_enseignant']) && $input['id_enseignant'] ? (int)$input['id_enseignant'] : null;
            $type_examen = $input['type_examen'];
            $date_examen = $input['date_examen'];
            $duree_examen = (int)$input['duree_examen'];
            $semestre = $input['semestre'];
            $commentaire = isset($input['commentaire']) ? $input['commentaire'] : null;

            // Validations (même que create)
            if (!in_array($type_examen, ['examen', 'devoir', 'contrôle', 'participation'])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Type d\'examen invalide'
                ]);
                return;
            }

            if (!in_array($semestre, ['S1', 'S2'])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Semestre invalide'
                ]);
                return;
            }

            if ($duree_examen <= 0 || $duree_examen > 480) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'La durée doit être entre 1 et 480 minutes'
                ]);
                return;
            }

            $sql = 'UPDATE Examen SET id_matiere=?, id_classe=?, id_enseignant=?, type_examen=?, date_examen=?, duree_examen=?, semestre=?, commentaire=? WHERE id_examen=?';
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([
                $id_matiere,
                $id_classe,
                $id_enseignant,
                $type_examen,
                $date_examen,
                $duree_examen,
                $semestre,
                $commentaire,
                $id
            ]);

            http_response_code(200);
            echo json_encode([
                'success' => true,
                'message' => 'Examen modifié avec succès'
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la modification de l\'examen',
                'error' => $e->getMessage()
            ]);
        }
    }

    public function delete($id) {
        try {
            // Vérifier que l'examen existe
            $stmt = $this->pdo->prepare('SELECT id_examen FROM Examen WHERE id_examen = ?');
            $stmt->execute([$id]);
            if (!$stmt->fetch()) {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'message' => 'Examen non trouvé'
                ]);
                return;
            }

            // Supprimer d'abord les notes associées (CASCADE devrait le faire automatiquement)
            $stmt = $this->pdo->prepare('DELETE FROM Note WHERE id_examen = ?');
            $stmt->execute([$id]);

            // Supprimer l'examen
            $stmt = $this->pdo->prepare('DELETE FROM Examen WHERE id_examen = ?');
            $stmt->execute([$id]);

            http_response_code(200);
            echo json_encode([
                'success' => true,
                'message' => 'Examen supprimé avec succès'
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la suppression de l\'examen',
                'error' => $e->getMessage()
            ]);
        }
    }
}
