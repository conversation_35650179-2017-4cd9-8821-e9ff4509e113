<?php

/**
 * Service d'envoi d'email SMTP sans dépendance externe
 * Alternative à PHPMailer pour l'envoi d'emails via SMTP
 */

class SMTPEmailService {
    private $config;
    private $socket;
    private $lastError;

    public function __construct() {
        $this->config = require __DIR__ . '/../config/email.php';
    }

    /**
     * Envoie un email avec mot de passe temporaire
     */
    public function sendTemporaryPassword($toEmail, $toName, $temporaryPassword) {
        try {
            error_log("📧 SMTP - Envoi mot de passe temporaire à: $toEmail");
            
            $subject = $this->config['templates']['temporary_password']['subject'];
            $message = $this->getTemporaryPasswordTemplate($toName, $temporaryPassword);
            
            $result = $this->sendEmail($toEmail, $toName, $subject, $message);
            
            if ($result) {
                error_log("✅ Email SMTP envoyé avec succès");
                return true;
            } else {
                error_log("❌ Échec envoi SMTP: " . $this->lastError);
                // Fallback vers sauvegarde
                $this->saveEmailToFile($toEmail, $toName, $subject, $message);
                return true; // Ne pas faire échouer le processus
            }
            
        } catch (Exception $e) {
            error_log("💥 Erreur SMTP: " . $e->getMessage());
            // Fallback vers sauvegarde
            $this->saveEmailToFile($toEmail, $toName, $subject, $message);
            return true;
        }
    }

    /**
     * Envoie un email via SMTP
     */
    private function sendEmail($toEmail, $toName, $subject, $message) {
        try {
            // Connexion au serveur SMTP
            if (!$this->connectSMTP()) {
                return false;
            }

            // Authentification
            if (!$this->authenticateSMTP()) {
                $this->disconnectSMTP();
                return false;
            }

            // Envoi de l'email
            $success = $this->sendSMTPEmail($toEmail, $toName, $subject, $message);
            
            // Déconnexion
            $this->disconnectSMTP();
            
            return $success;
            
        } catch (Exception $e) {
            $this->lastError = $e->getMessage();
            error_log("Erreur SMTP: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Connexion au serveur SMTP
     */
    private function connectSMTP() {
        $host = $this->config['smtp']['host'];
        $port = $this->config['smtp']['port'];
        
        error_log("🔗 Connexion SMTP à $host:$port");
        
        $this->socket = @fsockopen($host, $port, $errno, $errstr, 30);
        
        if (!$this->socket) {
            $this->lastError = "Impossible de se connecter à $host:$port - $errstr ($errno)";
            return false;
        }
        
        // Lire la réponse de bienvenue
        $response = $this->readSMTPResponse();
        if (!$this->isSuccessResponse($response)) {
            $this->lastError = "Réponse de connexion invalide: $response";
            return false;
        }
        
        // EHLO
        $this->sendSMTPCommand("EHLO " . $_SERVER['SERVER_NAME'] ?? 'localhost');
        $response = $this->readSMTPResponse();
        if (!$this->isSuccessResponse($response)) {
            $this->lastError = "Échec EHLO: $response";
            return false;
        }
        
        // STARTTLS si nécessaire
        if ($this->config['smtp']['encryption'] === 'tls') {
            $this->sendSMTPCommand("STARTTLS");
            $response = $this->readSMTPResponse();
            if (!$this->isSuccessResponse($response)) {
                $this->lastError = "Échec STARTTLS: $response";
                return false;
            }
            
            // Activer le chiffrement TLS
            if (!stream_socket_enable_crypto($this->socket, true, STREAM_CRYPTO_METHOD_TLS_CLIENT)) {
                $this->lastError = "Impossible d'activer TLS";
                return false;
            }
            
            // Nouveau EHLO après TLS
            $this->sendSMTPCommand("EHLO " . $_SERVER['SERVER_NAME'] ?? 'localhost');
            $this->readSMTPResponse();
        }
        
        return true;
    }

    /**
     * Authentification SMTP
     */
    private function authenticateSMTP() {
        $username = $this->config['smtp']['username'];
        $password = $this->config['smtp']['password'];
        
        error_log("🔐 Authentification SMTP pour: $username");
        
        // AUTH LOGIN
        $this->sendSMTPCommand("AUTH LOGIN");
        $response = $this->readSMTPResponse();
        if (!$this->isSuccessResponse($response)) {
            $this->lastError = "Échec AUTH LOGIN: $response";
            return false;
        }
        
        // Envoyer le nom d'utilisateur (encodé en base64)
        $this->sendSMTPCommand(base64_encode($username));
        $response = $this->readSMTPResponse();
        if (!$this->isSuccessResponse($response)) {
            $this->lastError = "Échec authentification username: $response";
            return false;
        }
        
        // Envoyer le mot de passe (encodé en base64)
        $this->sendSMTPCommand(base64_encode($password));
        $response = $this->readSMTPResponse();
        if (!$this->isSuccessResponse($response)) {
            $this->lastError = "Échec authentification password: $response";
            return false;
        }
        
        return true;
    }

    /**
     * Envoi de l'email via SMTP
     */
    private function sendSMTPEmail($toEmail, $toName, $subject, $message) {
        $fromEmail = $this->config['from']['email'];
        $fromName = $this->config['from']['name'];
        
        // MAIL FROM
        $this->sendSMTPCommand("MAIL FROM: <$fromEmail>");
        $response = $this->readSMTPResponse();
        if (!$this->isSuccessResponse($response)) {
            $this->lastError = "Échec MAIL FROM: $response";
            return false;
        }
        
        // RCPT TO
        $this->sendSMTPCommand("RCPT TO: <$toEmail>");
        $response = $this->readSMTPResponse();
        if (!$this->isSuccessResponse($response)) {
            $this->lastError = "Échec RCPT TO: $response";
            return false;
        }
        
        // DATA
        $this->sendSMTPCommand("DATA");
        $response = $this->readSMTPResponse();
        if (!$this->isSuccessResponse($response)) {
            $this->lastError = "Échec DATA: $response";
            return false;
        }
        
        // Headers et contenu
        $headers = $this->buildEmailHeaders($fromEmail, $fromName, $toEmail, $toName, $subject);
        $emailContent = $headers . "\r\n" . $message . "\r\n.";
        
        $this->sendSMTPCommand($emailContent);
        $response = $this->readSMTPResponse();
        if (!$this->isSuccessResponse($response)) {
            $this->lastError = "Échec envoi contenu: $response";
            return false;
        }
        
        return true;
    }

    /**
     * Construction des headers d'email
     */
    private function buildEmailHeaders($fromEmail, $fromName, $toEmail, $toName, $subject) {
        $headers = [];
        $headers[] = "From: $fromName <$fromEmail>";
        $headers[] = "To: $toName <$toEmail>";
        $headers[] = "Subject: =?UTF-8?B?" . base64_encode($subject) . "?=";
        $headers[] = "MIME-Version: 1.0";
        $headers[] = "Content-Type: text/html; charset=UTF-8";
        $headers[] = "Content-Transfer-Encoding: 8bit";
        $headers[] = "Date: " . date('r');
        $headers[] = "Message-ID: <" . uniqid() . "@" . $_SERVER['SERVER_NAME'] ?? 'localhost' . ">";
        
        return implode("\r\n", $headers);
    }

    /**
     * Envoie une commande SMTP
     */
    private function sendSMTPCommand($command) {
        fwrite($this->socket, $command . "\r\n");
        error_log("SMTP >> $command");
    }

    /**
     * Lit la réponse SMTP
     */
    private function readSMTPResponse() {
        $response = '';
        while (($line = fgets($this->socket, 515)) !== false) {
            $response .= $line;
            if (substr($line, 3, 1) === ' ') {
                break;
            }
        }
        $response = trim($response);
        error_log("SMTP << $response");
        return $response;
    }

    /**
     * Vérifie si la réponse SMTP indique un succès
     */
    private function isSuccessResponse($response) {
        $code = substr($response, 0, 3);
        return in_array($code, ['220', '221', '235', '250', '354']);
    }

    /**
     * Déconnexion SMTP
     */
    private function disconnectSMTP() {
        if ($this->socket) {
            $this->sendSMTPCommand("QUIT");
            $this->readSMTPResponse();
            fclose($this->socket);
            $this->socket = null;
        }
    }

    /**
     * Template HTML pour l'email de mot de passe temporaire
     */
    private function getTemporaryPasswordTemplate($name, $password) {
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <title>Mot de passe temporaire - ScolaNova</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background-color: #2563eb; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background-color: #f9f9f9; }
                .password-box { background-color: #e3f2fd; border: 2px solid #2196f3; padding: 15px; margin: 20px 0; text-align: center; }
                .password { font-size: 24px; font-weight: bold; color: #1976d2; letter-spacing: 2px; }
                .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; }
                .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>ScolaNova</h1>
                    <p>Système de Gestion Scolaire</p>
                </div>
                
                <div class='content'>
                    <h2>Bonjour $name,</h2>
                    
                    <p>Votre compte a été créé avec succès sur la plateforme ScolaNova.</p>
                    
                    <p>Voici votre mot de passe temporaire :</p>
                    
                    <div class='password-box'>
                        <div class='password'>$password</div>
                    </div>
                    
                    <div class='warning'>
                        <strong>⚠️ Important :</strong>
                        <ul>
                            <li>Ce mot de passe est temporaire et doit être changé lors de votre première connexion</li>
                            <li>Pour des raisons de sécurité, vous serez obligé(e) de définir un nouveau mot de passe</li>
                            <li>Ne partagez jamais vos identifiants de connexion</li>
                        </ul>
                    </div>
                    
                    <p>Pour vous connecter :</p>
                    <ol>
                        <li>Rendez-vous sur la plateforme ScolaNova</li>
                        <li>Utilisez votre adresse email et le mot de passe temporaire ci-dessus</li>
                        <li>Suivez les instructions pour définir votre nouveau mot de passe</li>
                    </ol>
                    
                    <p>Si vous avez des questions, n'hésitez pas à contacter l'administration.</p>
                    
                    <p>Cordialement,<br>L'équipe ScolaNova</p>
                </div>
                
                <div class='footer'>
                    <p>Cet email a été envoyé automatiquement, merci de ne pas y répondre.</p>
                    <p>© " . date('Y') . " ScolaNova - Tous droits réservés</p>
                </div>
            </div>
        </body>
        </html>";
    }

    /**
     * Sauvegarde l'email dans un fichier si l'envoi échoue
     */
    private function saveEmailToFile($toEmail, $toName, $subject, $message) {
        try {
            $emailsDir = __DIR__ . '/../emails_backup';
            if (!is_dir($emailsDir)) {
                mkdir($emailsDir, 0755, true);
            }
            
            $filename = $emailsDir . '/smtp_email_' . date('Y-m-d_H-i-s') . '_' . md5($toEmail) . '.html';
            
            $emailContent = "<!DOCTYPE html>\n";
            $emailContent .= "<html><head><meta charset='UTF-8'><title>$subject</title></head><body>\n";
            $emailContent .= "<div style='background: #f0f0f0; padding: 20px; margin-bottom: 20px;'>\n";
            $emailContent .= "<h2>📧 Email SMTP - Sauvegarde</h2>\n";
            $emailContent .= "<p><strong>Destinataire:</strong> $toName &lt;$toEmail&gt;</p>\n";
            $emailContent .= "<p><strong>Sujet:</strong> $subject</p>\n";
            $emailContent .= "<p><strong>Date:</strong> " . date('Y-m-d H:i:s') . "</p>\n";
            $emailContent .= "<p><strong>Service:</strong> SMTP (Fallback)</p>\n";
            if ($this->lastError) {
                $emailContent .= "<p><strong>Erreur:</strong> " . htmlspecialchars($this->lastError) . "</p>\n";
            }
            $emailContent .= "</div>\n";
            $emailContent .= $message;
            $emailContent .= "\n</body></html>";
            
            file_put_contents($filename, $emailContent);
            
            error_log("📁 Email SMTP sauvegardé dans: $filename");
            
        } catch (Exception $e) {
            error_log("❌ Erreur sauvegarde email SMTP: " . $e->getMessage());
        }
    }
}

?>
