<?php

require_once __DIR__ . '/../models/Paiement.php';

class PaiementController {
    private $paiement;

    public function __construct($db) {
        $this->paiement = new Paiement($db);
    }

    public function getPaiements() {
        try {
            $paiements = $this->paiement->getAllWithDetails();
            echo json_encode([
                'success' => true,
                'data' => $paiements,
                'message' => 'Paiements récupérés avec succès'
            ]);
        } catch (Exception $e) {
            error_log("Erreur dans getPaiements: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'data' => [],
                'message' => 'Erreur lors de la récupération des paiements: ' . $e->getMessage()
            ]);
        }
    }

    public function getPaiement($id) {
        try {
            $paiement = $this->paiement->getByIdWithDetails($id);
            if ($paiement) {
                echo json_encode([
                    'success' => true,
                    'data' => $paiement,
                    'message' => 'Paiement récupéré avec succès'
                ]);
            } else {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'data' => null,
                    'message' => 'Paiement non trouvé'
                ]);
            }
        } catch (Exception $e) {
            error_log("Erreur dans getPaiement: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'data' => null,
                'message' => 'Erreur lors de la récupération du paiement: ' . $e->getMessage()
            ]);
        }
    }

    public function addPaiement() {
        try {
            $data = json_decode(file_get_contents("php://input"), true);
            
            // Validation des données
            $errors = $this->validatePaiementData($data);
            if (!empty($errors)) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Données invalides',
                    'errors' => $errors
                ]);
                return;
            }

            // Vérifier si un paiement existe déjà pour cet élève, ce mois et ce type
            if ($this->paiement->exists($data['id_eleve'], $data['id_annee_scolaire'], $data['type_paiement'], $data['mois'])) {
                http_response_code(409);
                echo json_encode([
                    'success' => false,
                    'message' => 'Un paiement de ce type existe déjà pour cet élève ce mois-ci'
                ]);
                return;
            }

            $paiementId = $this->paiement->create($data);
            
            if ($paiementId) {
                $paiement = $this->paiement->getByIdWithDetails($paiementId);
                echo json_encode([
                    'success' => true,
                    'data' => $paiement,
                    'message' => 'Paiement créé avec succès'
                ]);
            } else {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la création du paiement'
                ]);
            }
        } catch (Exception $e) {
            error_log("Erreur dans addPaiement: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la création du paiement: ' . $e->getMessage()
            ]);
        }
    }

    public function updatePaiement($id) {
        try {
            $data = json_decode(file_get_contents("php://input"), true);
            
            // Vérifier si le paiement existe
            $existingPaiement = $this->paiement->getById($id);
            if (!$existingPaiement) {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'message' => 'Paiement non trouvé'
                ]);
                return;
            }

            // Validation des données
            $errors = $this->validatePaiementData($data, $id);
            if (!empty($errors)) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Données invalides',
                    'errors' => $errors
                ]);
                return;
            }

            $success = $this->paiement->update($id, $data);
            
            if ($success) {
                $paiement = $this->paiement->getByIdWithDetails($id);
                echo json_encode([
                    'success' => true,
                    'data' => $paiement,
                    'message' => 'Paiement mis à jour avec succès'
                ]);
            } else {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la mise à jour du paiement'
                ]);
            }
        } catch (Exception $e) {
            error_log("Erreur dans updatePaiement: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la mise à jour du paiement: ' . $e->getMessage()
            ]);
        }
    }

    public function deletePaiement($id) {
        try {
            $paiement = $this->paiement->getById($id);
            if (!$paiement) {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'message' => 'Paiement non trouvé'
                ]);
                return;
            }

            $success = $this->paiement->delete($id);
            
            if ($success) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Paiement supprimé avec succès'
                ]);
            } else {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la suppression du paiement'
                ]);
            }
        } catch (Exception $e) {
            error_log("Erreur dans deletePaiement: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la suppression du paiement: ' . $e->getMessage()
            ]);
        }
    }

    /* Récupérer les statistiques des paiements */
    public function getStatistiques() {
        try {
            // Récupérer l'année scolaire depuis les paramètres GET
            $annee_scolaire = isset($_GET['annee_scolaire']) ? (int)$_GET['annee_scolaire'] : null;

            $stats = $this->paiement->getStatistiques($annee_scolaire);
            echo json_encode([
                'success' => true,
                'data' => $stats,
                'message' => 'Statistiques récupérées avec succès'
            ]);
        } catch (Exception $e) {
            error_log("Erreur dans getStatistiques: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la récupération des statistiques: ' . $e->getMessage()
            ]);
        }
    }

    /*  Récupérer les paiements d'un élève  */
    public function getPaiementsEleve($id_eleve) {
        try {
            $paiements = $this->paiement->getByEleve($id_eleve);
            echo json_encode([
                'success' => true,
                'data' => $paiements,
                'message' => 'Paiements de l\'élève récupérés avec succès'
            ]);
        } catch (Exception $e) {
            error_log("Erreur dans getPaiementsEleve: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la récupération des paiements de l\'élève: ' . $e->getMessage()
            ]);
        }
    }

    /** Vérifier les paiements mensuels */
    public function verifierPaiementsMensuels() {
        try {
            $mois = $_GET['mois'] ?? null;
            $annee = $_GET['annee'] ?? null;
            
            if (!$mois || !$annee) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Mois et année requis'
                ]);
                return;
            }

            $verification = $this->paiement->verifierPaiementsMensuels($mois, $annee);
            echo json_encode([
                'success' => true,
                'data' => $verification,
                'message' => 'Vérification effectuée avec succès'
            ]);
        } catch (Exception $e) {
            error_log("Erreur dans verifierPaiementsMensuels: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la vérification: ' . $e->getMessage()
            ]);
        }
    }

    /**  Générer les paiements mensuels */
    public function genererPaiementsMensuels() {
        try {
            $data = json_decode(file_get_contents("php://input"), true);
            
            $mois = $data['mois'] ?? null;
            $annee = $data['annee'] ?? null;
            $type_paiement = $data['type_paiement'] ?? 'scolarité';
            
            if (!$mois || !$annee) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Mois et année requis'
                ]);
                return;
            }

            $result = $this->paiement->genererPaiementsMensuels($mois, $annee, $type_paiement);
            echo json_encode([
                'success' => true,
                'data' => $result,
                'message' => 'Paiements générés avec succès'
            ]);
        } catch (Exception $e) {
            error_log("Erreur dans genererPaiementsMensuels: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la génération: ' . $e->getMessage()
            ]);
        }
    }

    /**  Valider les données d'un paiement  */
    private function validatePaiementData($data, $id = null) {
        $errors = [];

        if (empty($data['id_eleve'])) {
            $errors[] = 'L\'élève est requis';
        }

        if (empty($data['id_annee_scolaire'])) {
            $errors[] = 'L\'année scolaire est requise';
        }

        if (empty($data['montant']) || $data['montant'] <= 0) {
            $errors[] = 'Le montant doit être supérieur à 0';
        }

        if (empty($data['mois'])) {
            $errors[] = 'Le mois est requis';
        }

        if (empty($data['type_paiement'])) {
            $errors[] = 'Le type de paiement est requis';
        }

        if (empty($data['mode_paiement'])) {
            $errors[] = 'Le mode de paiement est requis';
        }

        if (empty($data['date_paiement'])) {
            $errors[] = 'La date de paiement est requise';
        }

        return $errors;
    }
}
