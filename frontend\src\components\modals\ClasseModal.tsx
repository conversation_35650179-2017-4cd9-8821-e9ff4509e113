import React, { useState, useEffect } from 'react';
import { X, Users } from 'lucide-react';
import Button from '../Button';
import Input from '../Input';
import Select from '../Select';
import { Classe, NiveauDetaille } from '../../types';

interface ClasseModalProps {
  classe?: Classe | null;
  niveaux: NiveauDetaille[];
  onSave: (data: any) => void;
  onClose: () => void;
}

const ClasseModal: React.FC<ClasseModalProps> = ({ classe, niveaux, onSave, onClose }) => {
  const [formData, setFormData] = useState({
    id_niveau: '',
    nom_classe: ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (classe) {
      setFormData({
        id_niveau: classe.id_niveau.toString(),
        nom_classe: classe.nom_classe
      });
    }
  }, [classe]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Effacer l'erreur pour ce champ
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.id_niveau) {
      newErrors.id_niveau = 'Le niveau est requis';
    }

    if (!formData.nom_classe.trim()) {
      newErrors.nom_classe = 'Le nom de la classe est requis';
    } else if (formData.nom_classe.trim().length < 2) {
      newErrors.nom_classe = 'Le nom doit contenir au moins 2 caractères';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      onSave({
        id_niveau: parseInt(formData.id_niveau),
        nom_classe: formData.nom_classe.trim()
      });
    }
  };

  const getNiveauOptions = () => {
    return niveaux.map(niveau => ({
      value: niveau.id_niveau.toString(),
      label: `${niveau.libelle} (${niveau.cycle})`
    }));
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-3">
            <Users className="h-6 w-6 text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-900">
              {classe ? 'Modifier la classe' : 'Ajouter une classe'}
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <Select
            name="id_niveau"
            label="Niveau *"
            value={formData.id_niveau}
            onChange={handleInputChange}
            options={[
              { value: '', label: 'Sélectionner un niveau' },
              ...getNiveauOptions()
            ]}
            error={errors.id_niveau}
            required
          />

          <Input
            name="nom_classe"
            label="Nom de la classe *"
            value={formData.nom_classe}
            onChange={handleInputChange}
            placeholder="Ex: 6ème A, CP1, Grande Section A..."
            error={errors.nom_classe}
            required
          />

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
            >
              Annuler
            </Button>
            <Button type="submit">
              {classe ? 'Modifier' : 'Ajouter'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ClasseModal;
