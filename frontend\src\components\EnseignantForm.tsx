
import React, { useState, useEffect } from "react";
import { PlusCircle, MinusCircle } from "lucide-react";
import Input from "./Input";
import Select from "./Select";
import { addEnseignant, addDiplome, addContrat, register, getDiplomesByEnseignant, getActiveContratByEnseignant, updateUser, updateEnseignant, updateDiplome, updateContrat, getMatieres, getMatieresEnseignant, assignMatieresEnseignant, updateMatieresEnseignant } from "../services/api";
import type { Di<PERSON>lo<PERSON>, Contrat, Enseignant, User, Matiere } from "../types";

interface EnseignantFormProps {
  onSuccess?: () => void;
  initialEnseignant?: Enseignant | null;
}

const EnseignantForm: React.FC<EnseignantFormProps> = ({ onSuccess, initialEnseignant }) => {
  const [step, setStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState<boolean | null>(null);
  const [submitMessage, setSubmitMessage] = useState("");
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  // Select options aligned with interfaces
  const sexeOptions = [
    { value: "homme", label: "Homme" },
    { value: "femme", label: "Femme" },
  ];

  const situationFamilialeOptions = [
    { value: "C", label: "Célibataire" },
    { value: "M", label: "Marié(e)" },
    { value: "D", label: "Divorcé(e)" },
    { value: "V", label: "Veuf/Veuve" },
  ];

  const typeContratOptions = [
    { value: "CDI", label: "CDI" },
    { value: "CDD", label: "CDD" },
    { value: "Vacataire", label: "Vacataire" },
    { value: "Autre", label: "Autre" },
  ];

  const posteOptions = [
    { value: "enseignant", label: "Enseignant" },
    { value: "administratif", label: "Administratif" },
    { value: "autre", label: "Autre" },
  ];

  const nationaliteOptions = [
    { value: "marocain", label: "Marocain(e)" },
    { value: "francais", label: "Français(e)" },
    { value: "espagnol", label: "Espagnol(e)" },
    { value: "algerien", label: "Algérien(ne)" },
    { value: "tunisien", label: "Tunisien(ne)" },
    { value: "autre", label: "Autre" },
  ];

  const [userEnseignant, setUserEnseignant] = useState<User>(
    initialEnseignant?.user || {
      id_utilisateur: 0,
      nom: "",
      prenom: "",
      email: "",
      mot_de_passe: "",
      role: "enseignant",
      sexe: null,
      date_naissance: "",
      lieu_naissance: "",
      nationalite: "",
      telephone: "",
      adresse: "",
      photo: "",
      est_valide: false,
      est_actif: true,
    }
  );

  const [enseignant, setEnseignant] = useState<Enseignant>(
    initialEnseignant || {
      id_enseignant: 0,
      id_utilisateur: 0,
      num_CIN: "",
      num_CNSS: "",
      situation_familiale: null,
      nombre_enfants: 0,
      date_embauche: "",
      banque: "",
      rib: "",
      user: undefined,
    }
  );

  const [diplomes, setDiplomes] = useState<Diplome[]>([
    {
      id_diplome: 0,
      id_enseignant: 0,
      intitule: "",
      institut: "",
      specialite: "",
      date_promotion: "",
    },
  ]);

  const [contrat, setContrat] = useState<Contrat>({
    id_contrat: 0,
    id_enseignant: 0,
    type_contrat: "CDD",
    poste: "enseignant",
    date_debut: "",
    date_fin: "",
    salaire_base: 5000, // Valeur par défaut non nulle
    statut: "actif",
    description: "",
    matieres: [], // Matières sélectionnées pour les enseignants
  });

  // Pour tracker les diplômes supprimés
  const [diplomesSupprimes, setDiplomesSupprimes] = useState<number[]>([]);

  // État pour les matières disponibles
  const [matieresDisponibles, setMatieresDisponibles] = useState<Matiere[]>([]);

  // Charger les matières disponibles
  useEffect(() => {
    const fetchMatieres = async () => {
      try {
        const response = await getMatieres();
        if (response.data.success && Array.isArray(response.data.data)) {
          setMatieresDisponibles(response.data.data);
        }
      } catch (error) {
        console.error("Erreur lors du chargement des matières:", error);
      }
    };

    fetchMatieres();
  }, []);

  // Charger les données des diplômes et du contrat pour la modification
  useEffect(() => {
    const loadEnseignantData = async () => {
      if (initialEnseignant && initialEnseignant.id_enseignant) {
        console.log("Chargement des données pour l'enseignant:", initialEnseignant.id_enseignant);

        let diplomesLoaded = false;
        let contratLoaded = false;

        try {
          // Charger les diplômes
          try {
            console.log(`Chargement des diplômes pour l'enseignant ${initialEnseignant.id_enseignant}`);
            const diplomesResponse = await getDiplomesByEnseignant(initialEnseignant.id_enseignant);
            console.log("Réponse diplômes:", diplomesResponse);

            if (diplomesResponse.data.success && diplomesResponse.data.data && diplomesResponse.data.data.length > 0) {
              setDiplomes(diplomesResponse.data.data);
              diplomesLoaded = true;
            } else {
              console.log("Aucun diplôme trouvé pour cet enseignant");
            }
          } catch (diplomesError) {
            console.warn("Erreur lors du chargement des diplômes:", diplomesError);
            // Ne pas faire échouer tout le chargement si les diplômes ne sont pas trouvés
          }

          // Charger le contrat actif
          try {
            console.log(`Chargement du contrat actif pour l'enseignant ${initialEnseignant.id_enseignant}`);
            const contratResponse = await getActiveContratByEnseignant(initialEnseignant.id_enseignant);
            console.log("Réponse contrat:", contratResponse);

            if (contratResponse.data.success && contratResponse.data.data) {
              const contratData = contratResponse.data.data;
              setContrat({
                ...contratData,
                matieres: [] // Sera chargé séparément
              });
              contratLoaded = true;

              // Charger les matières de l'enseignant si c'est un enseignant
              if (contratData.poste === "enseignant") {
                try {
                  const matieresResponse = await getMatieresEnseignant(initialEnseignant.id_enseignant);
                  if (matieresResponse.data.success && Array.isArray(matieresResponse.data.data)) {
                    const matieresIds = matieresResponse.data.data.map((m: any) => m.id_matiere);
                    setContrat(prev => ({
                      ...prev,
                      matieres: matieresIds
                    }));
                  }
                } catch (error) {
                  console.error("Erreur lors du chargement des matières de l'enseignant:", error);
                }
              }
            } else {
              console.log("Aucun contrat actif trouvé pour cet enseignant");
            }
          } catch (contratError) {
            console.warn("Erreur lors du chargement du contrat:", contratError);
            // Ne pas faire échouer tout le chargement si le contrat n'est pas trouvé
          }

          // Log du chargement des données (sans affichage à l'utilisateur)
          let message = "Données de base chargées";
          if (diplomesLoaded && contratLoaded) {
            message = "Toutes les données chargées avec succès";
          } else if (diplomesLoaded) {
            message = "Données de base et diplômes chargés";
          } else if (contratLoaded) {
            message = "Données de base et contrat chargés";
          }

          // Données chargées avec succès
        } catch (error) {
          console.error("Erreur générale lors du chargement des données:", error);
          // Ne pas afficher d'erreur à l'utilisateur lors du chargement initial
        }
      }
    };

    loadEnseignantData();
  }, [initialEnseignant]);

  const validateForm = (currentStep: number) => {
    const newErrors: { [key: string]: string } = {};

    if (currentStep === 1) {
      // Step 1: Informations personnelles
      if (!userEnseignant.nom.trim()) newErrors.nom = "Le nom est requis.";
      if (!userEnseignant.prenom.trim()) newErrors.prenom = "Le prénom est requis.";
      if (!userEnseignant.sexe) newErrors.sexe = "Le sexe est requis.";
      if (!userEnseignant.date_naissance) newErrors.date_naissance = "La date de naissance est requise.";
      if (!userEnseignant.lieu_naissance.trim()) newErrors.lieu_naissance = "Le lieu de naissance est requis.";
      if (!userEnseignant.email.trim()) newErrors.email = "L'email est requis.";
      else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(userEnseignant.email))
        newErrors.email = "L'email n'est pas valide.";
      if (!userEnseignant.telephone.trim()) newErrors.telephone = "Le téléphone est requis.";
      if (!enseignant.num_CIN.trim()) newErrors.num_CIN = "Le numéro de CIN est requis.";
      else if (!/^[A-Z]{1,2}[0-9]{5,6}$/.test(enseignant.num_CIN))
        newErrors.num_CIN = "Le numéro de CIN est invalide.";
      if (!enseignant.situation_familiale) newErrors.situation_familiale = "La situation familiale est requise.";

      // Validation du nombre d'enfants seulement si la situation n'est pas célibataire
      if (enseignant.situation_familiale !== "C") {
        if (enseignant.nombre_enfants === null || enseignant.nombre_enfants < 0) {
          newErrors.nombre_enfants = "Le nombre d'enfants doit être supérieur ou égal à 0.";
        }
      }
      if (!enseignant.date_embauche) newErrors.date_embauche = "La date d'embauche est requise.";
    } else if (currentStep === 2) {
      // Step 2: Diplômes - validation optionnelle car les diplômes ne sont pas obligatoires
      diplomes.forEach((diplome, index) => {
        if (!diplome.intitule.trim()) newErrors[`diplome_intitule_${index}`] = "L'intitule est requis.";         
        if (!diplome.institut.trim()) newErrors[`diplome_institut_${index}`] = "L'institut est requis.";
        if (!diplome.specialite.trim()) newErrors[`diplome_specialite_${index}`] = "La spécialité est requise.";
        if (!diplome.date_promotion) newErrors[`diplome_date_promotion_${index}`] = "La date d'obtention est requise.";
      });
    } else if (currentStep === 3) {
      // Step 3: Contrat
      if (!contrat.type_contrat) newErrors.type_contrat = "Le type de contrat est requis.";
      if (!contrat.poste) newErrors.poste = "Le poste est requis.";
      if (!contrat.date_debut) newErrors.date_debut = "La date de début du contrat est requise.";
      if (!contrat.salaire_base || contrat.salaire_base <= 0) newErrors.salaire_base = "Le salaire de base doit être supérieur à 0.";

      // Validation des matières pour les enseignants
      if (contrat.poste === "enseignant" && (!contrat.matieres || contrat.matieres.length === 0)) {
        newErrors.matieres = "Veuillez sélectionner au moins une matière pour un enseignant.";
      }
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNextStep = () => {
    if (validateForm(step)) {
      setStep(step + 1);
    } else {
      setSubmitMessage("Veuillez remplir tous les champs requis avant de continuer.");
      setSubmitSuccess(false);
    }
  };

  const handleEnseignantChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setErrors((prev) => ({ ...prev, [name]: "" }));

    if (["num_CIN", "num_CNSS", "situation_familiale", "nombre_enfants", "date_embauche", "banque", "rib"].includes(name)) {
      setEnseignant((prev) => {
        const updatedEnseignant = {
          ...prev,
          [name]: name === "nombre_enfants" ? (value ? parseInt(value) : 0) : value,
        };

        // Si la situation familiale devient "célibataire", remettre le nombre d'enfants à 0
        if (name === "situation_familiale" && value === "C") {
          updatedEnseignant.nombre_enfants = 0;
        }

        return updatedEnseignant;
      });
    } else {
      setUserEnseignant((prev) => {
        const updatedUser = { ...prev, [name]: value };
        return updatedUser;
      });
    }
  };

  const handleDiplomeChange = (index: number, field: keyof Diplome, value: string) => {
    setErrors((prev) => ({ ...prev, [`diplome_${field}_${index}`]: "" }));
    const newDiplomes = [...diplomes];
    newDiplomes[index] = { ...newDiplomes[index], [field]: value };
    setDiplomes(newDiplomes);
  };

  const handleContratChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setErrors((prev) => ({ ...prev, [name]: "" }));
    setContrat((prev) => ({
      ...prev,
      [name]: name === "salaire_base" ? (value ? parseFloat(value) : 0) : value,
    }));
  };

  const addDiplomeField = () => {
    setDiplomes([
      ...diplomes,
      {
        id_diplome: 0,
        id_enseignant: 0,
        intitule: "",
        institut: "",
        specialite: "",
        date_promotion: "",
      },
    ]);
  };

  const removeDiplome = (index: number) => {
    setDiplomes(diplomes.filter((_, i) => i !== index));
  };



  const resetForm = () => {
    setUserEnseignant({
      id_utilisateur: 0,
      nom: "",
      prenom: "",
      email: "",
      mot_de_passe: "",
      role: "enseignant",
      sexe: null,
      date_naissance: "",
      lieu_naissance: "",
      nationalite: "",
      telephone: "",
      adresse: "",
      photo: "",
      est_valide: false,
      est_actif: true,
    });
    setEnseignant({
      id_enseignant: 0,
      id_utilisateur: 0,
      num_CIN: "",
      num_CNSS: "",
      situation_familiale: null,
      nombre_enfants: 0,
      date_embauche: "",
      banque: "",
      rib: "",
    });
    setDiplomes([
      {
        id_diplome: 0,
        id_enseignant: 0,
        intitule: "",
        institut: "",
        specialite: "",
        date_promotion: "",
      },
    ]);
    setContrat({
      id_contrat: 0,
      id_enseignant: 0,
      type_contrat: "CDD",
      poste: "enseignant",
      date_debut: "",
      date_fin: "",
      salaire_base: 5000, // Valeur par défaut non nulle
      statut: "actif",
      description: "",
      matieres: [],
    });
    setStep(1);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log("🔥 handleSubmit appelé - étape:", step, "isSubmitting:", isSubmitting);
    console.trace("Stack trace pour voir d'où vient l'appel de handleSubmit");

    // Empêcher la soumission si on n'est pas à l'étape 3 ou si déjà en cours de soumission
    if (step !== 3 || isSubmitting) {
      console.log("Soumission bloquée - étape:", step, "isSubmitting:", isSubmitting);
      return;
    }

    // Valider toutes les étapes avant soumission
    if (!validateForm(1) || !validateForm(2) || !validateForm(3)) {
      setSubmitMessage("Veuillez remplir tous les champs requis.");
      setSubmitSuccess(false);
      return;
    }
    setIsSubmitting(true);
    setErrors({});

    try {
      const isEditing = initialEnseignant && initialEnseignant.id_enseignant > 0;
      let enseignantId: number;

      if (isEditing) {
        // Mode modification
        enseignantId = initialEnseignant.id_enseignant;
        setSubmitMessage("Modification en cours...");

        // 1. Mettre à jour l'utilisateur
        if (initialEnseignant.user) {
          const userData = {
            ...userEnseignant,
            id_utilisateur: initialEnseignant.user.id_utilisateur
          };
          console.log("Mise à jour utilisateur:", userData);
          const updatedUser = await updateUser(initialEnseignant.user.id_utilisateur, userData);
          if (!updatedUser.data.success) {
            throw new Error("Erreur lors de la mise à jour de l'utilisateur");
          }
        }

        // 2. Mettre à jour l'enseignant
        const enseignantData = {
          num_CIN: enseignant.num_CIN.trim(),
          num_CNSS: enseignant.num_CNSS?.trim() || null,
          situation_familiale: enseignant.situation_familiale,
          nombre_enfants: enseignant.situation_familiale === "C" ? null : parseInt(enseignant.nombre_enfants?.toString() || "0"),
          date_embauche: enseignant.date_embauche,
          banque: enseignant.banque?.trim() || null,
          rib: enseignant.rib?.trim() || null,
        };
        console.log("Mise à jour enseignant:", enseignantData);
        console.log("ID enseignant à modifier:", initialEnseignant.id_enseignant);

        try {
          const updatedEnseignant = await updateEnseignant(initialEnseignant.id_enseignant, enseignantData);
          console.log("Réponse mise à jour enseignant:", updatedEnseignant);
          if (!updatedEnseignant.data.success) {
            throw new Error(updatedEnseignant.data.message || "Erreur lors de la mise à jour de l'enseignant");
          }
        } catch (enseignantError: any) {
          console.error("Erreur spécifique lors de la mise à jour de l'enseignant:", enseignantError);
          console.error("Détails de l'erreur enseignant:", {
            status: enseignantError.response?.status,
            data: enseignantError.response?.data,
            message: enseignantError.message
          });
          throw enseignantError;
        }
      } else {
        // Mode création
        // 1. Créer l'utilisateur
        const { data: createdUser } = await register(userEnseignant);
        console.log("User created:", createdUser);

        if (!createdUser.success || !createdUser.id_user) {
          throw new Error("Erreur lors de la création de l'utilisateur");
        }

        // Vérifier que l'ID utilisateur est valide
        const userId = parseInt(createdUser.id_user);
        if (isNaN(userId)) {
          throw new Error(`ID utilisateur invalide: ${createdUser.id_user}`);
        }
        console.log("createdUser.id_user:", createdUser.id_user);

        // 2. Créer l'enseignant avec l'ID utilisateur
        const enseignantData = {
          id_utilisateur: userId,
          num_CIN: enseignant.num_CIN.trim(),
          num_CNSS: enseignant.num_CNSS?.trim() || null, // Null si vide car CNSS est optionnel
          situation_familiale: enseignant.situation_familiale,
          nombre_enfants: enseignant.situation_familiale === "C" ? null : parseInt(enseignant.nombre_enfants?.toString() || "0"),
          date_embauche: enseignant.date_embauche,
          banque: enseignant.banque?.trim() || null,
          rib: enseignant.rib?.trim() || null,
        };

        console.log("Données enseignant à envoyer:", enseignantData);

        const createdEnseignant = await addEnseignant(enseignantData);
        console.log("Enseignant created:", createdEnseignant);

        if (!createdEnseignant.data.success || !createdEnseignant.data.id_enseignant) {
          throw new Error("Erreur lors de la création de l'enseignant");
        }

        enseignantId = createdEnseignant.data.id_enseignant;
      }

      // 3. Gérer les diplômes (créer ou mettre à jour)
      let allDiplomesOk = true;
      for (const diplome of diplomes) {
        if (diplome.intitule.trim()) {
          const diplomeData = {
            ...diplome,
            id_enseignant: enseignantId,
          };
          console.log("Données diplôme:", diplomeData);

          try {
            if (isEditing && diplome.id_diplome && diplome.id_diplome > 0) {
              // Mise à jour du diplôme existant
              const updatedDiplome = await updateDiplome(diplome.id_diplome, diplomeData);
              if (!updatedDiplome.data.success) {
                allDiplomesOk = false;
                break;
              }
            } else {
              // Création d'un nouveau diplôme
              const createdDiplome = await addDiplome(diplomeData);
              if (!createdDiplome.data.success) {
                allDiplomesOk = false;
                break;
              }
            }
          } catch (diplomeError) {
            console.error("Erreur lors de la gestion du diplôme:", diplomeError);
            allDiplomesOk = false;
            break;
          }
        }
      }

      // 4. Créer le contrat
      const contratData = {
        ...contrat,
        id_enseignant: enseignantId,
        // S'assurer que les valeurs numériques sont correctes
        salaire_base: parseFloat(contrat.salaire_base?.toString() || "0"),
        // S'assurer que les champs optionnels sont gérés
        date_fin: contrat.date_fin || null,
        description: contrat.description || null,
        statut: contrat.statut || 'actif'
      };
      console.log("Contrat data:", contratData);

      try {
        let contratResult;
        console.log("Gestion du contrat - isEditing:", isEditing, "contrat.id_contrat:", contrat.id_contrat);

        if (isEditing && contrat.id_contrat && contrat.id_contrat > 0) {
          // Mise à jour du contrat existant
          console.log("Mise à jour du contrat existant avec ID:", contrat.id_contrat);
          contratResult = await updateContrat(contrat.id_contrat, contratData);
          console.log("Contrat updated:", contratResult);
        } else {
          // Création d'un nouveau contrat
          console.log("Création d'un nouveau contrat pour l'enseignant:", enseignantId);
          contratResult = await addContrat(contratData);
          console.log("Contrat created:", contratResult);
        }

        console.log("Résultat contrat:", contratResult);
        if (!contratResult.data.success) {
          console.error("Échec de l'opération contrat:", contratResult.data);
          throw new Error(contratResult.data.message || `Erreur lors de la ${isEditing && contrat.id_contrat > 0 ? 'mise à jour' : 'création'} du contrat`);
        }

        // 5. Gérer les affectations de matières pour les enseignants
        if (contrat.poste === "enseignant" && contrat.matieres && contrat.matieres.length > 0) {
          try {
            if (isEditing) {
              await updateMatieresEnseignant(enseignantId, contrat.matieres);
            } else {
              await assignMatieresEnseignant(enseignantId, contrat.matieres);
            }
            console.log("Matières affectées avec succès");
          } catch (matiereError) {
            console.error("Erreur lors de l'affectation des matières:", matiereError);
            // Ne pas faire échouer toute l'opération pour les matières
          }
        }

        if (allDiplomesOk) {
          setSubmitSuccess(true);
          setSubmitMessage(isEditing ? "Enseignant modifié avec succès !" : "Enseignant ajouté avec succès !");
          // Appeler onSuccess immédiatement comme pour les élèves
          console.log("🎉 SUCCÈS COMPLET - Appel de onSuccess depuis handleSubmit");
          if (onSuccess) onSuccess();
        } else {
          setSubmitSuccess(false);
          setSubmitMessage(isEditing
            ? "Enseignant et contrat modifiés, mais erreur lors de la modification des diplômes"
            : "Enseignant et contrat créés, mais erreur lors de l'ajout des diplômes"
          );
        }
      } catch (contratError: any) {
        console.error("Erreur lors de la création du contrat:", contratError);
        setSubmitSuccess(false);

        let errorMessage = "Erreur lors de la création du contrat";
        if (contratError.response?.data?.message) {
          errorMessage = contratError.response.data.message;
        } else if (contratError.message) {
          errorMessage = contratError.message;
        }

        setSubmitMessage(`Enseignant ${isEditing ? 'modifié' : 'créé'} mais ${errorMessage}`);
      }
    } catch (error: any) {
      console.error("Erreur lors de la soumission:", error);
      console.error("Détails de l'erreur:", {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message
      });
      setSubmitSuccess(false);

      // Essayer d'extraire le message d'erreur du backend
      let errorMessage = "Erreur lors de la requête";
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.data) {
        errorMessage = JSON.stringify(error.response.data);
      } else if (error.message) {
        errorMessage = error.message;
      }

      setSubmitMessage(`${isEditing ? 'Modification' : 'Création'} échouée: ${errorMessage}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const isEditing = initialEnseignant && initialEnseignant.id_enseignant > 0;

  return (
    <div className="max-w-4xl mx-auto my-8 bg-white rounded-xl shadow-md overflow-hidden">

      {/* Progress Indicator */}
      <div className="bg-gray-50 px-6 py-4 flex border-b text-center">
        <div className="flex-1 flex flex-col items-center">
          <div className={`w-6 h-6 rounded-full flex items-center justify-center ${step >= 1 ? "bg-blue-600 text-white" : "bg-gray-200 text-gray-600"}`}>1</div>
          <span className="text-xs mt-1">Informations personnelles</span>
        </div>
        <div className="flex-1 flex flex-col items-center">
          <div className={`w-6 h-6 rounded-full flex items-center justify-center ${step >= 2 ? "bg-blue-600 text-white" : "bg-gray-200 text-gray-600"}`}>2</div>
          <span className="text-xs mt-1">Diplômes</span>
        </div>
        <div className="flex-1 flex flex-col items-center">
          <div className={`w-6 h-6 rounded-full flex items-center justify-center ${step >= 3 ? "bg-blue-600 text-white" : "bg-gray-200 text-gray-600"}`}>3</div>
          <span className="text-xs mt-1">Contrat</span>
        </div>
      </div>

      {/* Success/Error message */}
      {submitMessage && (
        <div className={`p-4 ${submitSuccess ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`}>
          {submitMessage}
        </div>
      )}

      <form onSubmit={(e) => {
        e.preventDefault();
        console.log("🛑 Soumission de formulaire bloquée - étape:", step);
        // Ne permettre la soumission que si on est à l'étape 3 ET que ce n'est pas automatique
        if (step === 3) {
          console.log("⚠️ Tentative de soumission à l'étape 3 - mais bloquée pour debug");
        }
      }} className="space-y-6 p-6" onKeyDown={(e) => {
        // Empêcher la soumission par la touche Entrée
        if (e.key === 'Enter') {
          console.log("🚫 Touche Entrée bloquée - étape:", step);
          e.preventDefault();
        }
      }}>
        {/* Step 1: Personal Information */}
        {step === 1 && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Input
              name="nom"
              required
              label="Nom"
              value={userEnseignant.nom || ""}
              onChange={handleEnseignantChange}
              error={errors.nom}
            />
            <Input
              name="prenom"
              required
              label="Prénom"
              value={userEnseignant.prenom || ""}
              onChange={handleEnseignantChange}
              error={errors.prenom}
            />
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Sexe<span className="text-red-500 font-bold"> *</span></label>
              <div className="flex space-x-6 items-center">
                {sexeOptions.map((option) => (
                  <label key={option.value} className="flex items-center">
                    <input
                      type="radio"
                      name="sexe"
                      value={option.value}
                      checked={userEnseignant.sexe === option.value}
                      onChange={handleEnseignantChange}
                      className="mr-2"
                    />
                    {option.label}
                  </label>
                ))}
              </div>
              {errors.sexe && <p className="text-red-500 text-sm mt-1">{errors.sexe}</p>}
            </div>
            <Input
              type="date"
              name="date_naissance"
              required
              label="Date de naissance"
              value={userEnseignant.date_naissance || ""}
              onChange={handleEnseignantChange}
              error={errors.date_naissance}
            />
            <Input
              name="lieu_naissance"
              required
              label="Lieu de naissance"
              value={userEnseignant.lieu_naissance || ""}
              onChange={handleEnseignantChange}
              error={errors.lieu_naissance}
            />
            <Input
              name="num_CIN"
              required
              label="CIN"
              value={enseignant.num_CIN || ""}
              onChange={handleEnseignantChange}
              error={errors.num_CIN}
            />
            
            <Input
              type="email"
              name="email"
              required
              label="Email"
              value={userEnseignant.email || ""}
              onChange={handleEnseignantChange}
              error={errors.email}
            />
            <Input
              name="telephone"
              required
              label="Téléphone"
              value={userEnseignant.telephone || ""}
              onChange={handleEnseignantChange}
              error={errors.telephone}
            />
            <Input
              name="num_CNSS"
              label="Numéro CNSS"
              value={enseignant.num_CNSS || ""}
              onChange={handleEnseignantChange}
              error={errors.num_CNSS}
            />
            <Select
              name="nationalite"
              label="Nationalité"
              placeholder="Sélectionner une nationalité"
              options={nationaliteOptions}
              value={userEnseignant.nationalite ?? ""}
              onChange={handleEnseignantChange}
              error={errors.nationalite}
            />
            
            <Select
              name="situation_familiale"
              required
              label="Situation familiale"
              placeholder="Sélectionner une situation"
              options={situationFamilialeOptions}
              value={enseignant.situation_familiale ?? ""}
              onChange={handleEnseignantChange}
              error={errors.situation_familiale}
            />
            <Input
              type="number"
              name="nombre_enfants"
              required
              label="Nombre d'enfants"
              value={enseignant.situation_familiale === "C" ? "0" : (enseignant.nombre_enfants?.toString() || "")}
              onChange={handleEnseignantChange}
              min="0"
              disabled={enseignant.situation_familiale === "C"}
              error={errors.nombre_enfants}
            />
            <Input
              type="date"
              name="date_embauche"
              required
              label="Date d'embauche"
              value={enseignant.date_embauche || ""}
              onChange={handleEnseignantChange}
              error={errors.date_embauche}
            />
            <Input
              name="banque"
              label="Banque"
              value={enseignant.banque || ""}
              onChange={handleEnseignantChange}
              error={errors.banque}
            />
            <Input
              name="rib"
              label="RIB"
              value={enseignant.rib || ""}
              onChange={handleEnseignantChange}
              error={errors.rib}
            />
            <Input
              name="adresse"
              label="Adresse"
              value={userEnseignant.adresse || ""}
              onChange={handleEnseignantChange}
              error={errors.adresse}
            />
          </div>
        )}

        {/* Step 2: Diplomas */}
        {step === 2 && (
          <div className="space-y-6">
            <h2 className="text-lg font-medium">Diplômes</h2>
            {diplomes.map((diplome, index) => (
              <div key={index} className="p-4 border rounded-lg space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-md font-medium">Diplôme {index + 1}</h3>
                  {diplomes.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeDiplome(index)}
                      className="text-red-600 hover:text-red-800"
                    >
                      <MinusCircle className="h-5 w-5" />
                    </button>
                  )}
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    name={`diplome-${index}-intitule`}
                    label="Intitulé"
                    value={diplome.intitule || ""}
                    onChange={(e) => handleDiplomeChange(index, "intitule", e.target.value)}
                    error={errors[`diplome_intitule_${index}`]}
                  />
                  <Input
                    name={`diplome-${index}-institut`}
                    label="Institut"
                    value={diplome.institut || ""}
                    onChange={(e) => handleDiplomeChange(index, "institut", e.target.value)}
                    error={errors[`diplome_institut_${index}`]}
                  />
                  <Input
                    name={`diplome-${index}-specialite`}
                    label="Spécialité"
                    value={diplome.specialite || ""}
                    onChange={(e) => handleDiplomeChange(index, "specialite", e.target.value)}
                    error={errors[`diplome_specialite_${index}`]}
                  />
                  <Input
                    type="date"
                    name={`diplome-${index}-date`}
                    label="Date d'obtention"
                    value={diplome.date_promotion || ""}
                    onChange={(e) => handleDiplomeChange(index, "date_promotion", e.target.value)}
                    error={errors[`diplome_date_promotion_${index}`]}
                  />
                </div>
              </div>
            ))}
            <button
              type="button"
              onClick={addDiplomeField}
              className="flex items-center text-blue-600 hover:text-blue-800"
            >
              <PlusCircle className="h-5 w-5 mr-2" />
              Ajouter un diplôme
            </button>
          </div>
        )}

        {/* Step 3: Contract */}
        {step === 3 && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Select
              name="type_contrat"
              required
              label="Type de contrat"
              placeholder="Sélectionner le type de contrat"
              options={typeContratOptions}
              value={contrat.type_contrat || ""}
              onChange={handleContratChange}
              error={errors.type_contrat}
            />
            <Select
              name="poste"
              required
              label="Poste"
              options={posteOptions}
              value={contrat.poste || ""}
              onChange={handleContratChange}
              error={errors.poste}
            />
            <Input
              type="date"
              name="date_debut"
              required
              label="Date de début"
              value={contrat.date_debut || ""}
              onChange={handleContratChange}
              error={errors.date_debut}
            />
            <Input
              type="date"
              name="date_fin"
              label="Date de fin"
              value={contrat.date_fin || ""}
              onChange={handleContratChange}
              error={errors.date_fin}
            />
            <Input
              type="number"
              name="salaire_base"
              required
              label="Salaire de base"
              value={contrat.salaire_base?.toString() || ""}
              onChange={handleContratChange}
              min="0"
              step="0.01"
              error={errors.salaire_base}
            />
            <div className="col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea
                name="description"
                value={contrat.description || ""}
                onChange={handleContratChange}
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                rows={4}
              />
            </div>

            {/* Section des matières pour les enseignants */}
            {contrat.poste === "enseignant" && (
              <div className="col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Matières enseignées <span className="text-red-500">*</span>
                </label>
                <select
                  multiple
                  value={contrat.matieres?.map(String) || []}
                  onChange={(e) => {
                    const selectedValues = Array.from(e.target.selectedOptions, option => parseInt(option.value));
                    setContrat(prev => ({
                      ...prev,
                      matieres: selectedValues
                    }));
                  }}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 min-h-[120px]"
                  size={6}
                >
                  {matieresDisponibles.map((matiere) => (
                    <option
                      key={matiere.id_matiere}
                      value={matiere.id_matiere}
                      className="py-2 px-3"
                    >
                      {matiere.nom_matiere_fr} - {matiere.nom_matiere_ar}
                    </option>
                  ))}
                </select>
                <p className="text-xs text-gray-500 mt-1">
                  Maintenez Ctrl (ou Cmd sur Mac) pour sélectionner plusieurs matières
                </p>
                {contrat.poste === "enseignant" && (!contrat.matieres || contrat.matieres.length === 0) && (
                  <p className="text-red-500 text-sm mt-1">
                    Veuillez sélectionner au moins une matière pour un enseignant.
                  </p>
                )}
              </div>
            )}
          </div>
        )}

        {/* Navigation Buttons */}
        <div className="flex justify-between pt-4">
          {step > 1 && (
            <button
              type="button"
              onClick={() => setStep(step - 1)}
              className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
            >
              Précédent
            </button>
          )}
          <div className="ml-auto">
            {step < 3 ? (
              <button
                type="button"
                onClick={handleNextStep}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Suivant
              </button>
            ) : (
              <button
                type="button"
                disabled={isSubmitting}
                onClick={async (e) => {
                  console.log("🎯 Clic EXPLICITE sur le bouton de soumission");
                  await handleSubmit(e as any);
                }}
                className={`px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors ${
                  isSubmitting ? "opacity-70 cursor-not-allowed" : ""
                }`}
              >
                {isSubmitting
                  ? (isEditing ? "Modification..." : "Création...")
                  : (isEditing ? "Modifier l'enseignant" : "Créer l'enseignant")
                }
              </button>
            )}
          </div>
        </div>
      </form>
    </div>
  );
};

export default EnseignantForm;