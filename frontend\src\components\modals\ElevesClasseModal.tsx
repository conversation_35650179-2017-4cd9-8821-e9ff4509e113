import React, { useState, useEffect } from 'react';
import { X, Users, User, Mail, Phone, Calendar } from 'lucide-react';
import Button from '../Button';
import { Classe } from '../../types';
import { getClasseEleves } from '../../services/api';

interface ElevesClasseModalProps {
  classe: Classe;
  onClose: () => void;
}

interface EleveClasse {
  id_eleve: number;
  nom: string;
  prenom: string;
  email?: string;
  telephone?: string;
  date_inscription: string;
  statut: string;
  code_massar?: string;
}

const ElevesClasseModal: React.FC<ElevesClasseModalProps> = ({ classe, onClose }) => {
  const [eleves, setEleves] = useState<EleveClasse[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadEleves();
  }, [classe.id_classe]);

  const loadEleves = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getClasseEleves(classe.id_classe);
      if (response.data.success) {
        setEleves(response.data.data);
      } else {
        setError('Erreur lors du chargement des élèves');
      }
    } catch (error) {
      console.error('Erreur lors du chargement des élèves:', error);
      setError('Erreur lors du chargement des élèves');
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  const getStatutColor = (statut: string) => {
    switch (statut.toLowerCase()) {
      case 'inscrit':
        return 'bg-green-100 text-green-800';
      case 'transféré':
        return 'bg-yellow-100 text-yellow-800';
      case 'abandonné':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-3">
            <Users className="h-6 w-6 text-blue-600" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Élèves de la classe {classe.nom_classe}
              </h2>
              <p className="text-sm text-gray-500">
                {classe.niveau_libelle} • {classe.cycle}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {isLoading ? (
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-600">Chargement...</span>
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <div className="text-red-600 mb-2">{error}</div>
              <Button onClick={loadEleves} variant="outline">
                Réessayer
              </Button>
            </div>
          ) : eleves.length === 0 ? (
            <div className="text-center py-8">
              <Users className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Aucun élève inscrit</h3>
              <p className="mt-1 text-sm text-gray-500">
                Cette classe n'a pas encore d'élèves inscrits.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Statistiques */}
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Users className="h-5 w-5 text-blue-600" />
                    <span className="font-medium text-blue-900">
                      {eleves.length} élève{eleves.length > 1 ? 's' : ''} inscrit{eleves.length > 1 ? 's' : ''}
                    </span>
                  </div>
                  <div className="text-sm text-blue-700">
                    Classe: {classe.nom_classe}
                  </div>
                </div>
              </div>

              {/* Liste des élèves */}
              <div className="grid gap-4">
                {eleves.map((eleve) => (
                  <div key={eleve.id_eleve} className="border rounded-lg p-4 hover:bg-gray-50">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-3">
                        <div className="bg-blue-100 p-2 rounded-full">
                          <User className="h-4 w-4 text-blue-600" />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900">
                            {eleve.prenom} {eleve.nom}
                          </h4>
                          {eleve.code_massar && (
                            <p className="text-sm text-gray-500">
                              Code Massar: {eleve.code_massar}
                            </p>
                          )}
                          <div className="mt-2 space-y-1">
                            {eleve.email && (
                              <div className="flex items-center space-x-2 text-sm text-gray-600">
                                <Mail className="h-3 w-3" />
                                <span>{eleve.email}</span>
                              </div>
                            )}
                            {eleve.telephone && (
                              <div className="flex items-center space-x-2 text-sm text-gray-600">
                                <Phone className="h-3 w-3" />
                                <span>{eleve.telephone}</span>
                              </div>
                            )}
                            <div className="flex items-center space-x-2 text-sm text-gray-600">
                              <Calendar className="h-3 w-3" />
                              <span>Inscrit le {formatDate(eleve.date_inscription)}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatutColor(eleve.statut)}`}>
                        {eleve.statut}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t bg-gray-50">
          <Button onClick={onClose}>
            Fermer
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ElevesClasseModal;
