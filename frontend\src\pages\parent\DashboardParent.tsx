import React, { useState, useEffect } from 'react';
import { Calendar, <PERSON><PERSON>hart, BookOpen, MessageSquare, User, Activity, DollarSign, Clock, AlertCircle, TrendingUp } from 'lucide-react';
import Card from '../../components/Card';
import Button from '../../components/Button';
import EmploiTempsParent from '../../components/EmploiTempsParent';
import { getElevesByParent, getActivites, getEleves } from '../../services/api';
import type { Eleve, Activite } from '../../types';

interface DashboardStats {
  total_enfants: number;
  activites_inscrites: number;
  moyenne_generale: number;
  taux_presence: number;
}

const DashboardParent: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [enfants, setEnfants] = useState<Eleve[]>([]);
  const [activites, setActivites] = useState<Activite[]>([]);
  const [selectedEnfant, setSelectedEnfant] = useState<Eleve | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>("");
  const [showEmploiTemps, setShowEmploiTemps] = useState(false);

  useEffect(() => {
    console.log("🚀 useEffect déclenché");
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    setLoading(true);
    setError("");

    try {
      console.log("🔄 Chargement dashboard parent...");

      console.log("📊 Chargement des données parent...");

      // Essayer de récupérer les vrais enfants du parent
      try {
        const elevesRes = await getElevesByParent();
        console.log("📡 Réponse API élèves:", elevesRes);

        if (elevesRes.data?.success && elevesRes.data?.data?.length > 0) {
          const mesEnfants = elevesRes.data.data;
          console.log("👨‍👩‍👧‍👦 Enfants récupérés de la DB:", mesEnfants);

          setEnfants(mesEnfants);
          setSelectedEnfant(mesEnfants[0] || null);

          console.log("✅ État mis à jour - enfants:", mesEnfants.length, "sélectionné:", mesEnfants[0]);
        } else {
          console.log("⚠️ Aucun enfant trouvé pour ce parent");
          // Fallback : utiliser les élèves existants (pour test)
          const tousElevesRes = await getEleves();
          if (tousElevesRes.data?.success && tousElevesRes.data?.data?.length > 0) {
            const premierEleve = tousElevesRes.data.data[0];
            console.log("🔄 Utilisation du premier élève comme fallback:", premierEleve);
            setEnfants([premierEleve]);
            setSelectedEnfant(premierEleve);
          } else {
            console.log("❌ Aucun élève dans la base de données");
            setEnfants([]);
            setSelectedEnfant(null);
          }
        }
      } catch (error) {
        console.error("💥 Erreur lors de la récupération des enfants:", error);

        // En cas d'erreur, essayer de récupérer tous les élèves
        try {
          const tousElevesRes = await getEleves();
          if (tousElevesRes.data?.success && tousElevesRes.data?.data?.length > 0) {
            const premierEleve = tousElevesRes.data.data[0];
            console.log("🔄 Fallback - utilisation du premier élève:", premierEleve);
            setEnfants([premierEleve]);
            setSelectedEnfant(premierEleve);
          } else {
            setEnfants([]);
            setSelectedEnfant(null);
          }
        } catch (fallbackError) {
          console.error("💥 Erreur fallback:", fallbackError);
          setEnfants([]);
          setSelectedEnfant(null);
        }
      }

      // Calculer les statistiques basées sur les vrais enfants
      setStats({
        total_enfants: enfants.length,
        activites_inscrites: 0, // À récupérer via API
        moyenne_generale: 0, // À récupérer via API
        taux_presence: 0 // À récupérer via API
      });

      console.log("✅ Dashboard parent chargé avec succès");

    } catch (error) {
      console.error("💥 Erreur lors du chargement du dashboard:", error);
      setError("Erreur lors du chargement des données - Mode dégradé activé");

      // Mode dégradé : données minimales
      setStats({
        total_enfants: 0,
        activites_inscrites: 0,
        moyenne_generale: 0,
        taux_presence: 0
      });
    } finally {
      setLoading(false);
    }
  };

  const StatCard = ({ title, value, subtitle, icon: Icon, color, loading }: {
    title: string;
    value: string | number;
    subtitle?: string;
    icon: any;
    color: 'blue' | 'green' | 'purple' | 'orange';
    loading?: boolean;
  }) => {
    const colorClasses = {
      blue: 'bg-blue-50 text-blue-600',
      green: 'bg-green-50 text-green-600',
      purple: 'bg-purple-50 text-purple-600',
      orange: 'bg-orange-50 text-orange-600'
    };

    if (loading) {
      return (
        <Card>
          <div className="animate-pulse">
            <div className="flex items-center justify-between">
              <div>
                <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-16 mb-1"></div>
                <div className="h-3 bg-gray-200 rounded w-20"></div>
              </div>
              <div className="p-3 rounded-lg bg-gray-100">
                <div className="w-6 h-6 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>
        </Card>
      );
    }

    return (
      <Card>
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
            <p className="text-2xl font-bold text-gray-900 mb-1">
              {typeof value === 'number' ? value.toLocaleString() : value}
            </p>
            {subtitle && (
              <p className="text-sm text-gray-500">{subtitle}</p>
            )}
          </div>
          <div className={`p-3 rounded-lg ${colorClasses[color]}`}>
            <Icon className="w-6 h-6" />
          </div>
        </div>
      </Card>
    );
  };

  if (error && !stats) {
    return (
      <div className="p-6">
        <Card>
          <div className="flex items-center justify-center py-8 text-orange-600">
            <AlertCircle className="w-8 h-8 mr-2" />
            <div className="text-center">
              <p className="font-medium">Mode dégradé activé</p>
              <p className="text-sm text-gray-600 mt-1">
                Certaines données peuvent ne pas être disponibles
              </p>
            </div>
          </div>
        </Card>
      </div>
    );
  }

  console.log("🎨 Rendu dashboard parent - enfants:", enfants.length, enfants);
  console.log("👶 Enfant sélectionné:", selectedEnfant);

  return (
    <div className="p-6 space-y-6">
      {/* En-tête de bienvenue */}
      <Card className="bg-gradient-to-r from-[#005bac] to-blue-600 text-white">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-2xl font-bold">Espace Parent</h1>
            <p className="mt-1 text-blue-100">
              Suivez la scolarité de vos enfants en temps réel
            </p>
          </div>

          {/* Test simple du select */}
          <div className="mt-4 md:mt-0">
            <div className="flex items-center">
              <span className="mr-2 text-blue-100">Enfant sélectionné :</span>
              <select
                className="rounded-md bg-white text-gray-900 border border-gray-300 py-2 px-3"
                value={selectedEnfant?.id_eleve || ''}
                onChange={(e) => {
                  const enfant = enfants.find(enfant => enfant.id_eleve.toString() === e.target.value);
                  setSelectedEnfant(enfant || null);
                }}
              >
                <option value="">-- Sélectionner un enfant --</option>
                {enfants.map(enfant => (
                  <option key={enfant.id_eleve} value={enfant.id_eleve}>
                    {enfant.user?.prenom || enfant.prenom} {enfant.user?.nom || enfant.nom}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </Card>

      {/* Message si aucun enfant */}
      {enfants.length === 0 && !loading && (
        <Card className="text-center py-12">
          <div className="flex flex-col items-center">
            <User className="w-16 h-16 text-gray-300 mb-4" />
            <h3 className="text-xl font-medium text-gray-900 mb-2">
              Aucun enfant trouvé
            </h3>
            <p className="text-gray-600 mb-6 max-w-md">
              Aucun enfant n'est associé à votre compte parent dans le système.
              Veuillez contacter l'administration de l'école pour associer vos enfants à votre compte.
            </p>
            <div className="flex flex-col sm:flex-row gap-3">
              <Button variant="primary">
                Contacter l'administration
              </Button>
              <Button variant="outline">
                Actualiser
              </Button>
            </div>
          </div>
        </Card>
      )}

      {/* Cartes de statistiques */}
      {enfants.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Mes Enfants"
          value={enfants.length}
          subtitle="Enfants scolarisés"
          icon={User}
          color="blue"
          loading={loading}
        />
        
        <StatCard
          title="Activités"
          value={stats?.activites_inscrites || 0}
          subtitle="Inscriptions actives"
          icon={Activity}
          color="green"
          loading={loading}
        />

        <StatCard
          title="Moyenne Générale"
          value={stats?.moyenne_generale ? `${stats.moyenne_generale}/20` : 0}
          subtitle="Dernière période"
          icon={TrendingUp}
          color="purple"
          loading={loading}
        />

        <StatCard
          title="Assiduité"
          value={stats?.taux_presence ? `${stats.taux_presence}%` : 0}
          subtitle="Taux de présence"
          icon={Clock}
          color="orange"
          loading={loading}
        />
      </div>
      )}

      {/* Actions rapides */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-100 mr-3">
                <Calendar className="w-5 h-5 text-blue-600" />
              </div>
              <span className="font-medium">Emploi du temps</span>
            </div>
            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                console.log("🔘 Clic sur bouton Voir");
                console.log("👶 Enfant sélectionné:", selectedEnfant);
                console.log("📅 État showEmploiTemps avant:", showEmploiTemps);
                setShowEmploiTemps(true);
                console.log("📅 setShowEmploiTemps(true) appelé");
              }}
              disabled={!selectedEnfant}
            >
              Voir
            </Button>
          </div>
        </Card>
        
        <Card>
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-100 mr-3">
                <BarChart className="w-5 h-5 text-green-600" />
              </div>
              <span className="font-medium">Bulletins de notes</span>
            </div>
            <Button size="sm" variant="outline">Consulter</Button>
          </div>
        </Card>
        
        <Card>
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-purple-100 mr-3">
                <MessageSquare className="w-5 h-5 text-purple-600" />
              </div>
              <span className="font-medium">Contacter l'école</span>
            </div>
            <Button size="sm" variant="outline">Message</Button>
          </div>
        </Card>
      </div>

      {/* Notifications récentes */}
      <Card>
        <div className="flex items-center gap-2 mb-4">
          <MessageSquare className="w-5 h-5 text-blue-600" />
          <h3 className="text-lg font-medium text-gray-900">Notifications récentes</h3>
        </div>
        
        <div className="space-y-3">
          <div className="border-l-4 border-blue-500 bg-blue-50 p-4 rounded-r-lg">
            <div className="flex justify-between items-start">
              <div>
                <h4 className="font-medium text-gray-900">Réunion parents-professeurs</h4>
                <p className="text-sm text-gray-600 mt-1">
                  Prochaine réunion prévue le 15 novembre à 18h00
                </p>
              </div>
              <span className="text-sm text-gray-500">Il y a 2 jours</span>
            </div>
          </div>
          
          <div className="border-l-4 border-green-500 bg-green-50 p-4 rounded-r-lg">
            <div className="flex justify-between items-start">
              <div>
                <h4 className="font-medium text-gray-900">Bulletin disponible</h4>
                <p className="text-sm text-gray-600 mt-1">
                  Le bulletin du premier trimestre est maintenant disponible
                </p>
              </div>
              <span className="text-sm text-gray-500">Il y a 1 semaine</span>
            </div>
          </div>
        </div>
      </Card>

      {/* Modal Emploi du temps */}
      {console.log("🎨 Rendu modal - showEmploiTemps:", showEmploiTemps, "selectedEnfant:", !!selectedEnfant)}
      {showEmploiTemps && selectedEnfant && (
        <>
          {console.log("📅 Rendu EmploiTempsParent avec:", {
            eleveId: selectedEnfant.id_eleve,
            eleveNom: `${selectedEnfant.user?.prenom || selectedEnfant.prenom} ${selectedEnfant.user?.nom || selectedEnfant.nom}`
          })}
          <EmploiTempsParent
            eleveId={selectedEnfant.id_eleve}
            eleveNom={`${selectedEnfant.user?.prenom || selectedEnfant.prenom} ${selectedEnfant.user?.nom || selectedEnfant.nom}`}
            onClose={() => {
              console.log("❌ Fermeture modal emploi du temps");
              setShowEmploiTemps(false);
            }}
          />
        </>
      )}
    </div>
  );
};

export default DashboardParent;
