<?php
// Le dossier /utils avec auth.php semble gérer l'authentification
// (ex. : génération/verification de JWT, validation des données).
// Cela est essentiel pour sécuriser l'API.

// require_once __DIR__ . '/../../vendor/autoload.php';
require_once realpath(__DIR__ . '/../vendor/autoload.php');
require_once __DIR__ . '/../config/config.php';

use Firebase\JWT\JWT;
use Firebase\JWT\Key;

class Auth {
    private static $algo = 'HS256';

    public static function generateToken($userData) {
        $payload = [
            'iss' => 'scolaNova', // issuer
            'iat' => time(),       // issued at
            'exp' => time() + JWT_EXPIRATION, // expiration time
            'data' => $userData    // données à stocker (id, role, etc.)
        ];
        return JWT::encode($payload, JWT_SECRET, self::$algo);
    }

    public static function verifyToken($token) {
        try {
            $decoded = JWT::decode($token, new Key(JWT_SECRET, self::$algo));
            return $decoded->data;
        } catch (Exception $e) {
            return false;
        }
    }
}
