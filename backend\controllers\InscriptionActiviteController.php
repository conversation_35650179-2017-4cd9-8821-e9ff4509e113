<?php
// controllers/InscriptionActiviteController.php
require_once __DIR__ . '/../models/Activite.php';
require_once __DIR__ . '/../config/db.php';
require_once __DIR__ . '/../utils/auth.php';

class InscriptionActiviteController {
    private $pdo;
    private $activite;

    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->activite = new Activite($pdo);
    }

    // Récupérer les élèves inscrits à une activité
    public function getElevesInscrits($id_activite) {
        try {
            $eleves = $this->activite->getElevesInscrits($id_activite);
            
            if ($eleves === false) {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la récupération des élèves inscrits'
                ]);
                return;
            }

            // Formater les données
            $elevesFormates = array_map(function($eleve) {
                return [
                    'id_eleve' => (int)$eleve['id_eleve'],
                    'nom' => $eleve['nom'],
                    'prenom' => $eleve['prenom'],
                    'code_massar' => $eleve['code_massar'],
                    'classe' => $eleve['nom_classe'],
                    'date_inscription' => $eleve['date_inscription'],
                    'statut_inscription' => $eleve['statut_inscription']
                ];
            }, $eleves);

            http_response_code(200);
            echo json_encode([
                'success' => true,
                'data' => $elevesFormates,
                'message' => 'Élèves inscrits récupérés avec succès'
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la récupération des élèves inscrits',
                'error' => $e->getMessage()
            ]);
        }
    }

    // Inscrire un élève à une activité
    public function inscrireEleve() {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            // Validation des données
            if (!isset($input['id_activite']) || !isset($input['id_eleve'])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'ID activité et ID élève sont obligatoires'
                ]);
                return;
            }

            $id_activite = (int)$input['id_activite'];
            $id_eleve = (int)$input['id_eleve'];

            // Vérifier que l'activité existe
            $activite = $this->activite->getById($id_activite);
            if (!$activite) {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'message' => 'Activité non trouvée'
                ]);
                return;
            }

            // Vérifier que l'élève existe
            $stmt = $this->pdo->prepare('SELECT id_eleve FROM Eleve WHERE id_eleve = ?');
            $stmt->execute([$id_eleve]);
            if (!$stmt->fetch()) {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'message' => 'Élève non trouvé'
                ]);
                return;
            }

            $result = $this->activite->inscrireEleve($id_activite, $id_eleve);
            
            if (is_array($result) && isset($result['error'])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => $result['error']
                ]);
                return;
            }

            if ($result === false) {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de l\'inscription de l\'élève'
                ]);
                return;
            }

            http_response_code(201);
            echo json_encode([
                'success' => true,
                'message' => 'Élève inscrit avec succès'
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de l\'inscription de l\'élève',
                'error' => $e->getMessage()
            ]);
        }
    }

    // Désinscrire un élève d'une activité
    public function desinscrireEleve($id_activite, $id_eleve) {
        try {
            // Vérifier que l'inscription existe
            $stmt = $this->pdo->prepare('SELECT COUNT(*) FROM Inscription_Activite WHERE id_activite = ? AND id_eleve = ?');
            $stmt->execute([$id_activite, $id_eleve]);
            
            if ($stmt->fetchColumn() == 0) {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'message' => 'Inscription non trouvée'
                ]);
                return;
            }

            $result = $this->activite->desinscrireEleve($id_activite, $id_eleve);
            
            if ($result === false) {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la désinscription de l\'élève'
                ]);
                return;
            }

            http_response_code(200);
            echo json_encode([
                'success' => true,
                'message' => 'Élève désinscrit avec succès'
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la désinscription de l\'élève',
                'error' => $e->getMessage()
            ]);
        }
    }

    // Récupérer les activités d'un élève
    public function getActivitesEleve($id_eleve) {
        try {
            // Vérifier que l'élève existe
            $stmt = $this->pdo->prepare('SELECT id_eleve FROM Eleve WHERE id_eleve = ?');
            $stmt->execute([$id_eleve]);
            if (!$stmt->fetch()) {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'message' => 'Élève non trouvé'
                ]);
                return;
            }

            $activites = $this->activite->getActivitesEleve($id_eleve);
            
            if ($activites === false) {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la récupération des activités de l\'élève'
                ]);
                return;
            }

            // Formater les données
            $activitesFormatees = array_map(function($activite) {
                return [
                    'id_activite' => (int)$activite['id_activite'],
                    'nom_activite' => $activite['nom_activite'],
                    'description' => $activite['description'],
                    'type_activite' => $activite['type_activite'],
                    'jour_semaine' => $activite['jour_semaine'],
                    'heure_debut' => $activite['heure_debut'],
                    'heure_fin' => $activite['heure_fin'],
                    'lieu' => $activite['lieu'],
                    'prix' => (float)$activite['prix'],
                    'responsable' => $activite['nom_responsable'] ? [
                        'nom' => $activite['nom_responsable'],
                        'prenom' => $activite['prenom_responsable']
                    ] : null,
                    'date_inscription' => $activite['date_inscription'],
                    'statut_inscription' => $activite['statut_inscription']
                ];
            }, $activites);

            http_response_code(200);
            echo json_encode([
                'success' => true,
                'data' => $activitesFormatees,
                'message' => 'Activités de l\'élève récupérées avec succès'
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la récupération des activités de l\'élève',
                'error' => $e->getMessage()
            ]);
        }
    }

    // Récupérer les statistiques des activités
    public function getStatistiques() {
        try {
            $stats = $this->activite->getStatistiques();
            
            if ($stats === false) {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la récupération des statistiques'
                ]);
                return;
            }

            http_response_code(200);
            echo json_encode([
                'success' => true,
                'data' => $stats,
                'message' => 'Statistiques récupérées avec succès'
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la récupération des statistiques',
                'error' => $e->getMessage()
            ]);
        }
    }

    // Récupérer les élèves disponibles pour inscription (non encore inscrits)
    public function getElevesDisponibles($id_activite) {
        try {
            $query = "
                SELECT 
                    e.id_eleve,
                    u.nom,
                    u.prenom,
                    e.code_massar,
                    c.nom_classe
                FROM Eleve e
                JOIN Utilisateur u ON e.id_utilisateur = u.id_utilisateur
                JOIN Inscription i ON e.id_eleve = i.id_eleve
                JOIN Classe c ON i.id_classe = c.id_classe
                JOIN Annee_Scolaire a ON i.id_annee_scolaire = a.id_annee_scolaire
                WHERE a.est_active = true
                AND e.id_eleve NOT IN (
                    SELECT id_eleve 
                    FROM Inscription_Activite 
                    WHERE id_activite = ? AND statut_inscription = 'active'
                )
                ORDER BY u.nom, u.prenom
            ";
            
            $stmt = $this->pdo->prepare($query);
            $stmt->execute([$id_activite]);
            $eleves = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Formater les données
            $elevesFormates = array_map(function($eleve) {
                return [
                    'id_eleve' => (int)$eleve['id_eleve'],
                    'nom' => $eleve['nom'],
                    'prenom' => $eleve['prenom'],
                    'code_massar' => $eleve['code_massar'],
                    'classe' => $eleve['nom_classe']
                ];
            }, $eleves);

            http_response_code(200);
            echo json_encode([
                'success' => true,
                'data' => $elevesFormates,
                'message' => 'Élèves disponibles récupérés avec succès'
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la récupération des élèves disponibles',
                'error' => $e->getMessage()
            ]);
        }
    }
}
?>
