<?php

require_once __DIR__ . '/../models/Cours.php';

class CoursController {
    private $coursModel;

    public function __construct($pdo) {
        $this->coursModel = new Cours($pdo);
    }

    
    public function getAll() {
        try {
            $cours = $this->coursModel->getAll();

            echo json_encode([
                'success' => true,
                'data' => $cours,
                'message' => 'Cours récupérés avec succès'
            ]);

        } catch (Exception $e) {
            error_log("Erreur dans getAll: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la récupération des cours: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Récupérer les cours par classe
     * GET /cours/classe/{id}
     */
    public function getByClasse($id_classe) {
        try {
            if (!is_numeric($id_classe) || $id_classe <= 0) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'ID classe invalide'
                ]);
                return;
            }

            $cours = $this->coursModel->getByClasse($id_classe);

            echo json_encode([
                'success' => true,
                'data' => $cours,
                'message' => 'Emploi du temps de la classe récupéré avec succès'
            ]);

        } catch (Exception $e) {
            error_log("Erreur dans getByClasse: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la récupération de l\'emploi du temps: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Récupérer les cours par enseignant
     * GET /cours/enseignant/{id}
     */
    public function getByEnseignant($id_enseignant) {
        try {
            if (!is_numeric($id_enseignant) || $id_enseignant <= 0) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'ID enseignant invalide'
                ]);
                return;
            }

            $cours = $this->coursModel->getByEnseignant($id_enseignant);

            echo json_encode([
                'success' => true,
                'data' => $cours,
                'message' => 'Emploi du temps de l\'enseignant récupéré avec succès'
            ]);

        } catch (Exception $e) {
            error_log("Erreur dans getByEnseignant: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la récupération de l\'emploi du temps: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Créer un nouveau cours
     * POST /cours
     */
    public function create() {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Données JSON invalides'
                ]);
                return;
            }

            // Valider les champs requis
            $required_fields = ['id_unite', 'id_enseignant', 'id_classe', 'id_salle', 'jour_semaine', 'heure_debut', 'heure_fin'];
            foreach ($required_fields as $field) {
                if (!isset($input[$field]) || empty($input[$field])) {
                    http_response_code(400);
                    echo json_encode([
                        'success' => false,
                        'message' => "Le champ $field est requis"
                    ]);
                    return;
                }
            }

            // Valider le format des heures
            if (!$this->validateTimeFormat($input['heure_debut']) || !$this->validateTimeFormat($input['heure_fin'])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Format d\'heure invalide (HH:MM attendu)'
                ]);
                return;
            }

            // Valider que heure_fin > heure_debut
            if ($input['heure_debut'] >= $input['heure_fin']) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'L\'heure de fin doit être supérieure à l\'heure de début'
                ]);
                return;
            }

            $result = $this->coursModel->create($input);

            if ($result['success']) {
                http_response_code(201);
                echo json_encode([
                    'success' => true,
                    'data' => ['id_cours' => $result['id']],
                    'message' => 'Cours créé avec succès'
                ]);
            } else {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => $result['message'] ?? 'Erreur lors de la création du cours',
                    'conflicts' => $result['conflicts'] ?? []
                ]);
            }

        } catch (Exception $e) {
            error_log("Erreur dans create: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la création du cours: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Mettre à jour un cours
     * PUT /cours/{id}
     */
    public function update($id) {
        try {
            if (!is_numeric($id) || $id <= 0) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'ID cours invalide'
                ]);
                return;
            }

            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Données JSON invalides'
                ]);
                return;
            }

            // Valider le format des heures si présentes
            if (isset($input['heure_debut']) && !$this->validateTimeFormat($input['heure_debut'])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Format d\'heure de début invalide (HH:MM attendu)'
                ]);
                return;
            }

            if (isset($input['heure_fin']) && !$this->validateTimeFormat($input['heure_fin'])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Format d\'heure de fin invalide (HH:MM attendu)'
                ]);
                return;
            }

            // Valider que heure_fin > heure_debut si les deux sont présentes
            if (isset($input['heure_debut']) && isset($input['heure_fin']) && $input['heure_debut'] >= $input['heure_fin']) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'L\'heure de fin doit être supérieure à l\'heure de début'
                ]);
                return;
            }

            $result = $this->coursModel->update($id, $input);

            if ($result['success']) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Cours mis à jour avec succès'
                ]);
            } else {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => $result['message'] ?? 'Erreur lors de la mise à jour du cours',
                    'conflicts' => $result['conflicts'] ?? []
                ]);
            }

        } catch (Exception $e) {
            error_log("Erreur dans update: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la mise à jour du cours: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Supprimer un cours
     * DELETE /cours/{id}
     */
    public function delete($id) {
        try {
            if (!is_numeric($id) || $id <= 0) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'ID cours invalide'
                ]);
                return;
            }

            $result = $this->coursModel->delete($id);

            if ($result) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Cours supprimé avec succès'
                ]);
            } else {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la suppression du cours'
                ]);
            }

        } catch (Exception $e) {
            error_log("Erreur dans delete: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la suppression du cours: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Récupérer un cours par ID
     * GET /cours/{id}
     */
    public function getById($id) {
        try {
            if (!is_numeric($id) || $id <= 0) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'ID cours invalide'
                ]);
                return;
            }

            $cours = $this->coursModel->getById($id);

            if ($cours) {
                echo json_encode([
                    'success' => true,
                    'data' => $cours,
                    'message' => 'Cours récupéré avec succès'
                ]);
            } else {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'message' => 'Cours non trouvé'
                ]);
            }

        } catch (Exception $e) {
            error_log("Erreur dans getById: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la récupération du cours: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Valider le format d'heure (HH:MM)
     */
    private function validateTimeFormat($time) {
        return preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $time);
    }
}

?>
