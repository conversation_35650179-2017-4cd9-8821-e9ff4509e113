<?php

class ParentEleve {
  private $pdo;
  private $table = "Parent";

  public function __construct($db) {
      $this->pdo = $db;
  }

  public function getAll() {
    $query = "SELECT p.*, u.* FROM Parent p 
              JOIN Utilisateur u ON p.id_utilisateur = u.id_utilisateur";
    $stmt = $this->pdo->prepare($query);
    $stmt->execute();
    $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

    return array_map(function ($row) {
        return [
            'id_parent' => (int) $row['id_parent'],
            'id_utilisateur' => (int) $row['id_utilisateur'],
            'nom_ar' => $row['nom_ar'],
            'prenom_ar' => $row['prenom_ar'],
            'num_CIN' => $row['num_CIN'],
            'user' => [
                'id_utilisateur' => (int) $row['id_utilisateur'],
                'nom' => $row['nom'],
                'prenom' => $row['prenom'],
                'email' => $row['email'],
                'mot_de_passe' => '',
                'role' => $row['role'],
                'sexe' => $row['sexe'],
                'date_naissance' => $row['date_naissance'],
                'lieu_naissance' => $row['lieu_naissance'],
                'nationalite' => $row['nationalite'],
                'telephone' => $row['telephone'],
                'adresse' => $row['adresse'] ?? '',
                'photo' => $row['photo'] ?? '',
                'est_valide' => (bool) $row['est_valide'],
                'est_actif' => (bool) $row['est_actif'],
            ]
        ];
    }, $rows);
}

  public function getById($id) {
      $stmt = $this->pdo->prepare("SELECT p.*, u.* FROM Parent p JOIN Utilisateur u 
                ON p.id_utilisateur = u.id_utilisateur WHERE p.id_parent = :id");
      $stmt->bindParam(':id', $id, PDO::PARAM_INT);
      $stmt->execute();
      $result = $stmt->fetch(PDO::FETCH_ASSOC);
      if ($result) {
        $parent = [
            'id_parent' => (int) $result['id_parent'],
            'id_utilisateur' => (int) $result['id_utilisateur'],
            'nom_ar' => $result['nom_ar'],
            'prenom_ar' => $result['prenom_ar'],
            'num_CIN' => $result['num_CIN'],
            'user' => [
                'id_utilisateur' => (int) $result['id_utilisateur'],
                'nom' => $result['nom'],
                'prenom' => $result['prenom'],
                'email' => $result['email'],
                'mot_de_passe' => '',
                'role' => $result['role'],
                'sexe' => $result['sexe'],
                'date_naissance' => $result['date_naissance'],
                'lieu_naissance' => $result['lieu_naissance'],
                'nationalite' => $result['nationalite'],
                'telephone' => $result['telephone'],
                'adresse' => $result['adresse'] ?? '',
                'photo' => $result['photo'] ?? '',
                'est_valide' => (bool) $result['est_valide'],
                'est_actif' => (bool) $result['est_actif'],
            ]
        ];
        return $parent;
    }
    return false;
}

  /**
   * Vérifier si un numéro CIN existe déjà
   */
  public function getByCIN($num_CIN) {
    $query = "SELECT p.*, u.nom, u.prenom FROM " . $this->table . " p
              JOIN Utilisateur u ON p.id_utilisateur = u.id_utilisateur
              WHERE p.num_CIN = :num_CIN";
    $stmt = $this->pdo->prepare($query);
    $stmt->bindParam(':num_CIN', $num_CIN, PDO::PARAM_STR);
    $stmt->execute();
    return $stmt->fetch(PDO::FETCH_ASSOC);
  }

  public function create($data) {
    try {
      $query = "INSERT INTO " . $this->table . " (id_utilisateur, nom_ar, prenom_ar, num_CIN)
                  VALUES (:id_utilisateur, :nom_ar, :prenom_ar, :num_CIN)";
      $stmt = $this->pdo->prepare($query);
      $stmt->bindParam(':id_utilisateur', $data['id_utilisateur']);
      $stmt->bindParam(':nom_ar', $data['nom_ar']);
      $stmt->bindParam(':prenom_ar', $data['prenom_ar']);
      $stmt->bindParam(':num_CIN', $data['num_CIN']);

      if ($stmt->execute()) {
        return $this->pdo->lastInsertId();
      } else {
        error_log("Erreur SQL Parent::create : " . implode(", ", $stmt->errorInfo()));
        return false;
      }
    } catch (PDOException $e) {
      error_log("Erreur PDO dans Parent::create : " . $e->getMessage());

      // Vérifier si c'est une erreur de contrainte d'unicité pour le CIN
      if ($e->getCode() == 23000 && strpos($e->getMessage(), 'num_CIN') !== false) {
        throw new Exception("Ce numéro CIN est déjà utilisé par un autre parent");
      }

      // Autres erreurs de contrainte
      if ($e->getCode() == 23000) {
        throw new Exception("Cette donnée existe déjà dans le système");
      }

      throw new Exception("Erreur lors de la création du parent : " . $e->getMessage());
    }
  }

  public function createRelation($data) {
    $query = "INSERT INTO Relation_Parent_Eleve (id_parent, id_eleve, type_relation)
                VALUES (:id_parent, :id_eleve, :type_relation)";
    $stmt = $this->pdo->prepare($query);
    $stmt->bindParam(':id_parent', $data['id_parent']);
    $stmt->bindParam(':id_eleve', $data['id_eleve']);
    $stmt->bindParam(':type_relation', $data['type_relation']);
    $stmt->execute();
      if ($stmt->rowCount() > 0) {
          return true;
      }else{
          return  false;
      }
}

  public function updateRelation($id_parent, $id_eleve, $type_relation) {
    $query = "UPDATE Relation_Parent_Eleve
                SET type_relation = :type_relation
                WHERE id_parent = :id_parent AND id_eleve = :id_eleve";
    $stmt = $this->pdo->prepare($query);
    $stmt->bindParam(':id_parent', $id_parent);
    $stmt->bindParam(':id_eleve', $id_eleve);
    $stmt->bindParam(':type_relation', $type_relation);
    $stmt->execute();
      if ($stmt->rowCount() > 0) {
          return true;
      }else{
          return  false;
      }
}

public function update($id, $data) {
    try {
        error_log("Tentative de mise à jour du parent ID: $id avec les données: " . json_encode($data));

        // Vérifier d'abord si le parent existe
        $query = "SELECT COUNT(*) FROM " . $this->table . " WHERE id_parent = :id_parent";
        $checkStmt = $this->pdo->prepare($query);
        $checkStmt->bindParam(':id_parent', $id, PDO::PARAM_INT);
        $checkStmt->execute();
        $exists = $checkStmt->fetchColumn() > 0;

        if (!$exists) {
            error_log("Parent avec ID $id n'existe pas");
            return false;
        }

        $query = "UPDATE " . $this->table . " SET nom_ar = :nom_ar,
                prenom_ar = :prenom_ar, num_CIN = :num_CIN WHERE id_parent = :id_parent";
        $stmt = $this->pdo->prepare($query);
        $stmt->bindParam(':nom_ar', $data['nom_ar'], PDO::PARAM_STR);
        $stmt->bindParam(':prenom_ar', $data['prenom_ar'], PDO::PARAM_STR);
        $stmt->bindParam(':num_CIN', $data['num_CIN'], PDO::PARAM_STR);
        $stmt->bindParam(':id_parent', $id, PDO::PARAM_INT);

        $result = $stmt->execute();
        $rowCount = $stmt->rowCount();

        error_log("Résultat de l'exécution: " . ($result ? 'true' : 'false') . ", Lignes affectées: $rowCount");

        // Si l'exécution a réussi, considérer comme succès même si aucune ligne n'a été affectée
        // (cela arrive quand les données sont identiques)
        if ($result) {
            if ($rowCount > 0) {
                error_log("Parent ID $id mis à jour avec succès ($rowCount ligne(s) affectée(s))");
            } else {
                error_log("Parent ID $id - aucune modification nécessaire (données identiques)");
            }
            return true;
        } else {
            error_log("Erreur lors de l'exécution de la requête UPDATE pour le parent ID: $id");
            return false;
        }
    } catch (PDOException $e) {
        error_log("Erreur PDO lors de la mise à jour du parent: " . $e->getMessage());

        // Vérifier si c'est une erreur de contrainte d'unicité pour le CIN
        if ($e->getCode() == 23000 && strpos($e->getMessage(), 'num_CIN') !== false) {
            throw new Exception("Ce numéro CIN est déjà utilisé par un autre parent");
        }

        // Autres erreurs de contrainte
        if ($e->getCode() == 23000) {
            throw new Exception("Cette donnée existe déjà dans le système");
        }

        throw new Exception("Erreur lors de la mise à jour du parent : " . $e->getMessage());
    }
}

  public function delete($id) {
      $stmt = $this->pdo->prepare("DELETE FROM " . $this->table . " WHERE id_parent = :id");
      $stmt->bindParam(':id', $id, PDO::PARAM_INT);
      $stmt->execute();
      if ($stmt->rowCount() > 0) {
          return true; 
      }else{
          return  false; 
      } 
  }
    public function findByCIN($cin) {
        try {
            $query = "SELECT p.*, u.* FROM Parent p JOIN Utilisateur u 
                        ON p.id_utilisateur = u.id_utilisateur WHERE p.num_CIN = :cin";
            $stmt = $this->pdo->prepare($query);
            $stmt->bindParam(':cin', $cin, PDO::PARAM_STR);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($result) {
                // Structurer les données pour correspondre à ce que le frontend attend
                return [
                    'id_parent' => $result['id_parent'],
                    'id_utilisateur' => $result['id_utilisateur'],
                    'nom_ar' => $result['nom_ar'],
                    'prenom_ar' => $result['prenom_ar'],
                    'num_CIN' => $result['num_CIN'],
                    'user' => [
                        'id_utilisateur' => $result['id_utilisateur'],
                        'nom' => $result['nom'],
                        'prenom' => $result['prenom'],
                        'email' => $result['email'],
                        'sexe' => $result['sexe'],
                        'date_naissance' => $result['date_naissance'],
                        'lieu_naissance' => $result['lieu_naissance'],
                        'nationalite' => $result['nationalite'],
                        'telephone' => $result['telephone'],
                        'adresse' => $result['adresse'],
                        'photo' => $result['photo']
                    ]
                ];
            } else {
                return false;
            }
        } catch (PDOException $e) {
            error_log("Erreur dans findByCIN: " . $e->getMessage());
            return false;
        }
    }

    public function getParentsByEleveId($id_eleve) {
        try {
            error_log("getParentsByEleveId appelé avec ID élève: " . $id_eleve);
            $query = "SELECT p.*, u.*, r.type_relation
                      FROM Parent p
                      JOIN Utilisateur u ON p.id_utilisateur = u.id_utilisateur
                      JOIN Relation_Parent_Eleve r ON p.id_parent = r.id_parent
                      WHERE r.id_eleve = :id_eleve";
            error_log("Requête SQL: " . $query);
            $stmt = $this->pdo->prepare($query);
            $stmt->bindParam(':id_eleve', $id_eleve, PDO::PARAM_INT);
            $stmt->execute();
            $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
            error_log("Nombre de résultats SQL: " . count($results));

            $parents = [];
            foreach ($results as $result) {
                $parents[] = [
                    'id_parent' => (int) $result['id_parent'],
                    'id_utilisateur' => (int) $result['id_utilisateur'],
                    'nom_ar' => $result['nom_ar'],
                    'prenom_ar' => $result['prenom_ar'],
                    'num_CIN' => $result['num_CIN'],
                    'type_relation' => $result['type_relation'],
                    'user' => [
                        'id_utilisateur' => (int) $result['id_utilisateur'],
                        'nom' => $result['nom'],
                        'prenom' => $result['prenom'],
                        'email' => $result['email'],
                        'sexe' => $result['sexe'],
                        'date_naissance' => $result['date_naissance'],
                        'lieu_naissance' => $result['lieu_naissance'],
                        'nationalite' => $result['nationalite'],
                        'telephone' => $result['telephone'],
                        'adresse' => $result['adresse'],
                        'photo' => $result['photo']
                    ]
                ];
            }
            return $parents;
        } catch (PDOException $e) {
            error_log("Erreur dans getParentsByEleveId: " . $e->getMessage());
            return false;
        }
    }
}

