
import React, { useState, useEffect } from "react";
import Input from "./Input";
import Select from "./Select";
import Button from "./Button";
import { register, addEleve, addParent, addRelation, searchParentByCIN, getParentsByEleve, updateUser, updateEleve, updateParent, updateRelation, getClasses, getAnneesScolaires, getInscriptionByEleve, addInscription, updateInscription } from "../services/api";
import type { User, Eleve, Parent, Relation, AncienneEcole, AnneeScolaire } from "../types";
import { Search, UserCheck, UserPlus, AlertCircle, MinusCircle, PlusCircle } from "lucide-react";
import { handleGlobalError, extractErrorMessage } from "../utils/errorUtils";

interface EleveFormProps {
  onSuccess?: () => void;
  initialEleve?: Eleve | null;
}

const EleveForm: React.FC<EleveFormProps> = ({ onSuccess, initialEleve }) => {
  const [step, setStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState<boolean | null>(null);
  const [submitMessage, setSubmitMessage] = useState("");
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [selectedClassId, setSelectedClassId] = useState<number | "">("");
  const [classes, setClasses] = useState<any[]>([]);
  const [activeAnneeScolaire, setActiveAnneeScolaire] = useState<any>(null);

  // États pour la recherche de parent
  const [searchCIN, setSearchCIN] = useState("");
  const [searchResults, setSearchResults] = useState<{ success: boolean, data?: any, message?: string } | null>(null);
  const [isSearching, setIsSearching] = useState(false);
  const [showSearchModal, setShowSearchModal] = useState(false);
  const [currentParentIndex, setCurrentParentIndex] = useState<number | null>(null);

  const [userEleve, setUserEleve] = useState<User>(() => {
    console.log("Données initiales de l'élève:", initialEleve);
    console.log("Données utilisateur de l'élève:", initialEleve?.user);

    if (initialEleve?.user) {
      return {
        ...initialEleve.user,
        id_utilisateur: initialEleve.user.id_utilisateur || initialEleve.id_utilisateur,
        mot_de_passe: "",
        role: "eleve",
        est_valide: true,
        est_actif: true,
      };
    }

    return {
      id_utilisateur: 0,
      nom: "",
      prenom: "",
      email: "",
      mot_de_passe: "",
      role: "eleve",
      sexe: null,
      date_naissance: "",
      lieu_naissance: "",
      nationalite: "marocain",
      telephone: "",
      adresse: "",
      photo: "",
      est_valide: false,
      est_actif: true,
    };
  });

  const [eleve, setEleve] = useState<Eleve>(
    initialEleve || {
      id_utilisateur: 0,
      id_eleve: 0,
      code_massar: "",
      nom_ar: "",
      prenom_ar: "",
      lieu_naissance_ar: "",
      code_gresa: "",
    }
  );

  const [usersParent, setUsersParent] = useState<User[]>([
    {
      id_utilisateur: 0,
      nom: "",
      prenom: "",
      email: "",
      mot_de_passe: "",
      role: "parent",
      sexe: null,
      date_naissance: "",
      lieu_naissance: "",
      nationalite: "marocain",
      telephone: "",
      adresse: "",
      photo: "",
      est_valide: false,
      est_actif: true,
    },
  ]);
  const [parents, setParents] = useState<Parent[]>([
    {
      id_utilisateur: 0,
      id_parent: 0,
      nom_ar: "",
      prenom_ar: "",
      num_CIN: "",
    },
  ]);

  const [relations, setRelations] = useState<Relation[]>([
    {
      id_parent: 0,
      id_eleve: 0,
      type_relation: "",
    },
  ]);

  const [lastSchool, setLastSchool] = useState<AncienneEcole>({
    code_gresa: "",
    nom: "",
    type: "publique",
    cycle: "maternelle",
    adresse: "",
  });

  const [hasAttendedSchool, setHasAttendedSchool] = useState<boolean>(false);

  const sexeOptions = [
    { value: "garçon", label: "Garçon" },
    { value: "fille", label: "Fille" },
  ];

  const nationaliteOptions = [
    { value: "marocain", label: "Marocain(e)" },
    { value: "francais", label: "Français(e)" },
    { value: "espagnol", label: "Espagnol(e)" },
    { value: "algerien", label: "Algérien(ne)" },
    { value: "tunisien", label: "Tunisien(ne)" },
    { value: "autre", label: "Autre" },
  ];

  const relationOptions = [
    { value: "père", label: "Père" },
    { value: "mère", label: "Mère" },
    { value: "tuteur", label: "Tuteur" },
  ];

  const cycleOptions = [
    { value: "maternelle", label: "Maternelle" },
    { value: "primaire", label: "Primaire" },
    { value: "collège", label: "Collège" },
    { value: "lycée", label: "Lycée" },
  ];

  // Charger les données des parents, de l'ancienne école et de l'inscription lors de la modification
  useEffect(() => {
    const loadData = async () => {
      if (initialEleve && initialEleve.id_eleve) {
        try {
          console.log("Chargement des données pour l'élève:", initialEleve.id_eleve);

          // Charger l'inscription
          const inscriptionResponse = await getInscriptionByEleve(initialEleve.id_eleve);
          console.log("inscription:",inscriptionResponse.data);
          
          if (inscriptionResponse.data.success && inscriptionResponse.data.data) {
            setSelectedClassId(inscriptionResponse.data.data.id_classe);
            console.log("Inscription chargée:", inscriptionResponse.data.data.id_classe);
          } else {
            console.log("Aucune inscription trouvée pour l'élève");
          }

          // Charger les données de l'ancienne école si code_gresa existe
          if (initialEleve.code_gresa) {
            try {
              const { getAncienneEcole } = await import('../services/api');
              const response = await getAncienneEcole(initialEleve.code_gresa);
              if (response.data.success && response.data.data) {
                const ancienneEcole = response.data.data;
                setHasAttendedSchool(true);
                setLastSchool({
                  code_gresa: ancienneEcole.code_gresa,
                  nom: ancienneEcole.nom,
                  type: ancienneEcole.type,
                  cycle: ancienneEcole.cycle,
                  adresse: ancienneEcole.adresse,
                });
                console.log("Ancienne école chargée:", ancienneEcole);
              } else {
                setHasAttendedSchool(true);
                setLastSchool({
                  code_gresa: initialEleve.code_gresa,
                  nom: "",
                  type: "publique",
                  cycle: "primaire",
                  adresse: "",
                });
                console.log("Code GRESA trouvé mais ancienne école non trouvée:", initialEleve.code_gresa);
              }
            } catch (error) {
              console.error("Erreur lors du chargement de l'ancienne école:", error);
              setHasAttendedSchool(true);
              setLastSchool({
                code_gresa: initialEleve.code_gresa,
                nom: "",
                type: "publique",
                cycle: "primaire",
                adresse: "",
              });
            }
          }

          // Charger les parents
          const response = await getParentsByEleve(initialEleve.id_eleve);
          console.log("Réponse parents:", response.data);

          if (response.data.success && response.data.data.length > 0) {
            const parentsData = response.data.data;
            const parentsInfo = parentsData.map((p: any) => ({
              id_parent: p.id_parent,
              id_utilisateur: p.id_utilisateur,
              nom_ar: p.nom_ar,
              prenom_ar: p.prenom_ar,
              num_CIN: p.num_CIN,
            }));
            const usersInfo = parentsData.map((p: any) => ({
              id_utilisateur: p.user.id_utilisateur,
              nom: p.user.nom,
              prenom: p.user.prenom,
              email: p.user.email,
              mot_de_passe: "",
              role: "parent",
              sexe: p.user.sexe,
              date_naissance: p.user.date_naissance,
              lieu_naissance: p.user.lieu_naissance,
              nationalite: p.user.nationalite,
              telephone: p.user.telephone,
              adresse: p.user.adresse,
              photo: p.user.photo,
              est_valide: true,
              est_actif: true,
            }));
            const relationsInfo = parentsData.map((p: any) => ({
              id_parent: p.id_parent,
              id_eleve: initialEleve.id_eleve,
              type_relation: p.type_relation,
            }));
            setParents(parentsInfo);
            setUsersParent(usersInfo);
            setRelations(relationsInfo);
            console.log("Données parents chargées:", { parents: parentsInfo, users: usersInfo, relations: relationsInfo });
          }
        } catch (error) {
          console.error("Erreur lors du chargement des données:", error);
        }
      }
    };

    loadData();
  }, [initialEleve]);

  // Charger les classes et l'année scolaire active au montage
  useEffect(() => {
    const fetchData = async () => {
      try {
        const classResponse = await getClasses();
        if (classResponse.data.success) {
          setClasses(classResponse.data.data);
        } else {
          console.error("Erreur lors du chargement des classes:", classResponse.data.message);
        }

        const anneeResponse = await getAnneesScolaires();
        if (anneeResponse.data.success) {
          const activeAnnee = anneeResponse.data.data.find((as: AnneeScolaire) => as.est_active);
          if (activeAnnee) {
            setActiveAnneeScolaire(activeAnnee);
          } else {
            console.error("Aucune année scolaire active trouvée");
          }
        } else {
          console.error("Erreur lors du chargement des années scolaires:", anneeResponse.data.message);
        }
      } catch (error) {
        console.error("Erreur lors du chargement des données:", error);
      }
    };

    fetchData();
  }, []);

  const addParentField = () => {
    setParents([
      ...parents,
      {
        id_utilisateur: 0,
        id_parent: 0,
        nom_ar: "",
        prenom_ar: "",
        num_CIN: "",
      },
    ]);
    setUsersParent([
      ...usersParent,
      {
        id_utilisateur: 0,
        nom: "",
        prenom: "",
        email: "",
        mot_de_passe: "",
        role: "parent",
        sexe: null,
        date_naissance: "",
        lieu_naissance: "",
        nationalite: "marocain",
        telephone: "",
        adresse: "",
        photo: "",
        est_valide: false,
        est_actif: true,
      },
    ]);
    setRelations([
      ...relations,
      {
        id_parent: 0,
        id_eleve: 0,
        type_relation: "",
      },
    ]);
  };

  const removeParent = (index: number) => {
    setParents(parents.filter((_, i) => i !== index));
    setUsersParent(usersParent.filter((_, i) => i !== index));
    setRelations(relations.filter((_, i) => i !== index));
  };

  const openSearchModal = (index: number) => {
    setCurrentParentIndex(index);
    setShowSearchModal(true);
    setSearchCIN("");
    setSearchResults(null);
  };

  const closeSearchModal = () => {
    setShowSearchModal(false);
    setCurrentParentIndex(null);
    setSearchCIN("");
    setSearchResults(null);
  };

  const handleSearchParent = async () => {
    if (!searchCIN.trim()) {
      alert("Veuillez saisir un numéro de CIN");
      return;
    }

    setIsSearching(true);
    try {
      const response = await searchParentByCIN(searchCIN.trim());
      setSearchResults(response.data);
    } catch (error) {
      console.error("Erreur lors de la recherche:", error);
      setSearchResults({ success: false, message: "Erreur lors de la recherche" });
    } finally {
      setIsSearching(false);
    }
  };

  const useExistingParent = () => {
    if (searchResults?.success && searchResults.data && currentParentIndex !== null) {
      const existingParent = searchResults.data;
      setParents(prev => {
        const updated = [...prev];
        updated[currentParentIndex] = {
          id_parent: existingParent.id_parent || 0,
          id_utilisateur: existingParent.id_utilisateur || 0,
          nom_ar: existingParent.nom_ar || "",
          prenom_ar: existingParent.prenom_ar || "",
          num_CIN: existingParent.num_CIN || "",
        };
        return updated;
      });
      setUsersParent(prev => {
        const updated = [...prev];
        updated[currentParentIndex] = {
          ...updated[currentParentIndex],
          id_utilisateur: existingParent.user?.id_utilisateur || 0,
          nom: existingParent.user?.nom || "",
          prenom: existingParent.user?.prenom || "",
          email: existingParent.user?.email || "",
          mot_de_passe: "123456",
          role: "parent",
          sexe: existingParent.user?.sexe || null,
          date_naissance: existingParent.user?.date_naissance || "",
          lieu_naissance: existingParent.user?.lieu_naissance || "",
          nationalite: existingParent.user?.nationalite || "",
          telephone: existingParent.user?.telephone || "",
          adresse: existingParent.user?.adresse || "",
          photo: existingParent.user?.photo || "",
          est_valide: true,
          est_actif: true,
        };
        return updated;
      });
      closeSearchModal();
      alert("Parent existant ajouté avec succès !");
    }
  };

  const validateStep = () => {
    const newErrors: { [key: string]: string } = {};
    const today = new Date().toISOString().split('T')[0];

    if (step === 1) {
      if (!userEleve.nom?.trim()) newErrors.nom_fr = "Le nom en français est requis.";
      if (!eleve.nom_ar?.trim()) newErrors.nom_ar = "Le nom en arabe est requis.";
      if (!userEleve.prenom?.trim()) newErrors.prenom_fr = "Le prénom en français est requis.";
      if (!eleve.prenom_ar?.trim()) newErrors.prenom_ar = "Le prénom en arabe est requis.";
      if (!userEleve.sexe) newErrors.sexe = "Le sexe est requis.";
      if (!userEleve.date_naissance) newErrors.date_naissance = "La date de naissance est requise.";
      else if (userEleve.date_naissance > today) newErrors.date_naissance = "La date de naissance ne peut pas être dans le futur.";
      if (!userEleve.lieu_naissance?.trim()) newErrors.lieu_naissance_fr = "Le lieu de naissance est requis.";
      if (!eleve.lieu_naissance_ar?.trim()) newErrors.lieu_naissance_ar = "Le lieu de naissance est requis.";
      if (!userEleve.nationalite) newErrors.nationalite = "La nationalité est requise.";
      if (!userEleve.adresse?.trim()) newErrors.adresse = "L'adresse est requise.";
      if (!userEleve.email?.trim()) newErrors.email = "L'email est requis.";
      else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(userEleve.email)) newErrors.email = "Format d'email invalide.";
    }

    if (step === 2) {
      // Validation des champs parents
      parents.forEach((parent, idx) => {
        const userParent = usersParent[idx];
        if (!userParent?.nom?.trim()) newErrors[`parent_nom_fr_${idx}`] = "Le nom en français est requis.";
        if (!parent.nom_ar?.trim()) newErrors[`parent_nom_ar_${idx}`] = "Le nom en arabe est requis.";
        if (!userParent?.prenom?.trim()) newErrors[`parent_prenom_fr_${idx}`] = "Le prénom en français est requis.";
        if (!parent.prenom_ar?.trim()) newErrors[`parent_prenom_ar_${idx}`] = "Le prénom en arabe est requis.";
        if (!userParent?.sexe) newErrors[`parent_sexe_${idx}`] = "Le sexe est requis.";
        if (!userParent?.date_naissance) newErrors[`parent_date_naissance_${idx}`] = "La date de naissance est requise.";
        else if (userParent?.date_naissance > today) newErrors[`parent_date_naissance_${idx}`] = "La date de naissance ne peut pas être dans le futur.";
        if (!userParent?.lieu_naissance?.trim()) newErrors[`parent_lieu_naissance_${idx}`] = "Le lieu de naissance est requis.";
        if (!parent.num_CIN?.trim()) {
          newErrors[`parent_num_CIN_${idx}`] = "Le numéro de la CIN est requis.";
        } else {
          // Vérifier les CIN dupliqués entre les parents du même formulaire
          const duplicateCINIndex = parents.findIndex((p, i) =>
            i !== idx && p.num_CIN?.trim() === parent.num_CIN?.trim() && parent.num_CIN?.trim()
          );
          if (duplicateCINIndex !== -1) {
            newErrors[`parent_num_CIN_${idx}`] = `Ce numéro CIN est déjà utilisé par le parent ${duplicateCINIndex + 1}`;
          }
        }
        if (!userParent?.nationalite) newErrors[`parent_nationalite_${idx}`] = "La nationalité est requise.";
        if (!userParent?.adresse?.trim()) newErrors[`parent_adresse_${idx}`] = "L'adresse est requise.";
        if (!userParent?.email?.trim()) newErrors[`parent_email_${idx}`] = "L'email est requis.";
        else if (userParent?.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(userParent.email)) newErrors[`parent_email_${idx}`] = "Format d'email invalide.";
        if (!userParent?.telephone?.trim()) newErrors[`parent_telephone_${idx}`] = "Le téléphone est requis.";
        if (!relations[idx]?.type_relation) newErrors[`type_relation_${idx}`] = "La relation est requise.";
      });

      // Validation des relations parent-enfant (pas plus d'un père ou d'une mère)
      const relationCounts = { père: 0, mère: 0, tuteur: 0 };
      relations.forEach((relation, idx) => {
        if (relation.type_relation) {
          relationCounts[relation.type_relation as keyof typeof relationCounts]++;
        }
      });

      // Vérifier qu'il n'y a pas plus d'un père
      if (relationCounts.père > 1) {
        relations.forEach((relation, idx) => {
          if (relation.type_relation === 'père') {
            newErrors[`type_relation_${idx}`] = "Un élève ne peut avoir qu'un seul père.";
          }
        });
      }

      // Vérifier qu'il n'y a pas plus d'une mère
      if (relationCounts.mère > 1) {
        relations.forEach((relation, idx) => {
          if (relation.type_relation === 'mère') {
            newErrors[`type_relation_${idx}`] = "Un élève ne peut avoir qu'une seule mère.";
          }
        });
      }
    }

    if (step === 3) {
      if (hasAttendedSchool) {
        if (!lastSchool.code_gresa?.trim()) newErrors.code_gresa = "Le code GRESA est requis.";
        if (!lastSchool.nom?.trim()) newErrors.nom = "Le nom de l'école est requis.";
        if (!lastSchool.type?.trim()) newErrors.type = "Le type d'école est requis.";
        if (!lastSchool.cycle?.trim()) newErrors.cycle = "Le cycle scolaire est requis.";
        if (!lastSchool.adresse?.trim()) newErrors.schoolAdresse = "L'adresse de l'école est requise.";
      }
      if (!selectedClassId) newErrors.classe = "La classe est requise.";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>,
    section: string,
    index?: number
  ) => {
    const { name, value } = e.target;

    if (section === "eleve") {
      if (["nom", "prenom", "sexe", "date_naissance", "lieu_naissance", "nationalite", "adresse", "telephone", "email"].includes(name)) {
        setUserEleve((prev) => ({ ...prev, [name]: value }));
      } else {
        setEleve((prev) => ({ ...prev, [name]: value }));
      }
    }
    if (section === "parent" && typeof index === "number") {
      const fieldName = name.startsWith("sexe_") ? "sexe" : name;
      if (
        ["nom", "prenom", "date_naissance", "lieu_naissance", "nationalite", "adresse", "telephone", "email", "sexe"].includes(fieldName)
      ) {
        setUsersParent((prev) => {
          const updated = [...prev];
          updated[index] = { ...updated[index], [fieldName]: value };
          return updated;
        });
      } else {
        setParents((prev) => {
          const updated = [...prev];
          updated[index] = { ...updated[index], [name]: value };
          return updated;
        });
      }
    }
    if (section === "relation" && typeof index === "number") {
      setRelations((prev) => {
        const updated = [...prev];
        updated[index] = { ...updated[index], [name]: value };
        return updated;
      });
    }
    if (section === "lastSchool") {
      setLastSchool((prev) => ({ ...prev, [name]: value }));
    }
  };

  const nextStep = () => {
    const isValid = validateStep();
    if (!isValid) {
      console.log("Erreurs de validation:", errors);
      return;
    }
    if (step < 3) {
      setStep(step + 1);
    }
  };

  const prevStep = () => {
    if (step > 1) setStep(step - 1);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateStep()) return;
    setIsSubmitting(true);

    const isEditing = initialEleve && initialEleve.id_eleve > 0;

    try {
      let eleveId;

      if (isEditing) {
        const userId = userEleve.id_utilisateur || initialEleve.id_utilisateur;
        if (!userId) throw new Error("ID utilisateur manquant pour la mise à jour");

        await updateUser(userId, userEleve);
        const eleveData = { ...eleve };
        if (hasAttendedSchool && lastSchool.code_gresa) {
          eleveData.ancienne_ecole = lastSchool;
        }
        await updateEleve(initialEleve.id_eleve, eleveData);
        eleveId = initialEleve.id_eleve;
        setSubmitMessage("Élève modifié avec succès !");
      } else {
        const createdEleveUser = await register(userEleve);
        const eleveData = { ...eleve, id_utilisateur: createdEleveUser.data.id_user };
        if (hasAttendedSchool && lastSchool.code_gresa) {
          eleveData.ancienne_ecole = lastSchool;
        }
        const createdEleve = await addEleve(eleveData);
        eleveId = createdEleve.data.id_eleve;
        setSubmitMessage("Élève ajouté avec succès !");
      }

      // Gestion de l'inscription
      if (selectedClassId && activeAnneeScolaire) {
        const inscriptionData = {
          id_eleve: eleveId,
          id_classe: Number(selectedClassId),
          id_annee_scolaire: activeAnneeScolaire.id_annee_scolaire,
        };

        if (isEditing) {
          const existingInscription = await getInscriptionByEleve(eleveId);
          if (existingInscription.data.success && existingInscription.data.data) {
            // Vérifier si la classe a changé avant de mettre à jour
            const currentClassId = existingInscription.data.data.id_classe;
            const newClassId = Number(selectedClassId);

            if (currentClassId !== newClassId) {
              // Mise à jour de l'inscription existante seulement si la classe a changé
              const updateData = {
                id_eleve: eleveId,
                id_annee_scolaire: activeAnneeScolaire.id_annee_scolaire,
                old_id_classe: currentClassId,
                new_id_classe: newClassId,
              };
              console.log("Mise à jour de l'inscription:", updateData);
              const updateRes = await updateInscription(updateData);
              if (!updateRes.data.success) throw new Error(updateRes.data.message || "Erreur lors de la mise à jour de l'inscription");
            } else {
              console.log("Aucune modification de classe détectée, inscription non mise à jour");
            }
          } else {
            // Création d'une nouvelle inscription
            console.log("Création d'une nouvelle inscription:", inscriptionData);
            const addRes = await addInscription(inscriptionData);
            if (!addRes.data.success) throw new Error(addRes.data.message || "Erreur lors de l'inscription");
          }
        } else {
          // Création d'une inscription pour un nouvel élève
          console.log("Création d'une inscription pour nouvel élève:", inscriptionData);
          const addRes = await addInscription(inscriptionData);
          if (!addRes.data.success) throw new Error(addRes.data.message || "Erreur lors de l'inscription");
        }
      } else {
        throw new Error("Classe ou année scolaire non sélectionnée");
      }

      // Traitement des parents et relations
      for (let i = 0; i < parents.length; i++) {
        let parentId;

        if (parents[i].id_parent && parents[i].id_parent > 0) {
          parentId = parents[i].id_parent;
          const userId = usersParent[i].id_utilisateur;
          if (userId) {
            await updateUser(userId, usersParent[i]);
            await updateParent(parentId, parents[i]);
          }

          if (isEditing) {
            const updateResult = await updateRelation({
              id_parent: parentId,
              id_eleve: eleveId,
              type_relation: relations[i].type_relation,
            });
            if (!updateResult.data.success) {
              await addRelation({
                id_parent: parentId,
                id_eleve: eleveId,
                type_relation: relations[i].type_relation,
              });
            }
          } else {
            await addRelation({
              id_parent: parentId,
              id_eleve: eleveId,
              type_relation: relations[i].type_relation,
            });
          }
        } else {
          const createdParentUser = await register(usersParent[i]);
          const createdParent = await addParent({
            ...parents[i],
            id_utilisateur: createdParentUser.data.id_user
          });
          parentId = createdParent.data.id_parent;
          await addRelation({
            ...relations[i],
            id_eleve: eleveId,
            id_parent: parentId,
          });
        }
      }

      setSubmitSuccess(true);
      if (onSuccess) onSuccess();
    } catch (error: any) {
      console.error("Erreur lors de la soumission:", error);
      setSubmitSuccess(false);

      // Utiliser le message personnalisé de l'intercepteur ou extraire le message
      let errorMessage = error.userMessage || extractErrorMessage(error, "Erreur lors de la soumission du formulaire");

      // Messages spécifiques pour les erreurs de CIN
      if (errorMessage.includes('CIN est déjà utilisé')) {
        errorMessage = "❌ " + errorMessage + "\n\nVeuillez vérifier les numéros CIN saisis ou utiliser la fonction de recherche pour associer un parent existant.";
      }

      setSubmitMessage(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto my-6 bg-white rounded-xl shadow-md overflow-hidden">
      <div className="bg-gray-50 px-6 py-4 flex border-b text-center">
        <div className="flex-1 flex flex-col items-center">
          <div className={`w-6 h-6 rounded-full flex items-center justify-center ${step >= 1 ? "bg-primary text-white" : "bg-gray-200 text-gray-600"}`}>1</div>
          <span className="text-xs mt-1">Informations de l'élève</span>
        </div>
        <div className="flex-1 flex flex-col items-center">
          <div className={`w-6 h-6 rounded-full flex items-center justify-center ${step >= 2 ? "bg-primary text-white" : "bg-gray-200 text-gray-600"}`}>2</div>
          <span className="text-xs mt-1">Informations du parent</span>
        </div>
        <div className="flex-1 flex flex-col items-center">
          <div className={`w-6 h-6 rounded-full flex items-center justify-center ${step >= 3 ? "bg-primary text-white" : "bg-gray-200 text-gray-600"}`}>3</div>
          <span className="text-xs mt-1">Informations scolaires</span>
        </div>
      </div>
      {submitMessage && (
        <div className={`px-5 py-2 ${submitSuccess ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`}>
          {submitMessage}
        </div>
      )}
      <form onSubmit={handleSubmit} className="space-y-4 p-4">
        {step === 1 && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 overflow-y-auto p-2">
            <Input name="nom" required label="Nom" value={userEleve.nom} onChange={(e) => handleChange(e, "eleve")} error={errors.nom_fr} />
            <Input name="nom_ar" lang="ar" dir="rtl" required label="الإسم العائلي" arabicKeyboard value={eleve.nom_ar} onChange={(e) => handleChange(e, "eleve")} error={errors.nom_ar} />
            <Input name="prenom" required label="Prénom" value={userEleve.prenom} onChange={(e) => handleChange(e, "eleve")} error={errors.prenom_fr} />
            <Input name="prenom_ar" lang="ar" dir="rtl" required label="الإسم الشخصي" arabicKeyboard value={eleve.prenom_ar} onChange={(e) => handleChange(e, "eleve")} error={errors.prenom_ar} />
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Sexe<span className="text-red-500 font-bold"> *</span></label>
              <div className="flex space-x-6 items-center">
                {sexeOptions.map((option) => (
                  <label key={option.value} className="flex items-center">
                    <input
                      type="radio"
                      name="sexe"
                      value={option.value}
                      checked={userEleve.sexe === option.value}
                      onChange={(e) => handleChange(e, "eleve")}
                      className="mr-2"
                    />
                    {option.label}
                  </label>
                ))}
              </div>
              {errors.sexe && <p className="text-red-500 text-sm mt-1">{errors.sexe}</p>}
            </div>
            <Input
              type="date"
              name="date_naissance"
              required
              label="Date de naissance"
              value={userEleve.date_naissance}
              onChange={(e) => handleChange(e, "eleve")}
              error={errors.date_naissance}
              max={new Date().toISOString().split('T')[0]} // Empêche les dates futures
            />
            <Input name="lieu_naissance" required label="Lieu de naissance" value={userEleve.lieu_naissance} onChange={(e) => handleChange(e, "eleve")} error={errors.lieu_naissance_fr} />
            <Input name="lieu_naissance_ar" required label="مكان الإزدياد" arabicKeyboard value={eleve.lieu_naissance_ar} onChange={(e) => handleChange(e, "eleve")} error={errors.lieu_naissance_ar} />
            <Input name="adresse" required label="Adresse" value={userEleve.adresse} onChange={(e) => handleChange(e, "eleve")} error={errors.adresse} />
            <Input name="code_massar" label="Code Massar" value={eleve.code_massar ?? ""} onChange={(e) => handleChange(e, "eleve")} />
            <Input name="email" type="email" required label="Email" value={userEleve.email} onChange={(e) => handleChange(e, "eleve")} error={errors.email} placeholder="<EMAIL>" />
            <Input name="telephone" label="Téléphone" value={userEleve.telephone} onChange={(e) => handleChange(e, "eleve")} />
            <Select name="nationalite" label="Nationalité" options={nationaliteOptions} value={userEleve.nationalite || ""} onChange={(e) => handleChange(e, "eleve")} error={errors.nationalite} placeholder="Sélectionnez une nationalité" />
          </div>
        )}
        {step === 2 && (
          <div className="space-y-6">
            {parents.length === 0 && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <p className="text-yellow-800">Aucun parent trouvé pour cet élève.</p>
                  <button
                    type="button"
                    onClick={() => {
                      setParents([...parents, {
                        id_parent: 0,
                        id_utilisateur: 0,
                        nom_ar: "",
                        prenom_ar: "",
                        num_CIN: "",
                      }]);
                      setUsersParent([...usersParent, {
                        id_utilisateur: 0,
                        nom: "",
                        prenom: "",
                        email: "",
                        mot_de_passe: "",
                        role: "parent",
                        sexe: null,
                        date_naissance: "",
                        lieu_naissance: "",
                        nationalite: "marocain",
                        telephone: "",
                        adresse: "",
                        photo: "",
                        est_valide: true,
                        est_actif: false,
                      }]);
                      setRelations([...relations, {
                        id_parent: 0,
                        id_eleve: initialEleve?.id_eleve || 0,
                        type_relation: "",
                      }]);
                    }}
                    className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium"
                  >
                    Ajouter un parent
                  </button>
                </div>
              </div>
            )}
            {parents.map((parent, index) => (
              <div key={index} className="p-4 border rounded-lg space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">Parent {index + 1}</h3>
                  <div className="flex space-x-2">
                    <button
                      type="button"
                      onClick={() => openSearchModal(index)}
                      className="flex items-center px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors"
                      title="Rechercher un parent existant"
                    >
                      <Search className="h-4 w-4 mr-1" />
                      Rechercher
                    </button>
                    {parents.length > 1 && (
                      <button type="button" onClick={() => removeParent(index)} className="text-red-600 hover:text-red-800">
                        <MinusCircle className="h-5 w-5" />
                      </button>
                    )}
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input name="nom" placeholder="Nom" value={usersParent[index]?.nom || ""} onChange={(e) => handleChange(e, "parent", index)} error={errors[`parent_nom_fr_${index}`]} />
                  <Input name="nom_ar" placeholder="الإسم العائلي" value={parent.nom_ar} onChange={(e) => handleChange(e, "parent", index)} error={errors[`parent_nom_ar_${index}`]} />
                  <Input name="prenom" placeholder="Prénom" value={usersParent[index]?.prenom || ""} onChange={(e) => handleChange(e, "parent", index)} error={errors[`parent_prenom_fr_${index}`]} />
                  <Input name="prenom_ar" placeholder="الإسم الشخصي" value={parent.prenom_ar} onChange={(e) => handleChange(e, "parent", index)} error={errors[`parent_prenom_ar_${index}`]} />
                  <div className="flex space-x-6 items-center">
                    <label className="text-sm font-semibold text-gray-700">Sexe<span className="text-red-500 font-bold"> *</span></label>
                    <Input type="radio" name={`sexe_${index}`}  value="homme" label="Homme" id={`sexe-homme-${index}`} checked={usersParent[index]?.sexe === "homme"} onChange={(e) => handleChange(e, "parent", index)} />
                    <Input type="radio" name={`sexe_${index}`} value="femme" label="Femme" id={`sexe-femme-${index}`} checked={usersParent[index]?.sexe === "femme"} onChange={(e) => handleChange(e, "parent", index)} />
                    {errors[`parent_sexe_${index}`] && <p className="mt-1 text-sm text-red-600">{errors[`parent_sexe_${index}`]}</p>}
                  </div>
                  <Input
                    type="date"
                    name="date_naissance"
                    placeholder="Date de naissance"
                    value={usersParent[index]?.date_naissance || ""}
                    onChange={(e) => handleChange(e, "parent", index)}
                    error={errors[`parent_date_naissance_${index}`]}
                    max={new Date().toISOString().split('T')[0]} // Empêche les dates futures
                  />
                  <Input name="lieu_naissance" placeholder="Lieu de naissance" value={usersParent[index]?.lieu_naissance || ""} onChange={(e) => handleChange(e, "parent", index)} error={errors[`parent_lieu_naissance_${index}`]} />
                  <Input name="num_CIN" placeholder="CIN" value={parent.num_CIN} onChange={(e) => handleChange(e, "parent", index)} error={errors[`parent_num_CIN_${index}`]} />
                  <Select name="nationalite" options={nationaliteOptions} value={usersParent[index]?.nationalite || ""} onChange={(e) => handleChange(e, "parent", index)} error={errors[`parent_nationalite_${index}`]} placeholder="Sélectionnez une nationalité" />
                  <Input name="adresse" placeholder="Adresse" value={usersParent[index]?.adresse || ""} onChange={(e) => handleChange(e, "parent", index)} error={errors[`parent_adresse_${index}`]} />
                  <Input name="email" type="email" required placeholder="Email" value={usersParent[index]?.email || ""} onChange={(e) => handleChange(e, "parent", index)} error={errors[`parent_email_${index}`]} />
                  <Input name="telephone" placeholder="Téléphone" value={usersParent[index]?.telephone || ""} onChange={(e) => handleChange(e, "parent", index)} error={errors[`parent_telephone_${index}`]} />
                  <Select name="type_relation" options={relationOptions} value={relations[index]?.type_relation || ""} onChange={(e) => handleChange(e, "relation", index)} error={errors[`type_relation_${index}`]} placeholder="Sélectionnez le type de la relation" />
                </div>
              </div>
            ))}
            <button type="button" onClick={addParentField} className="flex items-center text-blue-600 hover:text-blue-800">
              <PlusCircle className="h-5 w-5 mr-2" />
              Ajouter un parent
            </button>
          </div>
        )}
        {step === 3 && (
          <div className="space-y-6">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-2">Informations scolaires</h3>
              <p className="text-sm text-gray-600">
                Remplissez les informations sur l'école précédente et sélectionnez la classe à affecter.
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <input
                type="checkbox"
                id="hasAttendedSchool"
                checked={hasAttendedSchool}
                onChange={(e) => {
                  setHasAttendedSchool(e.target.checked);
                  if (!e.target.checked) {
                    setLastSchool({
                      code_gresa: "",
                      nom: "",
                      type: "publique",
                      cycle: "maternelle",
                      adresse: "",
                    });
                  }
                }}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="hasAttendedSchool" className="text-sm font-medium text-gray-700">
                L'élève a fréquenté une école précédente
              </label>
            </div>
            {hasAttendedSchool && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
                <div className="md:col-span-2">
                  <h4 className="text-lg font-medium text-gray-900 mb-4">Informations de l'école précédente</h4>
                </div>
                <Input name="code_gresa" placeholder="Code GRESA" value={lastSchool.code_gresa} onChange={(e) => handleChange(e, "lastSchool")} error={errors.code_gresa} required />
                <Input name="nom" placeholder="Nom de l'école" value={lastSchool.nom} onChange={(e) => handleChange(e, "lastSchool")} error={errors.nom} required />
                <div className="flex space-x-6 items-center">
                  <label className="text-sm font-medium text-gray-700">Type d'école :</label>
                  <Input type="radio" name="type" value="publique" label="Publique" id="publique" checked={lastSchool.type === "publique"} onChange={(e) => handleChange(e, "lastSchool")} error={errors.type} />
                  <Input type="radio" name="type" value="privée" label="Privée" id="privée" checked={lastSchool.type === "privée"} onChange={(e) => handleChange(e, "lastSchool")} error={errors.type} />
                  {errors.type && <p className="mt-1 text-sm text-red-600">{errors.type}</p>}
                </div>
                <Select name="cycle" options={cycleOptions} value={lastSchool.cycle} onChange={(e) => handleChange(e, "lastSchool")} error={errors.cycle} placeholder="Sélectionnez le cycle" required />
                <Input name="adresse" placeholder="Adresse de l'école" value={lastSchool.adresse} onChange={(e) => handleChange(e, "lastSchool")} error={errors.schoolAdresse} required />
              </div>
            )}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Select
                name="classe"
                label="Classe à affecter"
                options={classes.map((c) => ({
                  value: c.id_classe,
                  label: c.nom_classe,
                }))}
                value={selectedClassId}
                onChange={(e) => setSelectedClassId(Number(e.target.value))}
                error={errors.classe}
                placeholder="Sélectionnez une classe"
                required
              />
            </div>
          </div>
        )}
        <div className="flex justify-between">
          {step > 1 && (
            <Button onClick={prevStep} variant="secondary" type="button">
              Précédent
            </Button>
          )}
          <div className="ml-auto">
            {step < 3 ? (
              <Button onClick={nextStep} type="button">
                Suivant
              </Button>
            ) : (
              <div className="flex space-x-3">
                <Button variant="success" disabled={isSubmitting}>
                  {isSubmitting ? "Envoi..." : (initialEleve ? "Modifier l'élève" : "Ajouter l'élève")}
                </Button>
              </div>
            )}
          </div>
        </div>
      </form>
      {showSearchModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-2xl max-w-md w-full">
            <div className="bg-blue-600 text-white p-4 rounded-t-xl">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold flex items-center">
                  <Search className="mr-2" size={20} />
                  Rechercher un parent existant
                </h3>
                <button onClick={closeSearchModal} className="text-white hover:text-gray-200">×</button>
              </div>
            </div>
            <div className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Numéro de CIN</label>
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={searchCIN}
                    onChange={(e) => setSearchCIN(e.target.value)}
                    placeholder="Saisir le numéro de CIN"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    onKeyDown={(e) => e.key === 'Enter' && handleSearchParent()}
                  />
                  <button
                    onClick={handleSearchParent}
                    disabled={isSearching}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center"
                  >
                    {isSearching ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    ) : (
                      <Search size={16} />
                    )}
                  </button>
                </div>
              </div>
              {searchResults && (
                <div className="mt-4">
                  {searchResults.success ? (
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                      <div className="flex items-center mb-2">
                        <UserCheck className="text-green-600 mr-2" size={20} />
                        <span className="font-medium text-green-800">Parent trouvé !</span>
                      </div>
                      <div className="text-sm text-gray-700 space-y-1">
                        <p><strong>Nom :</strong> {searchResults.data?.user?.nom || "Non renseigné"} {searchResults.data?.user?.prenom || ""}</p>
                        <p><strong>Nom arabe :</strong> {searchResults.data?.nom_ar || "Non renseigné"} {searchResults.data?.prenom_ar || ""}</p>
                        <p><strong>CIN :</strong> {searchResults.data?.num_CIN || "Non renseigné"}</p>
                        <p><strong>Email :</strong> {searchResults.data?.user?.email || "Non renseigné"}</p>
                        <p><strong>Téléphone :</strong> {searchResults.data?.user?.telephone || "Non renseigné"}</p>
                        <p><strong>Adresse :</strong> {searchResults.data?.user?.adresse || "Non renseigné"}</p>
                      </div>
                      <div className="mt-4 flex space-x-2">
                        <button
                          onClick={useExistingParent}
                          className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                        >
                          <UserPlus className="mr-2" size={16} />
                          Utiliser ce parent
                        </button>
                        <button
                          onClick={() => setSearchResults(null)}
                          className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                        >
                          Nouvelle recherche
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                      <div className="flex items-center">
                        <AlertCircle className="text-yellow-600 mr-2" size={20} />
                        <span className="font-medium text-yellow-800">
                          {searchResults.message || "Aucun parent trouvé avec ce CIN"}
                        </span>
                      </div>
                      <p className="text-sm text-yellow-700 mt-2">
                        Vous pouvez continuer à remplir le formulaire pour créer un nouveau parent.
                      </p>
                    </div>
                  )}
                </div>
              )}
            </div>
            <div className="bg-gray-50 px-6 py-4 rounded-b-xl">
              <button
                onClick={closeSearchModal}
                className="w-full px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
              >
                Fermer
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EleveForm;