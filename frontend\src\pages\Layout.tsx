import { useEffect, useState } from "react";
import { Outlet, useNavigate } from "react-router-dom";
import Sidebar from '../components/Sidebar';
import Header from '../components/Header';
import { useAuth } from "../context/AuthContext";



export default function Layout() {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const titles = [
    { path: '/admin/dashboard', title: 'Tableau de Bord' },
    { path: '/enseignant/dashboard', title: 'Tableau de Bord' },
    { path: '/eleve/dashboard', title: 'Tableau de Bord' },
    { path: '/parent/dashboard', title: 'Tableau de Bord' },
    { path: '/admin/school-settings', title: "Paramètre d'école" },
    { path: '/admin/eleves', title: 'Gestion des Élèves' },
    { path: '/admin/parents', title: 'Gestion des Parents' },
    { path: '/admin/enseignants', title: 'Gestion des Enseignants' },
    { path: '/admin/classes', title: 'Gestion des Classes' },
    { path: '/admin/salles', title: 'Gestion des Salles' },
    { path: '/admin/matieres', title: 'Gestion des Matières' },
    { path: '/admin/emploi-du-temps', title: 'Gestion des Emploi du Temps' },
    { path: '/admin/examens', title: 'Gestion des Examens' },
    { path: '/admin/bulletins', title: 'Gestion des Bulletins' },
    { path: '/admin/paiements', title: 'Gestion des Paiements' },
    { path: '/admin/transport', title: 'Gestion du Transport' },
    { path:'/admin/activites', title: 'Gestion des Activités' },
    { path:'/admin/presences', title: 'Gestion des Présences' },
    { path:'/admin/evenements', title: 'Événements' },

    { path: '/enseignant/eleves', title: 'Liste des Élèves' },
    { path: '/enseignant/classes', title: 'Listes des Classes' },
    { path: '/enseignant/emploi-du-temps', title: 'Emploi du Temps' },
    { path: '/enseignant/presences', title: 'Gestion des Présences' },
    { path: '/enseignant/activites', title: 'Gestion des Activités' },
    { path: '/enseignant/examens', title: 'Gestion des Examens' },
    { path: '/enseignant/bulletins', title: 'Gestion des Bulletins' },

    { path: '/eleve/emploi-du-temps', title: 'Mon Emploi du Temps' },
    { path: '/eleve/presences', title: 'Mes Présences' },
    { path: '/eleve/activites', title: 'Mes Activités' },
    { path: '/eleve/examens', title: 'Mes Examens' },
    { path: '/eleve/bulletins', title: 'Mes Bulletins' },
    { path: '/eleve/notes', title: 'Mes Notes' },
    { path: '/eleve/devoirs', title: 'Mes Devoirs' },
    { path: '/eleve/cours', title: 'Mes Cours' },
    { path: '/eleve/messages', title: 'Mes Messages' },

    { path: '/parent/presences', title: 'Présences' },
    { path: '/parent/activites', title: 'Activités' },
    { path: '/parent/examens', title: 'Examens' },
    { path: '/parent/bulletins', title: 'Bulletins' },
    { path: '/parent/transport', title: 'Transport' },
  ];

  const getPageTitle = () => {
    const currentPath = location.pathname;
    const match = titles.find(({ path }) => currentPath.startsWith(path));
    return match ? match.title : 'École Administration';
  };

  useEffect(() => {
    // const token = localStorage.getItem("token");
    // if (!token) {
      if (!user) {
      navigate("/login");
    }
  }, [user,navigate]);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };


 
  return (
    <div className="flex h-screen font-poppins ">
      {/* Sidebar Desktop */}
      <div className="hidden md:block w-[8%] lg:w-[15%]">
        <Sidebar />
      </div>

      {/* Sidebar Mobile - Overlay */}
      {isMobileMenuOpen && (
        <div className="fixed inset-0 z-50 md:hidden">
          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300 ease-in-out"
            onClick={closeMobileMenu}
          ></div>

          {/* Sidebar */}
          <div 
            className="relative z-50 w-52 h-full bg-white shadow-lg transform transition-transform duration-300 ease-in-out translate-x-0"
          >
            <Sidebar onItemClick={closeMobileMenu} />
          </div>
        </div>
      )}
     

      {/* Contenu principal avec Header */}
      <div className="w-full md:w-[92%] lg:w-[85%] bg-background flex flex-col overflow-y-auto custom-scrollbar">
        <Header
          title={getPageTitle()}
          onMenuClick={toggleMobileMenu}
          isMobileMenuOpen={isMobileMenuOpen}
        />
        <main className="mt-16 p-6 smooth-scroll"><Outlet /></main>
      </div>
    </div>
  );
}