import React from "react";
import { X } from "lucide-react";

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  title?: string;
}

const Modal: React.FC<ModalProps> = ({ isOpen, onClose, children, title }) => {
  if (!isOpen) return null;

  const handleBackdropClick = (e: React.MouseEvent) => {
    // Empêcher la fermeture si le clic est sur l'arrière-plan
    // Ne fermer que si c'est un clic sur le bouton X
    if (e.target === e.currentTarget) {
      e.preventDefault();
      e.stopPropagation();
    }
  };

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/50"
      onClick={handleBackdropClick}
    >
      <div className="bg-gray-100 rounded-lg shadow-lg w-full max-w-4xl pt-2 px-8 relative max-h-[95vh] overflow-y-auto modal-scrollbar">

        
        <button
          onClick={onClose}
          className="absolute top-3 right-3 text-gray-500 hover:text-gray-700"
        >
          <X size={20} />
        </button>
        {title && <h2 className="text-center text-2xl font-bold text-secondary mt-2">{title}</h2>}
        {children}
      </div>
    </div>
  );
};

export default Modal;
