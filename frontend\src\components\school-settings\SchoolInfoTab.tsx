import React, { useState, useEffect } from 'react';
import { Save, Upload, MapPin, Phone, Mail, Globe, User, Camera } from 'lucide-react';
import Input from '../Input';
import Button from '../Button';
import Select from '../Select';
import { useSchoolContext } from '../../pages/admin/SchoolSettings';

interface SchoolInfo {
  nom_ecole: string;
  adresse: string;
  telephone: string;
  email: string;
  site_web: string;
  directeur: string;
  logo: string;
  type_etablissement: 'publique' | 'privée';
  cycles_proposes: string[];
  description: string;
}

const SchoolInfoTab: React.FC = () => {
  const { setCyclesProposed } = useSchoolContext();
  const [schoolInfo, setSchoolInfo] = useState<SchoolInfo>({
    nom_ecole: '',
    adresse: '',
    telephone: '',
    email: '',
    site_web: '',
    directeur: '',
    logo: '',
    type_etablissement: 'privée',
    cycles_proposes: [],
    description: ''
  });

  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);



  const cycleOptions = [
    { value: 'maternelle', label: 'Maternelle' },
    { value: 'primaire', label: 'Primaire' },
    { value: 'collège', label: 'Collège' },
    { value: 'lycée', label: 'Lycée' }
  ];

  // Charger les données de l'école
  useEffect(() => {
    loadSchoolInfo();
  }, []);

  const loadSchoolInfo = async () => {
    setIsLoading(true);
    try {
      // TODO: Appel API pour récupérer les informations de l'école
      // Pour l'instant, on utilise des données par défaut
      const defaultData: SchoolInfo = {
        nom_ecole: 'École Privée ScolaNova',
        adresse: '123 Rue de l\'Éducation, Casablanca, Maroc',
        telephone: '+212 5 22 XX XX XX',
        email: '<EMAIL>',
        site_web: 'https://www.scolanova.ma',
        directeur: 'M. Ahmed BENALI',
        logo: '',
        type_etablissement: 'privée',
        cycles_proposes: ['maternelle', 'primaire'],
        description: 'École privée offrant un enseignement de qualité dans un environnement bienveillant et moderne.'
      };
      setSchoolInfo(defaultData);
      // Mettre à jour le contexte avec les cycles proposés
      setCyclesProposed(defaultData.cycles_proposes);
    } catch (error) {
      console.error('Erreur lors du chargement des informations:', error);
      setMessage({ type: 'error', text: 'Erreur lors du chargement des informations' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setSchoolInfo(prev => ({ ...prev, [name]: value }));
  };

  const handleCycleChange = (cycle: string, checked: boolean) => {
    const newCycles = checked
      ? [...schoolInfo.cycles_proposes, cycle]
      : schoolInfo.cycles_proposes.filter(c => c !== cycle);

    setSchoolInfo(prev => ({
      ...prev,
      cycles_proposes: newCycles
    }));

    // Mettre à jour le contexte immédiatement
    setCyclesProposed(newCycles);
  };

  const handleLogoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // TODO: Implémenter l'upload du logo
      const reader = new FileReader();
      reader.onload = (event) => {
        setSchoolInfo(prev => ({ ...prev, logo: event.target?.result as string }));
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      // TODO: Appel API pour sauvegarder les informations
      console.log('Sauvegarde des informations:', schoolInfo);
      
      // Simulation d'un délai d'API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setMessage({ type: 'success', text: 'Informations sauvegardées avec succès!' });
      
      // Effacer le message après 3 secondes
      setTimeout(() => setMessage(null), 3000);
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      setMessage({ type: 'error', text: 'Erreur lors de la sauvegarde' });
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Chargement...</span>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Message de statut */}
      {message && (
        <div className={`p-4 rounded-lg ${
          message.type === 'success' ? 'bg-green-50 text-green-800 border border-green-200' : 'bg-red-50 text-red-800 border border-red-200'
        }`}>
          {message.text}
        </div>
      )}



      {/* Section Logo */}
      <div className="bg-gray-50 p-6 rounded-lg">
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <Camera className="h-5 w-5 mr-2" />
          Logo de l'école
        </h3>
        <div className="flex items-center space-x-6">
          <div className="flex-shrink-0">
            {schoolInfo.logo ? (
              <img
                src={schoolInfo.logo}
                alt="Logo de l'école"
                className="h-24 w-24 object-cover rounded-lg border-2 border-gray-200"
              />
            ) : (
              <div className="h-24 w-24 bg-gray-200 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300">
                <Camera className="h-8 w-8 text-gray-400" />
              </div>
            )}
          </div>
          <div>
            <label className="block">
              <span className="sr-only">Choisir un logo</span>
              <input
                type="file"
                accept="image/*"
                onChange={handleLogoUpload}
                className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
              />
            </label>
            <p className="text-xs text-gray-500 mt-1">PNG, JPG jusqu'à 2MB</p>
          </div>
        </div>
      </div>

      {/* Informations générales */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="md:col-span-2">
          <Input
            name="nom_ecole"
            label="Nom de l'école"
            value={schoolInfo.nom_ecole}
            onChange={handleInputChange}
            required
            leftIcon={<User className="h-4 w-4" />}
          />
        </div>

        <div className="md:col-span-2">
          <Input
            name="adresse"
            label="Adresse"
            value={schoolInfo.adresse}
            onChange={handleInputChange}
            required
            leftIcon={<MapPin className="h-4 w-4" />}
          />
        </div>

        <Input
          name="telephone"
          label="Téléphone"
          value={schoolInfo.telephone}
          onChange={handleInputChange}
          leftIcon={<Phone className="h-4 w-4" />}
        />

        <Input
          name="email"
          label="Email"
          type="email"
          value={schoolInfo.email}
          onChange={handleInputChange}
          leftIcon={<Mail className="h-4 w-4" />}
        />

        <Input
          name="site_web"
          label="Site web"
          value={schoolInfo.site_web}
          onChange={handleInputChange}
          leftIcon={<Globe className="h-4 w-4" />}
          placeholder="https://www.exemple.com"
        />

        <Input
          name="directeur"
          label="Directeur/Directrice"
          value={schoolInfo.directeur}
          onChange={handleInputChange}
          leftIcon={<User className="h-4 w-4" />}
        />
      </div>

      {/* Cycles proposés */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Cycles proposés
        </label>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {cycleOptions.map((cycle) => (
            <label key={cycle.value} className="flex items-center space-x-2 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
              <input
                type="checkbox"
                checked={schoolInfo.cycles_proposes.includes(cycle.value)}
                onChange={(e) => handleCycleChange(cycle.value, e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="text-sm font-medium text-gray-700">{cycle.label}</span>
            </label>
          ))}
        </div>
      </div>

      {/* Description */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Description
        </label>
        <textarea
          name="description"
          value={schoolInfo.description}
          onChange={handleInputChange}
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="Description de l'école..."
        />
      </div>

      {/* Bouton de sauvegarde */}
      <div className="flex justify-end pt-6 border-t">
        <Button
          onClick={handleSave}
          disabled={isSaving}
          className="flex items-center space-x-2"
        >
          <Save className="h-4 w-4" />
          <span>{isSaving ? 'Sauvegarde...' : 'Sauvegarder'}</span>
        </Button>
      </div>
    </div>
  );
};

export default SchoolInfoTab;
