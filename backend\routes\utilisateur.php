<?php

require_once __DIR__ . '/../controllers/UtilisateurController.php';

function handleUtilisateurRoutes($uri, $method, $pdo) {
    $controller = new UtilisateurController($pdo);

    // GET /utilisateurs/admins - Récupérer uniquement les administrateurs (AVANT la route avec ID)
    if ($uri === '/utilisateurs/admins' && $method === 'GET') {
        $controller->getAdmins();
        return true;
    }

    // GET /utilisateurs - Récupérer tous les utilisateurs
    if ($uri === '/utilisateurs' && $method === 'GET') {
        $controller->getAll();
        return true;
    }

    // GET /utilisateurs/{id} - Récupérer un utilisateur par ID
    if (preg_match('#^/utilisateurs/(\d+)$#', $uri, $matches) && $method === 'GET') {
        $id = (int)$matches[1];
        $controller->getById($id);
        return true;
    }

    // PUT /utilisateurs/{id} - Mettre à jour un utilisateur
    if (preg_match('#^/utilisateurs/(\d+)$#', $uri, $matches) && $method === 'PUT') {
        $id = (int)$matches[1];
        $controller->update($id);
        return true;
    }

    // DELETE /utilisateurs/{id} - Supprimer un utilisateur
    if (preg_match('#^/utilisateurs/(\d+)$#', $uri, $matches) && $method === 'DELETE') {
        $id = (int)$matches[1];
        $controller->delete($id);
        return true;
    }

    return false;
}

?>
