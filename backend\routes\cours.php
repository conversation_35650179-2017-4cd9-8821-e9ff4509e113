<?php

require_once __DIR__ . '/../controllers/CoursController.php';

function handleCoursRoutes($uri, $method, $pdo) {
    $controller = new CoursController($pdo);

    // GET /cours/classe/{id} - Récupérer les cours par classe (AVANT la route avec ID générique)
    if (preg_match('#^/cours/classe/(\d+)$#', $uri, $matches) && $method === 'GET') {
        $id_classe = (int)$matches[1];
        $controller->getByClasse($id_classe);
        return true;
    }

    // GET /cours/enseignant/{id} - Récupérer les cours par enseignant
    if (preg_match('#^/cours/enseignant/(\d+)$#', $uri, $matches) && $method === 'GET') {
        $id_enseignant = (int)$matches[1];
        $controller->getByEnseignant($id_enseignant);
        return true;
    }

    // GET /cours - Récupérer tous les cours
    if ($uri === '/cours' && $method === 'GET') {
        $controller->getAll();
        return true;
    }

    // POST /cours - Créer un nouveau cours
    if ($uri === '/cours' && $method === 'POST') {
        $controller->create();
        return true;
    }

    // GET /cours/{id} - Récupérer un cours par ID
    if (preg_match('#^/cours/(\d+)$#', $uri, $matches) && $method === 'GET') {
        $id = (int)$matches[1];
        $controller->getById($id);
        return true;
    }

    // PUT /cours/{id} - Mettre à jour un cours
    if (preg_match('#^/cours/(\d+)$#', $uri, $matches) && $method === 'PUT') {
        $id = (int)$matches[1];
        $controller->update($id);
        return true;
    }

    // DELETE /cours/{id} - Supprimer un cours
    if (preg_match('#^/cours/(\d+)$#', $uri, $matches) && $method === 'DELETE') {
        $id = (int)$matches[1];
        $controller->delete($id);
        return true;
    }

    return false;
}

?>
