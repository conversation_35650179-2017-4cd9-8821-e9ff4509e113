import React, { useState, useEffect } from "react";
import { Plus, Edit, Trash2, User as UserIcon, Shield, CheckCircle, XCircle, X } from "lucide-react";
import Input from "../Input";
import Select from "../Select";
import Button from "../Button";
import { User } from "../../types";
import { register, getUtilisateurs, updateUser, deleteUser } from "../../services/api";
import { useDeleteConfirmation } from "../../hooks/useConfirmation";
import { extractErrorMessage, extractSuccessMessage } from "../../utils/errorUtils";
import { useFormErrorHandler } from "../../hooks/useErrorHandler";
import ErrorDisplay from "../ErrorDisplay";

const UtilisateursTab: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [formData, setFormData] = useState<Partial<User>>({
    nom: "",
    prenom: "",
    email: "",
    role: "admin",
    sexe: "homme",
    telephone: "",
    est_actif: true,
  });
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const { error, validationErrors, clearError, clearFieldError, clearValidationErrors, handleError } = useFormErrorHandler();

  // Hook pour la confirmation de suppression
  const { confirmDelete, ConfirmationComponent } = useDeleteConfirmation();

  // Charger les utilisateurs admin au montage du composant
  useEffect(() => {
    fetchUsers();
  }, []);

  // Effet pour faire disparaître le message après 3 secondes
  useEffect(() => {
    if (message) {
      const timer = setTimeout(() => {
        setMessage(null);
      }, 3000); // 3 secondes

      return () => clearTimeout(timer);
    }
  }, [message]);

  const fetchUsers = async () => {
    setIsLoading(true);
    try {
      const response = await getUtilisateurs();
      if (response.data.success) {
        // L'API retourne déjà seulement les utilisateurs admin
        setUsers(response.data.data);
      }
    } catch (error: any) {
      console.error("Erreur lors du chargement des utilisateurs:", error);
      const errorMessage = extractErrorMessage(error, 'Erreur lors du chargement des utilisateurs');
      setMessage({ type: 'error', text: errorMessage });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAdd = () => {
    setEditingUser(null);
    setFormData({
      nom: "",
      prenom: "",
      email: "",
      role: "admin",
      sexe: "homme",
      telephone: "",
      est_actif: true,
    });
    clearValidationErrors(); // Effacer les erreurs de validation
    clearError(); // Effacer l'erreur globale
    setShowForm(true);
  };

  const handleEdit = (user: User) => {
    setEditingUser(user);
    setFormData({
      nom: user.nom,
      prenom: user.prenom,
      email: user.email,
      role: user.role,
      sexe: user.sexe,
      telephone: user.telephone,
      est_actif: user.est_actif,
    });
    clearValidationErrors(); // Effacer les erreurs de validation
    clearError(); // Effacer l'erreur globale
    setShowForm(true);
  };

  // Validation côté client
  const validateForm = (): boolean => {
    if (!formData.nom?.trim()) {
      setMessage({ type: 'error', text: 'Le nom est requis' });
      return false;
    }

    if (!formData.prenom?.trim()) {
      setMessage({ type: 'error', text: 'Le prénom est requis' });
      return false;
    }

    if (!formData.email?.trim()) {
      setMessage({ type: 'error', text: 'L\'email est requis' });
      return false;
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      setMessage({ type: 'error', text: 'Format d\'email invalide' });
      return false;
    }

    if (formData.telephone && !/^[0-9+\-\s()]+$/.test(formData.telephone)) {
      setMessage({ type: 'error', text: 'Format de téléphone invalide' });
      return false;
    }

    return true;
  };

  // Fonction pour gérer les changements d'input avec effacement des erreurs
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;

    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));

    // Effacer l'erreur de validation pour ce champ
    if (validationErrors[name]) {
      clearFieldError(name);
    }

    // Effacer l'erreur globale aussi
    if (error) {
      clearError();
    }
  };

  const handleDelete = async (id: number) => {
    try {
      const response = await deleteUser(id);
      const successMessage = extractSuccessMessage(response, 'Utilisateur supprimé avec succès');
      setMessage({ type: 'success', text: successMessage });
      fetchUsers();
    } catch (error: any) {
      console.error('Erreur lors de la suppression:', error);
      const errorMessage = extractErrorMessage(error, 'Erreur lors de la suppression');
      setMessage({ type: 'error', text: errorMessage });
    }
  };

  const handleDeleteClick = (user: User) => {
    const userName = `${user.prenom} ${user.nom}`;
    confirmDelete(
      () => handleDelete(user.id_utilisateur),
      userName,
      "Cette action supprimera définitivement cet utilisateur administrateur et toutes ses données associées."
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Valider le formulaire côté client
    if (!validateForm()) {
      return;
    }

    try {
      if (editingUser) {
        const response = await updateUser(editingUser.id_utilisateur, formData);
        const successMessage = extractSuccessMessage(response, 'Utilisateur modifié avec succès');
        setMessage({ type: 'success', text: successMessage });
        setShowForm(false); // Fermer la modal seulement en cas de succès
        fetchUsers();
      } else {
        // Préparer les données avec tous les champs requis
        const userData = {
          ...formData,
          mot_de_passe: 'admin123',
          nationalite: 'Marocaine', // Valeur par défaut
          adresse: '', // Valeur par défaut
          date_naissance: null,
          lieu_naissance: null,
          est_valide: false, // Forcer le changement de mot de passe
          est_actif: true
        } as User;

        console.log('Données envoyées pour création:', userData);
        const response = await register(userData);
        console.log('Réponse de création:', response);
        const successMessage = extractSuccessMessage(response, 'Utilisateur créé avec succès');
        setMessage({ type: 'success', text: successMessage });
        setShowForm(false); // Fermer la modal seulement en cas de succès
        fetchUsers();
      }
    } catch (error: any) {
      console.error('Erreur lors de la sauvegarde:', error);

      // Utiliser le message personnalisé de l'intercepteur ou extraire le message
      const errorMessage = error.userMessage || extractErrorMessage(error, 'Erreur lors de la sauvegarde');
      setMessage({ type: 'error', text: errorMessage });
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full"></div>
        <span className="ml-2">Chargement...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Messages - seulement quand la modal n'est pas ouverte */}
      {message && !showForm && (
        <div className={`p-4 rounded-md relative ${message.type === 'success' ? 'bg-green-50 text-green-800 border border-green-200' : 'bg-red-50 text-red-800 border border-red-200'}`}>
          <div className="flex items-center justify-between">
            <span>{message.text}</span>
            <button
              onClick={() => setMessage(null)}
              className="ml-4 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      {/* En-tête avec bouton d'ajout */}
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">Utilisateurs Administrateurs</h3>
        <Button
          icon={<Plus size={16} />}
          variant="primary"
          onClick={handleAdd}
        >
          Ajouter un admin
        </Button>
      </div>

      {/* Liste des utilisateurs */}
      <div className="bg-white rounded-lg border border-gray-200">
        {users.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <UserIcon className="mx-auto mb-2 w-10 h-10" />
            <p>Aucun utilisateur administrateur trouvé</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {users.map((user) => (
              <div key={user.id_utilisateur} className="p-4 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <UserIcon className="w-5 h-5 text-blue-600" />
                      </div>
                    </div>
                    <div>
                      <div className="flex items-center space-x-2">
                        <p className="text-sm font-medium text-gray-900">
                          {user.nom} {user.prenom}
                        </p>
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          <Shield className="w-3 h-3 mr-1" />
                          {user.role}
                        </span>
                        {user.est_actif ? (
                          <div title="Actif">
                            <CheckCircle className="w-4 h-4 text-green-500" />
                          </div>
                        ) : (
                          <div title="Inactif">
                            <XCircle className="w-4 h-4 text-red-500" />
                          </div>
                        )}
                      </div>
                      <p className="text-sm text-gray-500">{user.email}</p>
                      {user.telephone && (
                        <p className="text-sm text-gray-500">{user.telephone}</p>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      icon={<Edit size={14} />}
                      onClick={() => handleEdit(user)}
                    >
                      Modifier
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      icon={<Trash2 size={14} />}
                      onClick={() => handleDeleteClick(user)}
                      className="text-red-600 hover:text-red-700 border-red-200 hover:border-red-300"
                    >
                      Supprimer
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Formulaire modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            {/* En-tête de la modal */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">
                {editingUser ? 'Modifier l\'utilisateur' : 'Ajouter un utilisateur'}
              </h3>
              <button
                onClick={() => setShowForm(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Message d'erreur dans la modal */}
            {message && (
              <div className={`mx-6 mt-4 p-4 rounded-lg relative ${
                message.type === 'success' ? 'bg-green-50 text-green-800 border border-green-200' : 'bg-red-50 text-red-800 border border-red-200'
              }`}>
                <div className="flex items-center justify-between">
                  <span>{message.text}</span>
                  <button
                    onClick={() => setMessage(null)}
                    className="ml-4 text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>
            )}

            {/* Erreur du hook */}
            {error && (
              <div className="mx-6 mt-4">
                <ErrorDisplay
                  error={error}
                  onClose={clearError}
                  variant="error"
                />
              </div>
            )}

            {/* Corps de la modal */}
            <form onSubmit={handleSubmit} className="p-6 space-y-4">
              <Input
                name="nom"
                label="Nom"
                value={formData.nom || ''}
                onChange={handleInputChange}
                required
                error={validationErrors.nom}
              />

              <Input
                name="prenom"
                label="Prénom"
                value={formData.prenom || ''}
                onChange={handleInputChange}
                required
                error={validationErrors.prenom}
              />

              <Input
                name="email"
                type="email"
                label="Email"
                value={formData.email || ''}
                onChange={handleInputChange}
                required
                error={validationErrors.email}
              />

              <Input
                name="telephone"
                label="Téléphone"
                value={formData.telephone || ''}
                onChange={handleInputChange}
                error={validationErrors.telephone}
              />

              <Select
                name="sexe"
                label="Sexe"
                value={formData.sexe || 'homme'}
                onChange={handleInputChange}
                options={[
                  { value: 'homme', label: 'Homme' },
                  { value: 'femme', label: 'Femme' }
                ]}
              />

              {/* Pied de la modal */}
              <div className="flex justify-end space-x-3 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowForm(false)}
                >
                  Annuler
                </Button>
                <Button type="submit" variant="primary">
                  {editingUser ? 'Modifier' : 'Ajouter'}
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Composant de confirmation de suppression */}
      <ConfirmationComponent />
    </div>
  );
};

export default UtilisateursTab;
