import React, { forwardRef, useRef, useState } from 'react';
import ArabicKeyboard from './ArabicKeyboard';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  type?: string;
  label?: string;
  required?: boolean;
  placeholder?: string;
  name?: string;
  error?: string;
  className?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  arabicKeyboard?: boolean;
}

const Input = forwardRef<HTMLInputElement, InputProps>(
  ({ type = "text", label, required = false, placeholder, error, className = '', leftIcon, rightIcon, arabicKeyboard = false, ...props }, ref) => {
    const internalRef = useRef<HTMLInputElement>(null);
    const inputRef = (ref as React.RefObject<HTMLInputElement>) || internalRef;

    const [showKeyboard, setShowKeyboard] = useState(false);
    const [value, setValue] = useState(props.value?.toString() || '');

    const handleKeyPress = (char: string) => {
      const newValue = char === '←' ? value.slice(0, -1) : value + char;
      setValue(newValue);

      if (inputRef.current) {
        const nativeInputValueSetter = Object.getOwnPropertyDescriptor(HTMLInputElement.prototype, 'value')?.set;
        nativeInputValueSetter?.call(inputRef.current, newValue);

        const event = new Event('input', { bubbles: true });
        inputRef.current.dispatchEvent(event);
        inputRef.current.focus();
      }
    };

    if (type === "radio") {
      return (
        <div className="flex items-center space-x-2">
          <input
            ref={ref}
            type="radio"
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
            {...props}
          />
          {label && (
            <label htmlFor={props.id || props.name} className="text-sm text-gray-700">
              {label}{required && <span className="text-red-500 font-bold"> *</span>}
            </label>
          )}
        </div>
      );
    }

    return (
      <div className="w-full relative">
        
        {label && (
          <label className="block text-sm font-semibold mb-1 text-gray-700"
            lang={arabicKeyboard ? 'ar' : undefined}
            dir={arabicKeyboard ? 'rtl' : undefined}
          >
            {label}{required && <span className="text-red-500 font-bold"> *</span>}
            
          </label>
        )}

        <div className={`relative flex items-center border rounded-md px-3 py-2
          ${error ? 'border-red-500' : 'border-gray-300'}
          ${props.disabled ? 'bg-gray-100 border-gray-200' : 'focus-within:border-blue-600'}
          transition`}>

          {leftIcon && <div className="text-gray-500 mr-2">{leftIcon}</div>}

          <input
            ref={inputRef}
            type={type}
            placeholder={placeholder}
            className={`flex-1 w-full bg-transparent text-sm
               placeholder-gray-400 focus:outline-none focus:ring-primary sm:text-sm
              ${leftIcon ? 'pl-0' : ''} ${rightIcon ? 'pr-0' : ''}
              ${props.disabled ? 'text-gray-500 cursor-not-allowed' : ''}
              ${className}`}
            lang={arabicKeyboard ? 'ar' : undefined}
            dir={arabicKeyboard ? 'rtl' : undefined}
            onFocus={() => arabicKeyboard && setShowKeyboard(true)}
            onBlur={() => arabicKeyboard && setTimeout(() => setShowKeyboard(false), 200)}
            value={value}
            onChange={(e) => {
              setValue(e.target.value);
              props.onChange?.(e);
            }}
            {...props}
          />

          {rightIcon && <div className="text-gray-500 ml-2">{rightIcon}</div>}
        </div>

        {showKeyboard && arabicKeyboard && (
          <div className="absolute top-full mt-1 z-50">
            <ArabicKeyboard onKeyPress={handleKeyPress} />
          </div>
        )}

        {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
      </div>
    );
  }
);

export default Input;
