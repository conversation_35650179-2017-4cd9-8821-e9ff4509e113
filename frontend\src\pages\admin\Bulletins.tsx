import React, { useState, useEffect } from 'react';
import { Download, Eye, Filter, Users, BookOpen, TrendingUp, Award } from 'lucide-react';
import Button from '../../components/Button';
import Select from '../../components/Select';
import { getBulletinsClasse, downloadBulletinPDF, getClasses } from '../../services/api';
import { BulletinResume, Classe } from '../../types';
import BulletinModal from '../../components/BulletinModal';

const Bulletins: React.FC = () => {
  const [bulletins, setBulletins] = useState<BulletinResume[]>([]);
  const [classes, setClasses] = useState<Classe[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedClasse, setSelectedClasse] = useState<string>('');
  const [selectedSemestre, setSelectedSemestre] = useState<string>('S1');
  const [showBulletinModal, setShowBulletinModal] = useState(false);
  const [selectedEleve, setSelectedEleve] = useState<number | null>(null);

  useEffect(() => {
    fetchClasses();
  }, []);

  useEffect(() => {
    if (selectedClasse) {
      fetchBulletins();
    }
  }, [selectedClasse, selectedSemestre]);

  const fetchClasses = async () => {
    try {
      const response = await getClasses();
      setClasses(response.data.data || []);
    } catch (error) {
      console.error('Erreur lors du chargement des classes:', error);
      setError('Erreur lors du chargement des classes');
    }
  };

  const fetchBulletins = async () => {
    if (!selectedClasse) return;

    try {
      setLoading(true);
      setError(null);
      const response = await getBulletinsClasse(parseInt(selectedClasse), selectedSemestre);
      setBulletins(response.data.data || []);
    } catch (error: any) {
      console.error('Erreur lors du chargement des bulletins:', error);
      setError('Erreur lors du chargement des bulletins');
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadPDF = async (idEleve: number, nom: string, prenom: string) => {
    try {
      const response = await downloadBulletinPDF(idEleve, selectedSemestre);
      
      // Créer un lien de téléchargement
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `Bulletin_${nom}_${prenom}_${selectedSemestre}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (error: any) {
      console.error('Erreur lors du téléchargement:', error);
      setError('Erreur lors du téléchargement du bulletin');
    }
  };

  const handleViewBulletin = (idEleve: number) => {
    setSelectedEleve(idEleve);
    setShowBulletinModal(true);
  };

  const getClasseNom = () => {
    const classe = classes.find(c => c.id_classe.toString() === selectedClasse);
    return classe?.nom_classe || '';
  };

  const getMoyenneClasse = () => {
    if (bulletins.length === 0) return 0;
    const moyennes = bulletins.map(b => b.moyenne_generale).filter(m => m > 0);
    return moyennes.length > 0 ? moyennes.reduce((a, b) => a + b, 0) / moyennes.length : 0;
  };

  const getNombreAdmis = () => {
    return bulletins.filter(b => b.moyenne_generale >= 10).length;
  };

  const getTauxReussite = () => {
    if (bulletins.length === 0) return 0;
    return (getNombreAdmis() / bulletins.length) * 100;
  };

  const getMention = (moyenne: number) => {
    if (moyenne >= 18) return { text: 'EXCELLENT', color: 'text-green-600' };
    if (moyenne >= 16) return { text: 'TRÈS BIEN', color: 'text-blue-600' };
    if (moyenne >= 14) return { text: 'BIEN', color: 'text-indigo-600' };
    if (moyenne >= 12) return { text: 'ASSEZ BIEN', color: 'text-yellow-600' };
    if (moyenne >= 10) return { text: 'PASSABLE', color: 'text-orange-600' };
    return { text: 'INSUFFISANT', color: 'text-red-600' };
  };

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Gestion des Bulletins</h1>
          <p className="text-gray-600">Consultez et téléchargez les bulletins de notes</p>
        </div>
      </div>

      {/* Filtres */}
      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <div className="flex flex-wrap items-center gap-3">
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">Filtres :</span>
          </div>

          <Select
            name="classe"
            value={selectedClasse}
            onChange={(e) => setSelectedClasse(e.target.value)}
            options={[
              { value: "", label: "Sélectionner une classe" },
              ...classes.map((classe) => ({
                value: classe.id_classe.toString(),
                label: classe.nom_classe
              }))
            ]}
            className="min-w-[200px]"
          />

          <Select
            name="semestre"
            value={selectedSemestre}
            onChange={(e) => setSelectedSemestre(e.target.value)}
            options={[
              { value: "S1", label: "Semestre 1" },
              { value: "S2", label: "Semestre 2" }
            ]}
            className="min-w-[150px]"
          />
        </div>
      </div>

      {/* Statistiques */}
      {selectedClasse && bulletins.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <Users className="w-8 h-8 text-blue-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Élèves</p>
                <p className="text-2xl font-bold text-gray-900">{bulletins.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <BookOpen className="w-8 h-8 text-green-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Moyenne Classe</p>
                <p className="text-2xl font-bold text-gray-900">{getMoyenneClasse().toFixed(2)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <Award className="w-8 h-8 text-purple-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Admis</p>
                <p className="text-2xl font-bold text-gray-900">{getNombreAdmis()}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <TrendingUp className="w-8 h-8 text-orange-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Taux Réussite</p>
                <p className="text-2xl font-bold text-gray-900">{getTauxReussite().toFixed(1)}%</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Message d'erreur */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {error}
        </div>
      )}

      {/* Liste des bulletins */}
      {selectedClasse && (
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">
              Bulletins - {getClasseNom()} - {selectedSemestre}
            </h3>
          </div>

          {loading ? (
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Rang
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Élève
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Code Massar
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Moyenne
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Mention
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {bulletins.map((bulletin, index) => {
                    const mention = getMention(bulletin.moyenne_generale);
                    return (
                      <tr key={bulletin.id_eleve} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="text-sm font-medium text-gray-900">
                            {index + 1}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {bulletin.prenom} {bulletin.nom}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="text-sm text-gray-900">{bulletin.code_massar}</span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="text-sm font-medium text-gray-900">
                            {bulletin.moyenne_generale.toFixed(2)}/20
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`text-sm font-medium ${mention.color}`}>
                            {mention.text}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex items-center justify-end space-x-2">
                            <button
                              onClick={() => handleViewBulletin(bulletin.id_eleve)}
                              className="text-blue-600 hover:text-blue-900"
                              title="Voir le bulletin"
                            >
                              <Eye size={16} />
                            </button>
                            <button
                              onClick={() => handleDownloadPDF(bulletin.id_eleve, bulletin.nom, bulletin.prenom)}
                              className="text-green-600 hover:text-green-900"
                              title="Télécharger PDF"
                            >
                              <Download size={16} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>

              {bulletins.length === 0 && !loading && (
                <div className="text-center py-12">
                  <BookOpen className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">Aucun bulletin</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    {selectedClasse 
                      ? "Aucun bulletin trouvé pour cette classe et ce semestre."
                      : "Sélectionnez une classe pour voir les bulletins."}
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Modal de visualisation du bulletin */}
      {showBulletinModal && selectedEleve && (
        <BulletinModal
          idEleve={selectedEleve}
          semestre={selectedSemestre}
          onClose={() => setShowBulletinModal(false)}
        />
      )}
    </div>
  );
};

export default Bulletins;
