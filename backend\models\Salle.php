<?php

class Salle
{
    private $pdo;
    private $table = "Salle";

    public function __construct($db)
    {
        $this->pdo = $db;
    }

    public function getAll()
    {
        $stmt = $this->pdo->query("SELECT * FROM {$this->table}");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getById($id)
    {
        $stmt = $this->pdo->prepare("SELECT * FROM {$this->table} WHERE id_salle = ?");
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function create($data)
    {
        $query = "INSERT INTO {$this->table} (nom_salle, capacite) VALUES (?, ?)";
        $stmt = $this->pdo->prepare($query);
        $stmt->execute([$data['nom_salle'], $data['capacite']]);
        return $this->getById($this->pdo->lastInsertId());
    }

    public function update($id, $data)
    {
        $query = "UPDATE {$this->table} SET nom_salle = ?, capacite = ? WHERE id_salle = ?";
        $stmt = $this->pdo->prepare($query);
        $stmt->execute([$data['nom_salle'], $data['capacite'], $id]);
        return $this->getById($id);
    }

    public function delete($id)
    {
        $stmt = $this->pdo->prepare("DELETE FROM {$this->table} WHERE id_salle = ?");
        return $stmt->execute([$id]);
    }
}