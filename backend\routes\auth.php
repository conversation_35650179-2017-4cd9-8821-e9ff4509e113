<?php

require_once __DIR__ . '/../controllers/UtilisateurController.php';

function handleAuthRoutes($uri, $method, $pdo)
{
    $userController = new UtilisateurController($pdo);

    switch ($uri) {
        case '/register':
            if ($method === 'POST') {
                $userController->register();
                return true;
            }
            break;
        case '/login':
            if ($method === 'POST') {
                $userController->login();
                return true;
            }
            break;
        case '/change-password':
            if ($method === 'POST') {
                $userController->changePassword();
                return true;
            }
            break;
        case (preg_match('/^\/check-password-status\/(\d+)$/', $uri, $matches) ? true : false):
            if ($method === 'GET') {
                $userId = $matches[1];
                $userController->checkPasswordStatus($userId);
                return true;
            }
            break;
    }
    error_log("Route non reconnue dans handleAuthRoutes : $uri");
    return false;
}
