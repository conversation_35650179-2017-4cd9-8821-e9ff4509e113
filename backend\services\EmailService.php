<?php

class EmailService
{
    private $config;

    public function __construct()
    {
        // Charger la configuration email
        $this->config = require __DIR__ . '/../config/email.php';
    }

    /**
     * Envoie un email avec le mot de passe temporaire
     */
    public function sendTemporaryPassword($toEmail, $toName, $temporaryPassword)
    {
        try {
            error_log("📧 Tentative d'envoi email à: $toEmail");
            error_log("📧 Nom: $toName");
            error_log("📧 Mot de passe temporaire: $temporaryPassword");

            $subject = "Votre mot de passe temporaire - ScolaNova";
            $message = $this->getTemporaryPasswordTemplate($toName, $temporaryPassword);

            $result = $this->sendEmail($toEmail, $toName, $subject, $message);
            error_log("📧 Résultat envoi email: " . ($result ? 'SUCCESS' : 'FAILED'));

            return $result;
        } catch (Exception $e) {
            error_log("❌ Erreur envoi email mot de passe temporaire: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * Template HTML pour l'email de mot de passe temporaire
     */
    private function getTemporaryPasswordTemplate($name, $password)
    {
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <title>Mot de passe temporaire - ScolaNova</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background-color: #2563eb; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background-color: #f9f9f9; }
                .password-box { background-color: #e3f2fd; border: 2px solid #2196f3; padding: 15px; margin: 20px 0; text-align: center; }
                .password { font-size: 24px; font-weight: bold; color: #1976d2; letter-spacing: 2px; }
                .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; }
                .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>ScolaNova</h1>
                    <p>Système de Gestion Scolaire</p>
                </div>
                
                <div class='content'>
                    <h2>Bonjour $name,</h2>
                    
                    <p>Votre compte a été créé avec succès sur la plateforme ScolaNova.</p>
                    
                    <p>Voici votre mot de passe temporaire :</p>
                    
                    <div class='password-box'>
                        <div class='password'>$password</div>
                    </div>
                    
                    <div class='warning'>
                        <strong>⚠️ Important :</strong>
                        <ul>
                            <li>Ce mot de passe est temporaire et doit être changé lors de votre première connexion</li>
                            <li>Pour des raisons de sécurité, vous serez obligé(e) de définir un nouveau mot de passe</li>
                            <li>Ne partagez jamais vos identifiants de connexion</li>
                        </ul>
                    </div>
                    
                    <p>Pour vous connecter :</p>
                    <ol>
                        <li>Rendez-vous sur la plateforme ScolaNova</li>
                        <li>Utilisez votre adresse email et le mot de passe temporaire ci-dessus</li>
                        <li>Suivez les instructions pour définir votre nouveau mot de passe</li>
                    </ol>
                    
                    <p>Si vous avez des questions, n'hésitez pas à contacter l'administration.</p>
                    
                    <p>Cordialement,<br>L'équipe ScolaNova</p>
                </div>
                
                <div class='footer'>
                    <p>Cet email a été envoyé automatiquement, merci de ne pas y répondre.</p>
                    <p>© " . date('Y') . " ScolaNova - Tous droits réservés</p>
                </div>
            </div>
        </body>
        </html>";
    }

    /**
     * Fonction générique d'envoi d'email
     */
    private function sendEmail($toEmail, $toName, $subject, $message)
    {
        try {
            error_log("📧 sendEmail - Début pour: $toEmail");

            // Headers pour email HTML
            $headers = [
                'MIME-Version: 1.0',
                'Content-type: text/html; charset=' . $this->config['options']['charset'],
                'From: ' . $this->config['from']['name'] . ' <' . $this->config['from']['email'] . '>',
                'Reply-To: ' . $this->config['from']['email'],
                'X-Mailer: PHP/' . phpversion()
            ];

            error_log("📧 Headers préparés");

            // Tentative d'envoi de l'email
            $success = @mail($toEmail, $subject, $message, implode("\r\n", $headers));

            if ($success) {
                error_log("✅ Email envoyé avec succès à: $toEmail");
                return true;
            } else {
                error_log("❌ Échec envoi email à: $toEmail");

                // Fallback: sauvegarder l'email dans un fichier
                $this->saveEmailToFile($toEmail, $toName, $subject, $message);

                // Retourner true quand même pour ne pas faire échouer le processus
                return true;
            }
        } catch (Exception $e) {
            error_log("💥 Exception dans sendEmail: " . $e->getMessage());

            // Fallback: sauvegarder l'email dans un fichier
            $this->saveEmailToFile($toEmail, $toName, $subject, $message);

            // Retourner true pour ne pas faire échouer le processus
            return true;
        }
    }

    /**
     * Sauvegarde l'email dans un fichier si l'envoi échoue
     */
    private function saveEmailToFile($toEmail, $toName, $subject, $message)
    {
        try {
            $emailsDir = __DIR__ . '/../emails_backup';
            if (!is_dir($emailsDir)) {
                mkdir($emailsDir, 0755, true);
            }

            $filename = $emailsDir . '/email_' . date('Y-m-d_H-i-s') . '_' . md5($toEmail) . '.html';

            $emailContent = "<!DOCTYPE html>\n";
            $emailContent .= "<html><head><meta charset='UTF-8'><title>$subject</title></head><body>\n";
            $emailContent .= "<div style='background: #f0f0f0; padding: 20px; margin-bottom: 20px;'>\n";
            $emailContent .= "<h2>📧 Email non envoyé - Sauvegarde</h2>\n";
            $emailContent .= "<p><strong>Destinataire:</strong> $toName &lt;$toEmail&gt;</p>\n";
            $emailContent .= "<p><strong>Sujet:</strong> $subject</p>\n";
            $emailContent .= "<p><strong>Date:</strong> " . date('Y-m-d H:i:s') . "</p>\n";
            $emailContent .= "</div>\n";
            $emailContent .= $message;
            $emailContent .= "\n</body></html>";

            file_put_contents($filename, $emailContent);

            error_log("📁 Email sauvegardé dans: $filename");
            error_log("💡 Vous pouvez consulter le contenu de l'email et l'envoyer manuellement");
        } catch (Exception $e) {
            error_log("❌ Erreur sauvegarde email: " . $e->getMessage());
        }
    }

    /**
     * Envoie un email de confirmation de changement de mot de passe
     */
    public function sendPasswordChangeConfirmation($toEmail, $toName)
    {
        try {
            $subject = "Confirmation de changement de mot de passe - ScolaNova";
            $message = $this->getPasswordChangeConfirmationTemplate($toName);

            return $this->sendEmail($toEmail, $toName, $subject, $message);
        } catch (Exception $e) {
            error_log("Erreur envoi email confirmation changement mot de passe: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Template pour confirmation de changement de mot de passe
     */
    private function getPasswordChangeConfirmationTemplate($name)
    {
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <title>Mot de passe modifié - ScolaNova</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background-color: #16a085; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background-color: #f9f9f9; }
                .success { background-color: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 20px 0; }
                .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>ScolaNova</h1>
                    <p>Système de Gestion Scolaire</p>
                </div>
                
                <div class='content'>
                    <h2>Bonjour $name,</h2>
                    
                    <div class='success'>
                        <strong>✅ Succès :</strong> Votre mot de passe a été modifié avec succès.
                    </div>
                    
                    <p>Votre compte est maintenant entièrement activé et vous pouvez utiliser la plateforme ScolaNova normalement.</p>
                    
                    <p>Si vous n'êtes pas à l'origine de cette modification, contactez immédiatement l'administration.</p>
                    
                    <p>Cordialement,<br>L'équipe ScolaNova</p>
                </div>
                
                <div class='footer'>
                    <p>Cet email a été envoyé automatiquement, merci de ne pas y répondre.</p>
                    <p>© " . date('Y') . " ScolaNova - Tous droits réservés</p>
                </div>
            </div>
        </body>
        </html>";
    }
}
