<?php

require_once __DIR__ . '/../controllers/ActiviteController.php';
require_once __DIR__ . '/../controllers/InscriptionActiviteController.php';

function handleActiviteRoutes($uri, $method, $pdo) {
    $activiteController = new ActiviteController($pdo);
    $inscriptionController = new InscriptionActiviteController($pdo);

    // GET /activites - Récupérer toutes les activités
    if ($uri === '/activites' && $method === 'GET') {
        $activiteController->getAll();
        return true;
    }

    // GET /activites/{id} - Récupérer une activité par ID
    if (preg_match('/^\/activites\/(\d+)$/', $uri, $matches) && $method === 'GET') {
        $id = (int)$matches[1];
        $activiteController->getById($id);
        return true;
    }

    // POST /activites - C<PERSON>er une nouvelle activité
    if ($uri === '/activites' && $method === 'POST') {
        $activiteController->create();
        return true;
    }

    // PUT /activites/{id} - Mettre à jour une activité
    if (preg_match('/^\/activites\/(\d+)$/', $uri, $matches) && $method === 'PUT') {
        $id = (int)$matches[1];
        $activiteController->update($id);
        return true;
    }

    // DELETE /activites/{id} - Supprimer une activité
    if (preg_match('/^\/activites\/(\d+)$/', $uri, $matches) && $method === 'DELETE') {
        $id = (int)$matches[1];
        $activiteController->delete($id);
        return true;
    }

    // GET /activites/{id}/eleves - Récupérer les élèves inscrits à une activité
    if (preg_match('/^\/activites\/(\d+)\/eleves$/', $uri, $matches) && $method === 'GET') {
        $id = (int)$matches[1];
        $inscriptionController->getElevesInscrits($id);
        return true;
    }

    // GET /activites/{id}/eleves-disponibles - Récupérer les élèves disponibles pour inscription
    if (preg_match('/^\/activites\/(\d+)\/eleves-disponibles$/', $uri, $matches) && $method === 'GET') {
        $id = (int)$matches[1];
        $inscriptionController->getElevesDisponibles($id);
        return true;
    }

    // POST /activites/inscriptions - Inscrire un élève à une activité
    if ($uri === '/activites/inscriptions' && $method === 'POST') {
        $inscriptionController->inscrireEleve();
        return true;
    }

    // DELETE /activites/{id_activite}/eleves/{id_eleve} - Désinscrire un élève
    if (preg_match('/^\/activites\/(\d+)\/eleves\/(\d+)$/', $uri, $matches) && $method === 'DELETE') {
        $id_activite = (int)$matches[1];
        $id_eleve = (int)$matches[2];
        $inscriptionController->desinscrireEleve($id_activite, $id_eleve);
        return true;
    }

    // GET /eleves/{id}/activites - Récupérer les activités d'un élève
    if (preg_match('/^\/eleves\/(\d+)\/activites$/', $uri, $matches) && $method === 'GET') {
        $id = (int)$matches[1];
        $inscriptionController->getActivitesEleve($id);
        return true;
    }

    // GET /activites/statistiques - Récupérer les statistiques des activités
    if ($uri === '/activites/statistiques' && $method === 'GET') {
        $inscriptionController->getStatistiques();
        return true;
    }

    return false;
}
?>
