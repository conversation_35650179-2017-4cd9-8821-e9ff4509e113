<?php

require_once __DIR__ . '/../models/Inscription.php';
require_once __DIR__ . '/../models/Eleve.php';
require_once __DIR__ . '/../models/AnneeScolaire.php';
require_once __DIR__ . '/../models/Classe.php';

class InscriptionController
{
    private $inscription;
    private $eleve;
    private $anneeScolaire;
    private $classe;

    public function __construct($db)
    {
        $this->inscription = new Inscription($db);
        $this->eleve = new Eleve($db);
        $this->anneeScolaire = new AnneeScolaire($db);
        $this->classe = new Classe($db);
    }

    public function create()
    {
        try {
            $data = json_decode(file_get_contents("php://input"), true);

            if (empty($data['id_eleve']) || empty($data['id_classe']) || empty($data['id_annee_scolaire'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'ID Élève, ID Classe et ID Année Scolaire sont requis.']);
                return;
            }

            $id_eleve = $data['id_eleve'];
            $id_classe = $data['id_classe'];
            $id_annee_scolaire = $data['id_annee_scolaire'];

            // Vérifier si l'élève, la classe et l'année scolaire existent
            if (!$this->eleve->getById($id_eleve)) {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Élève non trouvé.']);
                return;
            }
            if (!$this->classe->getById($id_classe)) {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Classe non trouvée.']);
                return;
            }
            if (!$this->anneeScolaire->getById($id_annee_scolaire)) {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Année scolaire non trouvée.']);
                return;
            }

            // Vérifier si l'inscription existe déjà pour cette année scolaire
            if ($this->inscription->getInscriptionByEleve($id_eleve)) {
                http_response_code(409);
                echo json_encode(['success' => false, 'message' => 'L\'élève est déjà inscrit pour cette année scolaire.']);
                return;
            }

            $result = $this->inscription->create($id_eleve, $id_annee_scolaire, $id_classe);

            if ($result) {
                echo json_encode(['success' => true, 'message' => 'Inscription créée avec succès.']);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Erreur lors de la création de l\'inscription.']);
            }
        } catch (Exception $e) {
            error_log("Erreur dans InscriptionController::create: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    public function update($id)
    {
        try {
            $data = json_decode(file_get_contents("php://input"), true);

            if (empty($data['id_eleve']) || empty($data['id_annee_scolaire']) || empty($data['old_id_classe']) || empty($data['new_id_classe'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'ID Élève, ID Année Scolaire, ancienne ID Classe et nouvelle ID Classe sont requis.']);
                return;
            }

            $id_eleve = $data['id_eleve'];
            $id_annee_scolaire = $data['id_annee_scolaire'];
            $old_id_classe = $data['old_id_classe'];
            $new_id_classe = $data['new_id_classe'];

            // Vérifier si la nouvelle classe existe
            if (!$this->classe->getById($new_id_classe)) {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Nouvelle classe non trouvée.']);
                return;
            }

            $result = $this->inscription->update($id_eleve, $id_annee_scolaire, $old_id_classe, $new_id_classe);

            if ($result) {
                echo json_encode(['success' => true, 'message' => 'Inscription mise à jour avec succès.']);
            } else {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Inscription non trouvée ou aucune modification.']);
            }
        } catch (Exception $e) {
            error_log("Erreur dans InscriptionController::update: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    public function updateByEleve($id_eleve)
    {
        try {
            $data = json_decode(file_get_contents("php://input"), true);

            if (empty($data['id_eleve']) || empty($data['id_annee_scolaire']) || empty($data['old_id_classe']) || empty($data['new_id_classe'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'ID Élève, ID Année Scolaire, ancienne ID Classe et nouvelle ID Classe sont requis.']);
                return;
            }

            $id_eleve = $data['id_eleve'];
            $id_annee_scolaire = $data['id_annee_scolaire'];
            $old_id_classe = $data['old_id_classe'];
            $new_id_classe = $data['new_id_classe'];

            // Vérifier si la nouvelle classe existe
            if (!$this->classe->getById($new_id_classe)) {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Nouvelle classe non trouvée.']);
                return;
            }

            $result = $this->inscription->update($id_eleve, $id_annee_scolaire, $old_id_classe, $new_id_classe);

            if ($result) {
                echo json_encode(['success' => true, 'message' => 'Inscription mise à jour avec succès.']);
            } else {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Inscription non trouvée ou aucune modification.']);
            }
        } catch (Exception $e) {
            error_log("Erreur dans InscriptionController::updateByEleve: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    public function getInscriptionByEleve($id_eleve)
    {
        try {
            $result = $this->inscription->getInscriptionByEleve($id_eleve);
            if ($result) {
                echo json_encode(['success' => true, 'data' => $result]);
            } else {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Inscription non trouvée pour cet élève et cette année scolaire.']);
            }
        } catch (Exception $e) {
            error_log("Erreur dans InscriptionController::getInscriptionByEleve: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

}