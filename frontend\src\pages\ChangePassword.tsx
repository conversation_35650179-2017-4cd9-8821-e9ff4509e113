import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import Input from '../components/Input';
import Button from '../components/Button';
import { changePassword } from '../services/api';
import { Lock, Eye, EyeOff, AlertCircle, CheckCircle } from 'lucide-react';

const ChangePassword: React.FC = () => {
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState<'success' | 'error'>('error');

  const { user, setUser } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    // Rediriger si l'utilisateur n'est pas connecté
    if (!user) {
      navigate('/login');
      return;
    }
  }, [user, navigate]);

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    if (!currentPassword.trim()) {
      newErrors.currentPassword = 'Le mot de passe actuel est requis.';
    }

    if (!newPassword.trim()) {
      newErrors.newPassword = 'Le nouveau mot de passe est requis.';
    } else if (newPassword.length < 6) {
      newErrors.newPassword = 'Le mot de passe doit contenir au moins 6 caractères.';
    } else if (newPassword === currentPassword) {
      newErrors.newPassword = 'Le nouveau mot de passe doit être différent de l\'actuel.';
    }

    if (!confirmPassword.trim()) {
      newErrors.confirmPassword = 'La confirmation du mot de passe est requise.';
    } else if (newPassword !== confirmPassword) {
      newErrors.confirmPassword = 'Les mots de passe ne correspondent pas.';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    if (!user) return;

    setIsSubmitting(true);
    setMessage('');

    try {
      const response = await changePassword({
        user_id: user.id_utilisateur,
        current_password: currentPassword,
        new_password: newPassword
      });

      if (response.data.success) {
        setMessageType('success');
        setMessage('Mot de passe modifié avec succès ! Redirection en cours...');

        // Mettre à jour l'utilisateur pour indiquer qu'il n'a plus besoin de changer son mot de passe
        setUser({ ...user, est_valide: true });

        // Rediriger vers le dashboard approprié selon le rôle après 2 secondes
        setTimeout(() => {
          const role = user.role;
          if (role === "admin") {
            navigate("/admin/dashboard");
          } else if (role === "enseignant") {
            navigate("/enseignant/dashboard");
          } else if (role === "eleve") {
            navigate("/eleve/dashboard");
          } else if (role === "parent") {
            navigate("/parent/dashboard");
          } else {
            navigate("/");
          }
        }, 2000);
      } else {
        setMessageType('error');
        setMessage(response.data.message || 'Erreur lors du changement de mot de passe');
      }
    } catch (error: any) {
      console.error('Erreur changement mot de passe:', error);
      setMessageType('error');

      if (error.response?.data?.message) {
        setMessage(error.response.data.message);
      } else if (error.response?.status) {
        setMessage(`Erreur ${error.response.status}: ${error.response.statusText || 'Erreur serveur'}`);
      } else if (error.message) {
        setMessage(`Erreur réseau: ${error.message}`);
      } else {
        setMessage('Erreur lors du changement de mot de passe');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const togglePasswordVisibility = (field: 'current' | 'new' | 'confirm') => {
    switch (field) {
      case 'current':
        setShowCurrentPassword(!showCurrentPassword);
        break;
      case 'new':
        setShowNewPassword(!showNewPassword);
        break;
      case 'confirm':
        setShowConfirmPassword(!showConfirmPassword);
        break;
    }
  };

  if (!user) {
    return null; // ou un spinner de chargement
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <Lock className="h-12 w-12 text-blue-600" />
        </div>
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Changement de mot de passe obligatoire
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Pour des raisons de sécurité, vous devez définir un nouveau mot de passe
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
            <div className="flex">
              <AlertCircle className="h-5 w-5 text-yellow-400" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800">
                  Première connexion
                </h3>
                <p className="mt-1 text-sm text-yellow-700">
                  Vous devez changer votre mot de passe temporaire pour continuer.
                </p>
              </div>
            </div>
          </div>

          {message && (
            <div className={`mb-4 p-4 rounded-md ${
              messageType === 'success' 
                ? 'bg-green-50 border border-green-200' 
                : 'bg-red-50 border border-red-200'
            }`}>
              <div className="flex">
                {messageType === 'success' ? (
                  <CheckCircle className="h-5 w-5 text-green-400" />
                ) : (
                  <AlertCircle className="h-5 w-5 text-red-400" />
                )}
                <p className={`ml-3 text-sm ${
                  messageType === 'success' ? 'text-green-700' : 'text-red-700'
                }`}>
                  {message}
                </p>
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="relative">
              <Input
                name="currentPassword"
                label="Mot de passe actuel"
                type={showCurrentPassword ? 'text' : 'password'}
                required
                value={currentPassword}
                onChange={(e) => setCurrentPassword(e.target.value)}
                error={errors.currentPassword}
                placeholder="Entrez votre mot de passe temporaire"
              />
              <button
                type="button"
                className="absolute right-3 top-8 text-gray-400 hover:text-gray-600"
                onClick={() => togglePasswordVisibility('current')}
              >
                {showCurrentPassword ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
            </div>

            <div className="relative">
              <Input
                name="newPassword"
                label="Nouveau mot de passe"
                type={showNewPassword ? 'text' : 'password'}
                required
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                error={errors.newPassword}
                placeholder="Minimum 6 caractères"
              />
              <button
                type="button"
                className="absolute right-3 top-8 text-gray-400 hover:text-gray-600"
                onClick={() => togglePasswordVisibility('new')}
              >
                {showNewPassword ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
            </div>

            <div className="relative">
              <Input
                name="confirmPassword"
                label="Confirmer le nouveau mot de passe"
                type={showConfirmPassword ? 'text' : 'password'}
                required
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                error={errors.confirmPassword}
                placeholder="Répétez le nouveau mot de passe"
              />
              <button
                type="button"
                className="absolute right-3 top-8 text-gray-400 hover:text-gray-600"
                onClick={() => togglePasswordVisibility('confirm')}
              >
                {showConfirmPassword ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
            </div>

            <div>
              <Button
                type="submit"
                variant="primary"
                disabled={isSubmitting}
                className="w-full"
              >
                {isSubmitting ? 'Modification en cours...' : 'Changer le mot de passe'}
              </Button>
            </div>
          </form>

          <div className="mt-6">
            <div className="text-sm text-gray-600">
              <h4 className="font-medium mb-2">Conseils pour un mot de passe sécurisé :</h4>
              <ul className="list-disc list-inside space-y-1">
                <li>Au moins 6 caractères</li>
                <li>Mélange de lettres majuscules et minuscules</li>
                <li>Inclure des chiffres</li>
                <li>Utiliser des caractères spéciaux</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChangePassword;
