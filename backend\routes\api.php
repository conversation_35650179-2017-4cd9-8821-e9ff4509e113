<?php

require_once __DIR__ . '/../config/db.php';
require_once __DIR__ . '/auth.php';
require_once __DIR__ . '/eleve.php';
require_once __DIR__ . '/parent.php';
require_once __DIR__ . '/enseignant.php';
require_once __DIR__ . '/diplome.php';
require_once __DIR__ . '/contrat.php';
require_once __DIR__ . '/ancienne_ecole.php';
require_once __DIR__ . '/niveau.php';
require_once __DIR__ . '/annee_scolaire.php';
require_once __DIR__ . '/classe.php';
require_once __DIR__ . '/inscription.php';
require_once __DIR__ . '/matiere.php';

$db = new Database();
$pdo = $db->getConnection();

$uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH); //L'URL demandée par le client (ex. /login, /eleves/1).
$uri = str_replace('/ScolaNova/backend/public/index.php', '', $uri);
$method = $_SERVER['REQUEST_METHOD'];

$handled = false;


// Routes d'authentification
$handled = handleAuthRoutes($uri, $method, $pdo);

// Routes élèves
if (!$handled) {
    $handled = handleEleveRoutes($uri, $method, $pdo);
}
// Routes parents
if (!$handled) {
    $handled = handleParentRoutes($uri, $method, $pdo);
}
// Routes anciennes écoles
if (!$handled) {
    $handled = handleAncienneEcoleRoutes($uri, $method, $pdo);
}
// Routes niveaux
if (!$handled) {
    $handled = handleNiveauRoutes($uri, $method, $pdo);
}
// Routes années scolaires
if (!$handled) {
    $handled = handleAnneeScolaireRoutes($uri, $method, $pdo);
}
// Routes classes
if (!$handled) {
    $handled = handleClasseRoutes($uri, $method, $pdo);
}
// Routes inscriptions
if (!$handled) {
    $handled = handleInscriptionRoutes($uri, $method, $pdo);
}
// Routes diplomes (avant enseignants pour capturer /enseignants/{id}/diplomes)
if (!$handled) {
    $handled = handleDiplomeRoutes($uri, $method, $pdo);
}
// Routes contrats (avant enseignants pour capturer /enseignants/{id}/contrats/active)
if (!$handled) {
    $handled = handleContratRoutes($uri, $method, $pdo);
}
// Routes enseignants (après les routes spécifiques)
if (!$handled) {
    $handled = handleEnseignantRoutes($uri, $method, $pdo);
}

// Routes matières
if (!$handled) {
    $handled = handleMatiereRoutes($uri, $method, $pdo);
}

// Routes dashboard
if (!$handled) {
    require_once __DIR__ . '/dashboard.php';
    $handled = handleDashboardRoutes($uri, $method, $pdo);
}

// Routes salles
if (!$handled) {
    require_once __DIR__ . '/salle.php';
    $handled = handleSalleRoutes($uri, $method, $pdo);
}

// Routes utilisateurs
if (!$handled) {
    require_once __DIR__ . '/utilisateur.php';
    $handled = handleUtilisateurRoutes($uri, $method, $pdo);
}

// Routes cours
if (!$handled) {
    require_once __DIR__ . '/cours.php';
    $handled = handleCoursRoutes($uri, $method, $pdo);
}

// Routes unités
if (!$handled) {
    require_once __DIR__ . '/unite.php';
    $handled = handleUniteRoutes($uri, $method, $pdo);
}

// Routes paiements
if (!$handled) {
    require_once __DIR__ . '/paiement.php';
    $handled = handlePaiementRoutes($uri, $method, $pdo);
}

// Routes examens
if (!$handled) {
    require_once __DIR__ . '/examen.php';
    $handled = handleExamenRoutes($uri, $method, $pdo);
}

// Routes notes
if (!$handled) {
    require_once __DIR__ . '/examen.php';
    $handled = handleNoteRoutes($uri, $method, $pdo);
}


// Route non reconnue
if (!$handled) {
    http_response_code(404);
    echo json_encode(["error" => "Route non trouvée"]);
    error_log("Route non trouvée : $uri");
}
