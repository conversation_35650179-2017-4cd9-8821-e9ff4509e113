<?php

class AnneeScolaire
{
    private $pdo;
    private $table = "Annee_Scolaire";

    public function __construct($db)
    {
        $this->pdo = $db;
    }

    public function create($data)
    {
        try {
            // Si cette année est marquée comme active, désactiver toutes les autres
            if (isset($data['est_active']) && $data['est_active']) {
                $this->deactivateAll();
            }

            $query = "INSERT INTO " . $this->table . " (libelle, date_debut, date_fin, est_active)
                VALUES (:libelle, :date_debut, :date_fin, :est_active)";
            $stmt = $this->pdo->prepare($query);

            $stmt->bindParam(':libelle', $data['libelle'], PDO::PARAM_STR);
            $stmt->bindParam(':date_debut', $data['date_debut'], PDO::PARAM_STR);
            $stmt->bindParam(':date_fin', $data['date_fin'], PDO::PARAM_STR);
            $stmt->bindParam(':est_active', $data['est_active'], PDO::PARAM_BOOL);

            if ($stmt->execute()) {
                return $this->pdo->lastInsertId();
            } else {
                error_log("Erreur SQL AnneeScolaire : " . implode(", ", $stmt->errorInfo()));
                return false;
            }
        } catch (PDOException $e) {
            error_log("Erreur PDO dans AnneeScolaire::create : " . $e->getMessage());
            error_log("Données reçues : " . json_encode($data));
            return false;
        }
    }

    public function getAll()
    {
        try {
            $query="SELECT * FROM " . $this->table . " ORDER BY date_debut DESC";
            $stmt = $this->pdo->prepare($query);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur dans getAll: " . $e->getMessage());
            return false;
        }
    }

    public function getById($id)
    {
        try {
            $query = "SELECT * FROM " . $this->table . " WHERE id_annee_scolaire = :id";
            $stmt = $this->pdo->prepare($query);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return $result ? $result : false;
        } catch (PDOException $e) {
            error_log("Erreur dans getById: " . $e->getMessage());
            return false;
        }
    }

    public function getActive()
    {
        try {
            $query = "SELECT * FROM " . $this->table . " WHERE est_active = 1";
            $stmt = $this->pdo->prepare($query);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return $result ? $result : false;
        } catch (PDOException $e) {
            error_log("Erreur dans getActive: " . $e->getMessage());
            return false;
        }
    }

    public function update($id, $data)
    {
        try {
            // Si cette année est marquée comme active, désactiver toutes les autres
            if (isset($data['est_active']) && $data['est_active']) {
                $this->deactivateAll();
            }

            $query = "UPDATE " . $this->table . " SET 
                     libelle = :libelle,
                     date_debut = :date_debut,
                     date_fin = :date_fin,
                     est_active = :est_active
                     WHERE id_annee_scolaire = :id";
            $stmt = $this->pdo->prepare($query);

            $stmt->bindParam(':libelle', $data['libelle'], PDO::PARAM_STR);
            $stmt->bindParam(':date_debut', $data['date_debut'], PDO::PARAM_STR);
            $stmt->bindParam(':date_fin', $data['date_fin'], PDO::PARAM_STR);
            $stmt->bindParam(':est_active', $data['est_active'], PDO::PARAM_BOOL);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);

            if ($stmt->execute()) {
                return $stmt->rowCount() > 0;
            } else {
                error_log("Erreur SQL Update AnneeScolaire : " . implode(", ", $stmt->errorInfo()));
                return false;
            }
        } catch (PDOException $e) {
            error_log("Erreur PDO dans AnneeScolaire::update : " . $e->getMessage());
            return false;
        }
    }

    public function delete($id)
    {
        try {
            // Vérifier si cette année scolaire est utilisée dans d'autres tables
            $checkTables = [
                'Inscription' => 'id_annee_scolaire',
                'Paiement' => 'id_annee_scolaire',
                'Bulletin' => 'id_annee_scolaire',
                'Activite' => 'id_annee_scolaire',
                'Beneficier_Transport' => 'id_annee_scolaire'
            ];

            foreach ($checkTables as $table => $column) {
                $query = "SELECT COUNT(*) FROM $table WHERE $column = :id";
                $checkStmt = $this->pdo->prepare($query);
                $checkStmt->bindParam(':id', $id, PDO::PARAM_INT);
                $checkStmt->execute();
                $count = $checkStmt->fetchColumn();

                if ($count > 0) {
                    error_log("Impossible de supprimer l'année scolaire $id : des enregistrements existent dans $table");
                    return false;
                }
            }

            $query = "DELETE FROM " . $this->table . " WHERE id_annee_scolaire = :id";
            $stmt = $this->pdo->prepare($query);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            
            if ($stmt->execute()) {
                return $stmt->rowCount() > 0;
            } else {
                error_log("Erreur SQL Delete AnneeScolaire : " . implode(", ", $stmt->errorInfo()));
                return false;
            }
        } catch (PDOException $e) {
            error_log("Erreur PDO dans AnneeScolaire::delete : " . $e->getMessage());
            return false;
        }
    }

    public function exists($libelle, $excludeId = null)
    {
        try {
            $sql = "SELECT COUNT(*) FROM " . $this->table . " WHERE libelle = :libelle";
            if ($excludeId) {
                $sql .= " AND id_annee_scolaire != :excludeId";
            }
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':libelle', $libelle, PDO::PARAM_STR);
            if ($excludeId) {
                $stmt->bindParam(':excludeId', $excludeId, PDO::PARAM_INT);
            }
            
            $stmt->execute();
            return $stmt->fetchColumn() > 0;
        } catch (PDOException $e) {
            error_log("Erreur dans exists: " . $e->getMessage());
            return false;
        }
    }

    public function toggleActive($id)
    {
        try {
            // Récupérer l'état actuel
            $current = $this->getById($id);
            if (!$current) {
                return false;
            }

            $newStatus = !$current['est_active'];

            // Si on active cette année, désactiver toutes les autres
            if ($newStatus) {
                $this->deactivateAll();
            }

            $query = "UPDATE " . $this->table . " SET est_active = :status WHERE id_annee_scolaire = :id";
            $stmt = $this->pdo->prepare($query);
            $stmt->bindParam(':status', $newStatus, PDO::PARAM_BOOL);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);

            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Erreur dans toggleActive: " . $e->getMessage());
            return false;
        }
    }

    private function deactivateAll()
    {
        try {
            $stmt = $this->pdo->prepare("UPDATE " . $this->table . " SET est_active = 0");
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Erreur dans deactivateAll: " . $e->getMessage());
            return false;
        }
    }

    public function validateDates($dateDebut, $dateFin, $excludeId = null)
    {
        try {
            // Vérifier que la date de fin est après la date de début
            if (strtotime($dateFin) <= strtotime($dateDebut)) {
                return ['valid' => false, 'message' => 'La date de fin doit être après la date de début'];
            }

            // Vérifier les chevauchements avec d'autres années scolaires
            $sql = "SELECT COUNT(*) FROM " . $this->table . " 
                    WHERE (
                        (date_debut <= :date_debut AND date_fin >= :date_debut) OR
                        (date_debut <= :date_fin AND date_fin >= :date_fin) OR
                        (date_debut >= :date_debut AND date_fin <= :date_fin)
                    )";
            
            if ($excludeId) {
                $sql .= " AND id_annee_scolaire != :excludeId";
            }

            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':date_debut', $dateDebut, PDO::PARAM_STR);
            $stmt->bindParam(':date_fin', $dateFin, PDO::PARAM_STR);
            if ($excludeId) {
                $stmt->bindParam(':excludeId', $excludeId, PDO::PARAM_INT);
            }

            $stmt->execute();
            $count = $stmt->fetchColumn();

            if ($count > 0) {
                return ['valid' => false, 'message' => 'Les dates se chevauchent avec une autre année scolaire'];
            }

            return ['valid' => true, 'message' => 'Dates valides'];
        } catch (PDOException $e) {
            error_log("Erreur dans validateDates: " . $e->getMessage());
            return ['valid' => false, 'message' => 'Erreur lors de la validation'];
        }
    }
}
