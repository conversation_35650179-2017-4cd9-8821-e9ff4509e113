<?php

require_once __DIR__ . '/../models/Enseignant.php';

class EnseignantController
{
    private $enseignant;

    public function __construct($db)
    {
        $this->enseignant = new Enseignant($db);
    }

    public function getEnseignants()
    {
        try {
            $enseignants = $this->enseignant->getAllWithRelations();
            echo json_encode(['success' => true, 'data' => $enseignants]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur lors de la récupération des enseignants']);
        }
    }

    public function getEnseignant($id)
    {
        try {
            $enseignant = $this->enseignant->getById($id);
            if ($enseignant) {
                echo json_encode(['success' => true, 'data' => $enseignant]);
            } else {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Enseignant non trouvé']);
            }
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur lors de la récupération de l\'enseignant']);
        }
    }

    public function addEnseignant()
    {
        try {
            $data = json_decode(file_get_contents("php://input"), true);
            error_log("Données reçues pour addEnseignant : " . json_encode($data));

            if (!$data) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Aucune donnée reçue']);
                return;
            }

            $id_enseignant = $this->enseignant->create($data);
            if ($id_enseignant) {
                http_response_code(201);
                echo json_encode([
                    'success' => true,
                    'id_enseignant' => $id_enseignant,
                    'message' => 'Enseignant créé avec succès'
                ]);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Erreur lors de la création de l\'enseignant']);
            }
        } catch (Exception $e) {
            error_log("Exception dans addEnseignant: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }
    public function updateEnseignant($id)
    {
        try {
            $data = json_decode(file_get_contents("php://input"), true);

            if (!$data) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Aucune donnée reçue']);
                return;
            }

            $result = $this->enseignant->update($id, $data);
            // La méthode update retourne true si la requête s'est bien exécutée
            // même si aucune ligne n'a été modifiée (données identiques)
            echo json_encode(['success' => true, 'message' => 'Enseignant mis à jour avec succès']);
        } catch (Exception $e) {
            error_log("Exception dans updateEnseignant: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    public function deleteEnseignant($id)
    {
        try {
            if ($this->enseignant->delete($id)) {
                echo json_encode(['success' => true, 'message' => 'Enseignant supprimé avec succès']);
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Erreur lors de la suppression de l\'enseignant']);
            }
        } catch (Exception $e) {
            error_log("Exception dans deleteEnseignant: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    public function getActiveContratByEnseignant($id_enseignant)
    {
        try {

            $stmt = $this->pdo->prepare("SELECT * FROM Contrat WHERE id_enseignant = :id_enseignant
                        AND statut = 'actif' ORDER BY date_debut DESC LIMIT 1 ");
            $stmt->bindParam(':id_enseignant', $id_enseignant, PDO::PARAM_INT);
            $stmt->execute();
            $contrat = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($contrat) {
                error_log("Contrat actif trouvé: " . json_encode($contrat));
                echo json_encode([
                    'success' => true,
                    'data' => $contrat,
                    'message' => 'Contrat actif récupéré avec succès'
                ]);
            } else {
                error_log("Aucun contrat actif trouvé pour l'enseignant: $id_enseignant");
                echo json_encode([
                    'success' => false,
                    'data' => null,
                    'message' => 'Aucun contrat actif trouvé pour cet enseignant'
                ]);
            }
        } catch (Exception $e) {
            error_log("Exception dans getActiveContratByEnseignant: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'data' => null,
                'message' => 'Erreur serveur: ' . $e->getMessage()
            ]);
        }
    }

    /** Récupérer les élèves d'un enseignant  */
    public function getElevesEnseignant($id_enseignant)
    {
        try {
            $eleves = $this->enseignant->getElevesEnseignant($id_enseignant);
            echo json_encode([
                'success' => true,
                'data' => $eleves,
                'message' => 'Élèves de l\'enseignant récupérés avec succès'
            ]);
        } catch (Exception $e) {
            error_log("Exception dans getElevesEnseignant: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    /** Récupérer les classes d'un enseignant (les classes où il enseigne) */
    public function getClassesEnseignant($id_enseignant)
    {
        try {
            $classes = $this->enseignant->getClassesEnseignant($id_enseignant);
            echo json_encode([
                'success' => true,
                'data' => $classes,
                'message' => 'Classes de l\'enseignant récupérées avec succès'
            ]);
        } catch (Exception $e) {
            error_log("Exception dans getClassesEnseignant: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    /** Récupérer les cours d'aujourd'hui d'un enseignant */
    public function getCoursAujourdhui($id_enseignant)
    {
        try {
            $cours = $this->enseignant->getCoursAujourdhui($id_enseignant);
            echo json_encode([
                'success' => true,
                'data' => $cours,
                'message' => 'Cours d\'aujourd\'hui récupérés avec succès'
            ]);
        } catch (Exception $e) {
            error_log("Exception dans getCoursAujourdhui: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }
}
