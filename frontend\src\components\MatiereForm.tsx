import React, { useState, useEffect } from "react";
import Input from "./Input";
import { addMatiere, updateMatiere } from "../services/api";
import type { Matiere } from "../types";

interface MatiereFormProps {
  onSuccess?: () => void;
  initialMatiere?: Matiere | null;
}

const MatiereForm: React.FC<MatiereFormProps> = ({ onSuccess, initialMatiere }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState<boolean | null>(null);
  const [submitMessage, setSubmitMessage] = useState("");
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const [matiere, setMatiere] = useState<Matiere>({
    id_matiere: 0,
    nom_matiere_fr: "",
    nom_matiere_ar: "",
    description: "",
  });

  // Charger les données de la matière à modifier
  useEffect(() => {
    if (initialMatiere) {
      setMatiere({
        id_matiere: initialMatiere.id_matiere,
        nom_matiere_fr: initialMatiere.nom_matiere_fr || "",
        nom_matiere_ar: initialMatiere.nom_matiere_ar || "",
        description: initialMatiere.description || "",
      });
    }
  }, [initialMatiere]);

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    if (!matiere.nom_matiere_fr.trim()) {
      newErrors.nom_matiere_fr = "Le nom de la matière en français est requis.";
    }

    if (!matiere.nom_matiere_ar.trim()) {
      newErrors.nom_matiere_ar = "Le nom de la matière en arabe est requis.";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setErrors((prev) => ({ ...prev, [name]: "" }));
    
    setMatiere((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const resetForm = () => {
    setMatiere({
      id_matiere: 0,
      nom_matiere_fr: "",
      nom_matiere_ar: "",
      description: "",
    });
    setErrors({});
    setSubmitMessage("");
    setSubmitSuccess(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      setSubmitMessage("Veuillez corriger les erreurs dans le formulaire.");
      setSubmitSuccess(false);
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    try {
      const isEditing = initialMatiere && initialMatiere.id_matiere > 0;
      
      const matiereData = {
        nom_matiere_fr: matiere.nom_matiere_fr.trim(),
        nom_matiere_ar: matiere.nom_matiere_ar.trim(),
        description: matiere.description?.trim() || null,
      };

      console.log("Données matière à envoyer:", matiereData);

      let response;
      if (isEditing) {
        response = await updateMatiere(initialMatiere.id_matiere, matiereData);
        console.log("Matière mise à jour:", response);
      } else {
        response = await addMatiere(matiereData);
        console.log("Matière créée:", response);
      }

      if (response.data.success) {
        setSubmitSuccess(true);
        setSubmitMessage(isEditing ? "Matière modifiée avec succès !" : "Matière ajoutée avec succès !");
        
        if (!isEditing) {
          resetForm();
        }
        
        if (onSuccess) onSuccess();
      } else {
        throw new Error(response.data.message || "Erreur lors de l'opération");
      }
    } catch (error: any) {
      console.error("Erreur lors de la soumission:", error);
      setSubmitSuccess(false);

      let errorMessage = "Erreur lors de la requête";
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.data) {
        errorMessage = JSON.stringify(error.response.data);
      } else if (error.message) {
        errorMessage = error.message;
      }

      setSubmitMessage(`${initialMatiere ? 'Modification' : 'Création'} échouée: ${errorMessage}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const isEditing = initialMatiere && initialMatiere.id_matiere > 0;

  return (
    <div className="max-w-2xl mx-auto my-8 bg-white rounded-xl shadow-md overflow-hidden">
      {/* Success/Error message */}
      {submitMessage && (
        <div className={`p-4 ${submitSuccess ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`}>
          {submitMessage}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6 p-6">
        <div className="grid grid-cols-1 gap-4">
          <Input
            name="nom_matiere_fr"
            required
            label="Nom de la matière (Français)"
            value={matiere.nom_matiere_fr || ""}
            onChange={handleChange}
            error={errors.nom_matiere_fr}
            placeholder="Ex: Mathématiques, Français, Sciences..."
          />

          <Input
            name="nom_matiere_ar"
            required
            label="Nom de la matière (Arabe)"
            value={matiere.nom_matiere_ar || ""}
            onChange={handleChange}
            error={errors.nom_matiere_ar}
            placeholder="Ex: الرياضيات، الفرنسية، العلوم..."
            dir="rtl"
          />

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              name="description"
              value={matiere.description || ""}
              onChange={handleChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              rows={4}
              placeholder="Description de la matière (optionnel)"
            />
            {errors.description && (
              <p className="text-red-500 text-sm mt-1">{errors.description}</p>
            )}
          </div>
        </div>

        {/* Boutons */}
        <div className="flex justify-end space-x-4 pt-4">
          <button
            type="submit"
            disabled={isSubmitting}
            className={`px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors ${
              isSubmitting ? "opacity-70 cursor-not-allowed" : ""
            }`}
          >
            {isSubmitting
              ? (isEditing ? "Modification..." : "Création...")
              : (isEditing ? "Modifier la matière" : "Créer la matière")
            }
          </button>
        </div>
      </form>
    </div>
  );
};

export default MatiereForm;
