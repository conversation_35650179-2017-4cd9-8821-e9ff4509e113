import React, { useState, useEffect } from "react";
import { X, User, GraduationCap, FileText, Calendar, Phone, Mail, MapPin, CreditCard, Building, Hash } from "lucide-react";
import { getDiplomesByEnseignant, getActiveContratByEnseignant } from "../services/api";
import type { Enseignant, Diplome, Contrat } from "../types";

interface EnseignantDetailModalProps {
  enseignant: Enseignant;
  isOpen: boolean;
  onClose: () => void;
}

const EnseignantDetailModal: React.FC<EnseignantDetailModalProps> = ({ enseignant, isOpen, onClose }) => {
  const [diplomes, setDiplomes] = useState<Diplome[]>([]);
  const [contrat, setContrat] = useState<Contrat | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isOpen && enseignant.id_enseignant) {
      loadEnseignantDetails();
    }
  }, [isOpen, enseignant.id_enseignant]);

  const loadEnseignantDetails = async () => {
    setLoading(true);
    try {
      // Charger les diplômes
      const diplomesResponse = await getDiplomesByEnseignant(enseignant.id_enseignant);
      if (diplomesResponse.data.success && diplomesResponse.data.data) {
        setDiplomes(diplomesResponse.data.data);
      }

      // Charger le contrat actif
      const contratResponse = await getActiveContratByEnseignant(enseignant.id_enseignant);
      if (contratResponse.data.success && contratResponse.data.data) {
        setContrat(contratResponse.data.data);
      }
    } catch (error) {
      console.error("Erreur lors du chargement des détails:", error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return "Non renseigné";
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  const getSituationFamiliale = (situation: string) => {
    const situations: { [key: string]: string } = {
      'C': 'Célibataire',
      'M': 'Marié(e)',
      'D': 'Divorcé(e)',
      'V': 'Veuf/Veuve'
    };
    return situations[situation] || situation;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6 rounded-t-xl">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-3">
              <User size={32} />
              <div>
                <h2 className="text-2xl font-bold">
                  {enseignant.user?.prenom} {enseignant.user?.nom}
                </h2>
                <p className="text-blue-100">Fiche détaillée de l'enseignant</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-gray-200 transition-colors"
            >
              <X size={24} />
            </button>
          </div>
        </div>

        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Chargement des détails...</p>
          </div>
        ) : (
          <div className="p-6 space-y-8">
            {/* Informations personnelles */}
            <section>
              <h3 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                <User className="mr-2 text-blue-600" size={20} />
                Informations personnelles
              </h3>
              <div className="bg-gray-50 rounded-lg p-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <Mail className="text-gray-500" size={16} />
                  <span className="font-medium">Email:</span>
                  <span>{enseignant.user?.email || "Non renseigné"}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Phone className="text-gray-500" size={16} />
                  <span className="font-medium">Téléphone:</span>
                  <span>{enseignant.user?.telephone || "Non renseigné"}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Calendar className="text-gray-500" size={16} />
                  <span className="font-medium">Date de naissance:</span>
                  <span>{formatDate(enseignant.user?.date_naissance || "")}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <MapPin className="text-gray-500" size={16} />
                  <span className="font-medium">Lieu de naissance:</span>
                  <span>{enseignant.user?.lieu_naissance || "Non renseigné"}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="font-medium">Sexe:</span>
                  <span>{enseignant.user?.sexe === 'homme' ? 'Homme' : enseignant.user?.sexe === 'femme' ? 'Femme' : 'Non renseigné'}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="font-medium">Nationalité:</span>
                  <span>{enseignant.user?.nationalite || "Non renseigné"}</span>
                </div>
                <div className="flex items-center space-x-2 md:col-span-2">
                  <MapPin className="text-gray-500" size={16} />
                  <span className="font-medium">Adresse:</span>
                  <span>{enseignant.user?.adresse || "Non renseigné"}</span>
                </div>
              </div>
            </section>

            {/* Informations professionnelles */}
            <section>
              <h3 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                <CreditCard className="mr-2 text-blue-600" size={20} />
                Informations professionnelles
              </h3>
              <div className="bg-gray-50 rounded-lg p-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <Hash className="text-gray-500" size={16} />
                  <span className="font-medium">CIN:</span>
                  <span>{enseignant.num_CIN || "Non renseigné"}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Hash className="text-gray-500" size={16} />
                  <span className="font-medium">CNSS:</span>
                  <span>{enseignant.num_CNSS || "Non renseigné"}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="font-medium">Situation familiale:</span>
                  <span>{getSituationFamiliale(enseignant.situation_familiale || "")}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="font-medium">Nombre d'enfants:</span>
                  <span>{enseignant.nombre_enfants || 0}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Calendar className="text-gray-500" size={16} />
                  <span className="font-medium">Date d'embauche:</span>
                  <span>{formatDate(enseignant.date_embauche || "")}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Building className="text-gray-500" size={16} />
                  <span className="font-medium">Banque:</span>
                  <span>{enseignant.banque || "Non renseigné"}</span>
                </div>
                <div className="flex items-center space-x-2 md:col-span-2">
                  <CreditCard className="text-gray-500" size={16} />
                  <span className="font-medium">RIB:</span>
                  <span>{enseignant.rib || "Non renseigné"}</span>
                </div>
              </div>
            </section>

            {/* Diplômes */}
            <section>
              <h3 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                <GraduationCap className="mr-2 text-blue-600" size={20} />
                Diplômes ({diplomes.length})
              </h3>
              {diplomes.length > 0 ? (
                <div className="space-y-3">
                  {diplomes.map((diplome, index) => (
                    <div key={diplome.id_diplome || index} className="bg-gray-50 rounded-lg p-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <div>
                          <span className="font-medium text-blue-600">Intitulé:</span>
                          <p>{diplome.intitule}</p>
                        </div>
                        <div>
                          <span className="font-medium text-blue-600">Institut:</span>
                          <p>{diplome.institut}</p>
                        </div>
                        <div>
                          <span className="font-medium text-blue-600">Spécialité:</span>
                          <p>{diplome.specialite}</p>
                        </div>
                        <div>
                          <span className="font-medium text-blue-600">Date d'obtention:</span>
                          <p>{formatDate(diplome.date_promotion)}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="bg-gray-50 rounded-lg p-4 text-center text-gray-500">
                  Aucun diplôme enregistré
                </div>
              )}
            </section>

            {/* Contrat */}
            <section>
              <h3 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                <FileText className="mr-2 text-blue-600" size={20} />
                Contrat actuel
              </h3>
              {contrat ? (
                <div className="bg-gray-50 rounded-lg p-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="font-medium text-blue-600">Type de contrat:</span>
                    <p>{contrat.type_contrat}</p>
                  </div>
                  <div>
                    <span className="font-medium text-blue-600">Poste:</span>
                    <p>{contrat.poste}</p>
                  </div>
                  <div>
                    <span className="font-medium text-blue-600">Date de début:</span>
                    <p>{formatDate(contrat.date_debut)}</p>
                  </div>
                  <div>
                    <span className="font-medium text-blue-600">Date de fin:</span>
                    <p>{contrat.date_fin ? formatDate(contrat.date_fin) : "Indéterminée"}</p>
                  </div>
                  <div>
                    <span className="font-medium text-blue-600">Salaire de base:</span>
                    <p>{contrat.salaire_base ? `${contrat.salaire_base} MAD` : "Non renseigné"}</p>
                  </div>
                  <div>
                    <span className="font-medium text-blue-600">Statut:</span>
                    <p>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        contrat.statut === 'actif' ? 'bg-green-100 text-green-800' : 
                        contrat.statut === 'terminé' ? 'bg-red-100 text-red-800' : 
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        {contrat.statut}
                      </span>
                    </p>
                  </div>
                  {contrat.description && (
                    <div className="md:col-span-2">
                      <span className="font-medium text-blue-600">Description:</span>
                      <p>{contrat.description}</p>
                    </div>
                  )}
                </div>
              ) : (
                <div className="bg-gray-50 rounded-lg p-4 text-center text-gray-500">
                  Aucun contrat actuel
                </div>
              )}
            </section>
          </div>
        )}

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 rounded-b-xl">
          <div className="flex justify-end">
            <button
              onClick={onClose}
              className="px-6 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
            >
              Fermer
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnseignantDetailModal;
