<?php

class Contrat
{
    private $pdo;
    private $table = "Contrat";

    public function __construct($db)
    {
        $this->pdo = $db;
    }

    public function getAll()
    {
        $query = "SELECT * FROM " . $this->table;
        $stmt = $this->pdo->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getById($id)
    {
        $query = "SELECT * FROM " . $this->table . " WHERE id_contrat = :id";
        $stmt = $this->pdo->prepare($query);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function getByEnseignantId($id_enseignant)
    {
        $query = "SELECT * FROM " . $this->table . " WHERE 
                  id_enseignant = :id_enseignant ORDER BY date_debut DESC";
        $stmt = $this->pdo->prepare($query);
        $stmt->bindParam(':id_enseignant', $id_enseignant, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getActiveByEnseignantId($id_enseignant)
    {
        $query = "SELECT * FROM " . $this->table . " WHERE 
                  id_enseignant = :id_enseignant AND statut = 'actif' ORDER BY date_debut DESC";
        $stmt = $this->pdo->prepare($query);
        $stmt->bindParam(':id_enseignant', $id_enseignant, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function create($data)
    {
        try {
            error_log("Données reçues pour Contrat : " . json_encode($data));

            // Validation des données requises
            if (empty($data['id_enseignant'])) {
                throw new Exception("id_enseignant est requis");
            }
            if (empty($data['type_contrat'])) {
                throw new Exception("type_contrat est requis");
            }
            if (empty($data['poste'])) {
                throw new Exception("poste est requis");
            }
            if (empty($data['date_debut'])) {
                throw new Exception("date_debut est requis");
            }
            if (!isset($data['salaire_base']) || $data['salaire_base'] <= 0) {
                throw new Exception("salaire_base doit être supérieur à 0");
            }

            // Conversion des types
            $data['id_enseignant'] = (int)$data['id_enseignant'];
            $data['salaire_base'] = (float)$data['salaire_base'];

            // Gérer les champs optionnels
            if (empty($data['date_fin'])) {
                $data['date_fin'] = null;
            }
            if (empty($data['statut'])) {
                $data['statut'] = 'actif';
            }
            if (empty($data['description'])) {
                $data['description'] = null;
            }

            error_log("Données après validation : " . json_encode($data));

            $query = "INSERT INTO " . $this->table . " (
                    id_enseignant, type_contrat, poste, date_debut, date_fin,
                    salaire_base, statut, description
                ) VALUES (
                    :id_enseignant, :type_contrat, :poste, :date_debut, :date_fin,
                    :salaire_base, :statut, :description
                )";
            $stmt = $this->pdo->prepare($query);

            $stmt->bindParam(':id_enseignant', $data['id_enseignant'], PDO::PARAM_INT);
            $stmt->bindParam(':type_contrat', $data['type_contrat'], PDO::PARAM_STR);
            $stmt->bindParam(':poste', $data['poste'], PDO::PARAM_STR);
            $stmt->bindParam(':date_debut', $data['date_debut'], PDO::PARAM_STR);
            $stmt->bindParam(':date_fin', $data['date_fin'], PDO::PARAM_STR);
            $stmt->bindParam(':salaire_base', $data['salaire_base'], PDO::PARAM_STR);
            $stmt->bindParam(':statut', $data['statut'], PDO::PARAM_STR);
            $stmt->bindParam(':description', $data['description'], PDO::PARAM_STR);

            if ($stmt->execute()) {
                error_log("Contrat créé avec succès, ID: " . $this->pdo->lastInsertId());
                return $this->pdo->lastInsertId();
            } else {
                $errorInfo = $stmt->errorInfo();
                error_log("Erreur SQL Contrat : " . implode(", ", $errorInfo));
                throw new Exception("Erreur SQL: " . $errorInfo[2]);
            }
        } catch (Exception $e) {
            error_log("Exception dans Contrat::create: " . $e->getMessage());
            throw $e;
        }
    }

    public function update($id, $data)
    {
        try {
            error_log("Mise à jour contrat $id avec données : " . json_encode($data));

            $query = "UPDATE " . $this->table . " SET
                    id_enseignant = :id_enseignant,
                    type_contrat = :type_contrat,
                    poste = :poste,
                    date_debut = :date_debut,
                    date_fin = :date_fin,
                    salaire_base = :salaire_base,
                    statut = :statut,
                    description = :description
                WHERE id_contrat = :id_contrat";
            $stmt = $this->pdo->prepare($query);

            // Préparer les variables pour bindParam (nécessaire pour les valeurs par référence)
            $id_enseignant = $data['id_enseignant'];
            $type_contrat = $data['type_contrat'];
            $poste = $data['poste'];
            $date_debut = $data['date_debut'];
            $date_fin = $data['date_fin'] ?? null;
            $salaire_base = $data['salaire_base'];
            $statut = $data['statut'];
            $description = $data['description'] ?? null;

            $stmt->bindParam(':id_enseignant', $id_enseignant, PDO::PARAM_INT);
            $stmt->bindParam(':type_contrat', $type_contrat, PDO::PARAM_STR);
            $stmt->bindParam(':poste', $poste, PDO::PARAM_STR);
            $stmt->bindParam(':date_debut', $date_debut, PDO::PARAM_STR);
            $stmt->bindParam(':date_fin', $date_fin, PDO::PARAM_STR);
            $stmt->bindParam(':salaire_base', $salaire_base, PDO::PARAM_STR);
            $stmt->bindParam(':statut', $statut, PDO::PARAM_STR);
            $stmt->bindParam(':description', $description, PDO::PARAM_STR);
            $stmt->bindParam(':id_contrat', $id, PDO::PARAM_INT);

            if ($stmt->execute()) {
                $rowCount = $stmt->rowCount();
                error_log("Contrat mis à jour avec succès, lignes affectées: $rowCount");
                return true; // Retourner true si la requête s'exécute, même si aucune ligne modifiée
            } else {
                $errorInfo = $stmt->errorInfo();
                error_log("Erreur SQL Update Contrat : " . implode(", ", $errorInfo));
                throw new Exception("Erreur SQL: " . $errorInfo[2]);
            }
        } catch (Exception $e) {
            error_log("Exception dans Contrat::update: " . $e->getMessage());
            throw $e;
        }
    }

    public function delete($id)
    {
        $query = "DELETE FROM " . $this->table . " WHERE id_contrat = :id";
        $stmt = $this->pdo->prepare($query);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);

        if ($stmt->execute()) {
            return $stmt->rowCount() > 0;
        } else {
            error_log("Erreur SQL Delete Contrat : " . implode(", ", $stmt->errorInfo()));
            return false;
        }
    }

    public function terminateContract($id)
    {
        $query = "UPDATE " . $this->table . " SET
                    statut = 'terminé',
                    date_fin = CURDATE()
                WHERE id_contrat = :id_contrat";
        $stmt = $this->pdo->prepare($query);

        $stmt->bindParam(':id_contrat', $id, PDO::PARAM_INT);

        if ($stmt->execute()) {
            return $stmt->rowCount() > 0;
        } else {
            error_log("Erreur SQL Terminate Contrat : " . implode(", ", $stmt->errorInfo()));
            return false;
        }
    }
}
