<?php

require_once '../models/AnneeScolaire.php';

class AnneeScolaireController
{
    private $anneeScolaire;

    public function __construct($db)
    {
        $this->anneeScolaire = new AnneeScolaire($db);
    }

    public function create()
    {
        try {
            $data = json_decode(file_get_contents("php://input"), true);
            
            // Validation des données
            if (empty($data['libelle']) || empty($data['date_debut']) || empty($data['date_fin'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Libellé, date de début et date de fin sont requis']);
                return;
            }

            // Vérifier si l'année scolaire existe déjà
            if ($this->anneeScolaire->exists($data['libelle'])) {
                http_response_code(409);
                echo json_encode(['success' => false, 'message' => 'Cette année scolaire existe déjà']);
                return;
            }

            // Valider les dates
            $validation = $this->anneeScolaire->validateDates($data['date_debut'], $data['date_fin']);
            if (!$validation['valid']) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => $validation['message']]);
                return;
            }

            // Définir est_active par défaut à false si non spécifié
            if (!isset($data['est_active'])) {
                $data['est_active'] = false;
            }

            $result = $this->anneeScolaire->create($data);
            if ($result) {
                echo json_encode([
                    'success' => true, 
                    'data' => ['id_annee_scolaire' => $result], 
                    'message' => 'Année scolaire créée avec succès'
                ]);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Erreur lors de la création de l\'année scolaire']);
            }
        } catch (Exception $e) {
            error_log("Erreur dans AnneeScolaireController::create: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    public function getAll()
    {
        try {
            $result = $this->anneeScolaire->getAll();
            if ($result !== false) {
                echo json_encode(['success' => true, 'data' => $result]);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Erreur lors de la récupération des années scolaires']);
            }
        } catch (Exception $e) {
            error_log("Erreur dans AnneeScolaireController::getAll: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    public function getById($id)
    {
        try {
            $result = $this->anneeScolaire->getById($id);
            if ($result) {
                echo json_encode(['success' => true, 'data' => $result]);
            } else {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Année scolaire non trouvée']);
            }
        } catch (Exception $e) {
            error_log("Erreur dans AnneeScolaireController::getById: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    public function getActive()
    {
        try {
            $result = $this->anneeScolaire->getActive();
            if ($result) {
                echo json_encode(['success' => true, 'data' => $result]);
            } else {
                echo json_encode(['success' => true, 'data' => null, 'message' => 'Aucune année scolaire active']);
            }
        } catch (Exception $e) {
            error_log("Erreur dans AnneeScolaireController::getActive: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    public function update($id)
    {
        try {
            $data = json_decode(file_get_contents("php://input"), true);
            
            // Validation des données
            if (empty($data['libelle']) || empty($data['date_debut']) || empty($data['date_fin'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Libellé, date de début et date de fin sont requis']);
                return;
            }

            // Vérifier si l'année scolaire existe déjà (en excluant l'ID actuel)
            if ($this->anneeScolaire->exists($data['libelle'], $id)) {
                http_response_code(409);
                echo json_encode(['success' => false, 'message' => 'Cette année scolaire existe déjà']);
                return;
            }

            // Valider les dates
            $validation = $this->anneeScolaire->validateDates($data['date_debut'], $data['date_fin'], $id);
            if (!$validation['valid']) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => $validation['message']]);
                return;
            }

            // Définir est_active par défaut à false si non spécifié
            if (!isset($data['est_active'])) {
                $data['est_active'] = false;
            }

            $result = $this->anneeScolaire->update($id, $data);
            if ($result) {
                echo json_encode(['success' => true, 'message' => 'Année scolaire mise à jour avec succès']);
            } else {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Année scolaire non trouvée ou aucune modification']);
            }
        } catch (Exception $e) {
            error_log("Erreur dans AnneeScolaireController::update: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    public function delete($id)
    {
        try {
            $result = $this->anneeScolaire->delete($id);
            if ($result) {
                echo json_encode(['success' => true, 'message' => 'Année scolaire supprimée avec succès']);
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Impossible de supprimer cette année scolaire (des données y sont liées)']);
            }
        } catch (Exception $e) {
            error_log("Erreur dans AnneeScolaireController::delete: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    public function toggleActive($id)
    {
        try {
            $result = $this->anneeScolaire->toggleActive($id);
            if ($result) {
                echo json_encode(['success' => true, 'message' => 'Statut de l\'année scolaire mis à jour avec succès']);
            } else {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Année scolaire non trouvée']);
            }
        } catch (Exception $e) {
            error_log("Erreur dans AnneeScolaireController::toggleActive: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    public function getStatistics()
    {
        try {
            $result = $this->anneeScolaire->getStatistics();
            if ($result !== false) {
                echo json_encode(['success' => true, 'data' => $result]);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Erreur lors de la récupération des statistiques']);
            }
        } catch (Exception $e) {
            error_log("Erreur dans AnneeScolaireController::getStatistics: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    public function validateDates()
    {
        try {
            $data = json_decode(file_get_contents("php://input"), true);
            
            if (empty($data['date_debut']) || empty($data['date_fin'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Date de début et date de fin sont requises']);
                return;
            }

            $excludeId = isset($data['exclude_id']) ? $data['exclude_id'] : null;
            $result = $this->anneeScolaire->validateDates($data['date_debut'], $data['date_fin'], $excludeId);
            
            echo json_encode([
                'success' => true, 
                'data' => $result
            ]);
        } catch (Exception $e) {
            error_log("Erreur dans AnneeScolaireController::validateDates: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }
}
