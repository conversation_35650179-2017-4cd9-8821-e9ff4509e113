<?php
// controllers/ActiviteController.php
require_once __DIR__ . '/../models/Activite.php';
require_once __DIR__ . '/../config/db.php';
require_once __DIR__ . '/../utils/auth.php';

class ActiviteController {
    private $pdo;
    private $activite;

    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->activite = new Activite($pdo);
    }

    // Récupérer toutes les activités
    public function getAll() {
        try {
            $activites = $this->activite->getAll();
            
            if ($activites === false) {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la récupération des activités'
                ]);
                return;
            }

            // Formater les données
            $activitesFormatees = array_map(function($activite) {
                return [
                    'id_activite' => (int)$activite['id_activite'],
                    'nom_activite' => $activite['nom_activite'],
                    'description' => $activite['description'],
                    'type_activite' => $activite['type_activite'],
                    'id_responsable' => $activite['id_responsable'] ? (int)$activite['id_responsable'] : null,
                    'jour_semaine' => $activite['jour_semaine'],
                    'heure_debut' => $activite['heure_debut'],
                    'heure_fin' => $activite['heure_fin'],
                    'lieu' => $activite['lieu'],
                    'capacite_max' => (int)$activite['capacite_max'],
                    'prix' => (float)$activite['prix'],
                    'statut' => $activite['statut'],
                    'date_debut' => $activite['date_debut'],
                    'date_fin' => $activite['date_fin'],
                    'responsable' => $activite['nom_responsable'] ? [
                        'nom' => $activite['nom_responsable'],
                        'prenom' => $activite['prenom_responsable']
                    ] : null,
                    'nombre_inscrits' => (int)$activite['nombre_inscrits'],
                    'places_restantes' => (int)$activite['places_restantes']
                ];
            }, $activites);

            http_response_code(200);
            echo json_encode([
                'success' => true,
                'data' => $activitesFormatees,
                'message' => 'Activités récupérées avec succès'
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la récupération des activités',
                'error' => $e->getMessage()
            ]);
        }
    }

    // Récupérer une activité par ID
    public function getById($id) {
        try {
            $activite = $this->activite->getById($id);
            
            if (!$activite) {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'message' => 'Activité non trouvée'
                ]);
                return;
            }

            // Formater les données
            $activiteFormatee = [
                'id_activite' => (int)$activite['id_activite'],
                'nom_activite' => $activite['nom_activite'],
                'description' => $activite['description'],
                'type_activite' => $activite['type_activite'],
                'id_responsable' => $activite['id_responsable'] ? (int)$activite['id_responsable'] : null,
                'jour_semaine' => $activite['jour_semaine'],
                'heure_debut' => $activite['heure_debut'],
                'heure_fin' => $activite['heure_fin'],
                'lieu' => $activite['lieu'],
                'capacite_max' => (int)$activite['capacite_max'],
                'prix' => (float)$activite['prix'],
                'statut' => $activite['statut'],
                'date_debut' => $activite['date_debut'],
                'date_fin' => $activite['date_fin'],
                'responsable' => $activite['nom_responsable'] ? [
                    'nom' => $activite['nom_responsable'],
                    'prenom' => $activite['prenom_responsable']
                ] : null,
                'nombre_inscrits' => (int)$activite['nombre_inscrits']
            ];

            http_response_code(200);
            echo json_encode([
                'success' => true,
                'data' => $activiteFormatee,
                'message' => 'Activité récupérée avec succès'
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la récupération de l\'activité',
                'error' => $e->getMessage()
            ]);
        }
    }

    // Créer une nouvelle activité
    public function create() {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            // Validation des données
            $required = ['nom_activite', 'type_activite', 'jour_semaine', 'heure_debut', 'heure_fin', 'lieu', 'capacite_max', 'prix', 'date_debut', 'date_fin'];
            foreach ($required as $field) {
                if (!isset($input[$field]) || empty($input[$field])) {
                    http_response_code(400);
                    echo json_encode([
                        'success' => false,
                        'message' => "Le champ {$field} est obligatoire"
                    ]);
                    return;
                }
            }

            // Validation des types
            if (!in_array($input['type_activite'], ['sport', 'artistique', 'culturelle', 'scientifique', 'autre'])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Type d\'activité invalide'
                ]);
                return;
            }

            if (!in_array($input['jour_semaine'], ['lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi', 'samedi'])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Jour de la semaine invalide'
                ]);
                return;
            }

            // Validation des heures
            if ($input['heure_debut'] >= $input['heure_fin']) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'L\'heure de début doit être antérieure à l\'heure de fin'
                ]);
                return;
            }

            // Validation des dates
            if ($input['date_debut'] >= $input['date_fin']) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'La date de début doit être antérieure à la date de fin'
                ]);
                return;
            }

            // Validation de la capacité
            if ((int)$input['capacite_max'] <= 0) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'La capacité maximale doit être supérieure à 0'
                ]);
                return;
            }

            // Validation du prix
            if ((float)$input['prix'] < 0) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Le prix ne peut pas être négatif'
                ]);
                return;
            }

            // Vérifier que le responsable existe (si spécifié)
            if (isset($input['id_responsable']) && $input['id_responsable']) {
                $stmt = $this->pdo->prepare('SELECT id_enseignant FROM Enseignant WHERE id_enseignant = ?');
                $stmt->execute([$input['id_responsable']]);
                if (!$stmt->fetch()) {
                    http_response_code(400);
                    echo json_encode([
                        'success' => false,
                        'message' => 'Responsable non trouvé'
                    ]);
                    return;
                }
            }

            $id = $this->activite->create($input);
            
            if ($id === false) {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la création de l\'activité'
                ]);
                return;
            }

            http_response_code(201);
            echo json_encode([
                'success' => true,
                'data' => ['id_activite' => $id],
                'message' => 'Activité créée avec succès'
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la création de l\'activité',
                'error' => $e->getMessage()
            ]);
        }
    }

    // Mettre à jour une activité
    public function update($id) {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            // Vérifier que l'activité existe
            $activiteExistante = $this->activite->getById($id);
            if (!$activiteExistante) {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'message' => 'Activité non trouvée'
                ]);
                return;
            }

            // Validation similaire à create (réutiliser la logique)
            // ... (même validation que dans create)

            $result = $this->activite->update($id, $input);
            
            if ($result === false) {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la modification de l\'activité'
                ]);
                return;
            }

            http_response_code(200);
            echo json_encode([
                'success' => true,
                'message' => 'Activité modifiée avec succès'
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la modification de l\'activité',
                'error' => $e->getMessage()
            ]);
        }
    }

    // Supprimer une activité
    public function delete($id) {
        try {
            // Vérifier que l'activité existe
            $activite = $this->activite->getById($id);
            if (!$activite) {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'message' => 'Activité non trouvée'
                ]);
                return;
            }

            $result = $this->activite->delete($id);
            
            if (is_array($result) && isset($result['error'])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => $result['error']
                ]);
                return;
            }

            if ($result === false) {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la suppression de l\'activité'
                ]);
                return;
            }

            http_response_code(200);
            echo json_encode([
                'success' => true,
                'message' => 'Activité supprimée avec succès'
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la suppression de l\'activité',
                'error' => $e->getMessage()
            ]);
        }
    }
}
?>
