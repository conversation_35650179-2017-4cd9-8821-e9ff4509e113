<?php

require_once __DIR__ . '/../controllers/AncienneEcoleController.php';

function handleAncienneEcoleRoutes($uri, $method, $pdo) {
    $ancienneEcoleController = new AncienneEcoleController($pdo);

    // GET /anciennes-ecoles - Récupérer toutes les anciennes écoles
    if ($uri === '/anciennes-ecoles' && $method === 'GET') {
        $ancienneEcoleController->getAll();
        return true;
    }

    // GET /anciennes-ecoles/{code_gresa} - Récupérer une ancienne école par code GRESA
    if (preg_match('/^\/anciennes-ecoles\/([^\/]+)$/', $uri, $matches) && $method === 'GET') {
        $code_gresa = $matches[1];
        $ancienneEcoleController->getByCodeGresa($code_gresa);
        return true;
    }

    // POST /anciennes-ecoles - Créer une nouvelle ancienne école
    if ($uri === '/anciennes-ecoles' && $method === 'POST') {
        $ancienneEcoleController->create();
        return true;
    }

    // PUT /anciennes-ecoles/{code_gresa} - Mettre à jour une ancienne école
    if (preg_match('/^\/anciennes-ecoles\/([^\/]+)$/', $uri, $matches) && $method === 'PUT') {
        $code_gresa = $matches[1];
        $ancienneEcoleController->update($code_gresa);
        return true;
    }

    // DELETE /anciennes-ecoles/{code_gresa} - Supprimer une ancienne école
    if (preg_match('/^\/anciennes-ecoles\/([^\/]+)$/', $uri, $matches) && $method === 'DELETE') {
        $code_gresa = $matches[1];
        $ancienneEcoleController->delete($code_gresa);
        return true;
    }

    return false;
}
