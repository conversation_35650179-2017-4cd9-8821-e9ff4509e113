import React, { useEffect, useState } from "react";
import Input from "./Input";
import Select from "./Select";
import Button from "./Button";
import { register, addParent, updateParent, updateUser } from "../services/api";
import type { User, Parent } from "../types";

interface ParentFormProps {
  onSuccess?: () => void;
  initialParent?: Parent | null;
}

const ParentForm: React.FC<ParentFormProps> = ({ onSuccess, initialParent }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState<boolean | null>(null);
  const [submitMessage, setSubmitMessage] = useState("");
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const [userParent, setUserParent] = useState<User>({
    id_utilisateur: 0,
    nom: "",
    prenom: "",
    email: "",
    mot_de_passe: "",
    role: "parent",
    sexe: null,
    date_naissance: "",
    lieu_naissance: "",
    nationalite: "marocain",
    telephone: "",
    adresse: "",
    photo: "",
    est_valide: false,
    est_actif: true,
  });

  const [parent, setParent] = useState<Parent>(
    initialParent || {
      id_parent: 0,
      id_utilisateur: 0,
      nom_ar: "",
      prenom_ar: "",
      num_CIN: "",
      user: undefined,
    }
  );

  useEffect(() => {
    if (initialParent) {
      const { user, ...parentFields } = initialParent;
      setParent(parentFields);
      if (user) {
        // Transformer les données pour assurer la compatibilité
        const transformedUser = {
          ...user,
          id_utilisateur: user.id_utilisateur || (user as any).id_user || 0,
          mot_de_passe: "",
        };
        // Supprimer les anciennes propriétés si elles existent
        delete (transformedUser as any).id_user;
        delete (transformedUser as any).password;

        setUserParent(transformedUser);
      }
    }
  }, [initialParent]);

  const sexeOptions = [
    { value: "homme", label: "Homme" },
    { value: "femme", label: "Femme" },
  ];

  const nationaliteOptions = [
    { value: "marocain", label: "Marocain(e)" },
    { value: "francais", label: "Français(e)" },
    { value: "espagnol", label: "Espagnol(e)" },
    { value: "algerien", label: "Algérien(ne)" },
    { value: "tunisien", label: "Tunisien(ne)" },
    { value: "autre", label: "Autre" },
  ];

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};
    const today = new Date().toISOString().split('T')[0];

    if (!userParent.nom.trim()) newErrors.nom = "Le nom est requis.";
    if (!userParent.prenom.trim()) newErrors.prenom = "Le prénom est requis.";
    if (!userParent.email.trim()) newErrors.email = "L'email est requis.";
    else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(userParent.email))
      newErrors.email = "L'email n'est pas valide.";
    if (!userParent.sexe) newErrors.sexe = "Le sexe est requis.";
    if (!userParent.date_naissance) newErrors.date_naissance = "La date de naissance est requise.";
    else if (userParent.date_naissance > today) newErrors.date_naissance = "La date de naissance ne peut pas être dans le futur.";
    if (!userParent.lieu_naissance.trim()) newErrors.lieu_naissance = "Le lieu de naissance est requis.";
    if (!userParent.nationalite) newErrors.nationalite = "La nationalité est requise.";
    if (!userParent.telephone.trim()) newErrors.telephone = "Le téléphone est requis.";
    if (!userParent.adresse?.trim()) newErrors.adresse = "L'adresse est requise.";
    if (!parent.nom_ar.trim()) newErrors.nom_ar = "Le nom arabe est requis.";
    if (!parent.prenom_ar.trim()) newErrors.prenom_ar = "Le prénom arabe est requis.";
    if (!parent.num_CIN.trim()) newErrors.num_CIN = "Le numéro de CIN est requis.";
    else if (!/^[A-Z]{1,2}[0-9]{5,6}$/.test(parent.num_CIN))
              newErrors.num_CIN = "Le numéro de CIN est invalide.";
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;

    if (["nom_ar", "prenom_ar", "num_CIN"].includes(name)) {
      setParent((prev) => ({ ...prev, [name]: value }));
    } else {
      setUserParent((prev) => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;
    setIsSubmitting(true);

    try {
      if (initialParent && parent.id_parent) {
        await updateUser(userParent.id_utilisateur, userParent);
        await updateParent(parent.id_parent, {
          nom_ar: parent.nom_ar,
          prenom_ar: parent.prenom_ar,
          num_CIN: parent.num_CIN,
        });
        setSubmitMessage("Parent modifié avec succès !");
      } else {
        const { data: createdUser } = await register(userParent);
        await addParent({ id_utilisateur: createdUser.id_user,
                          nom_ar: parent.nom_ar,
                          prenom_ar: parent.prenom_ar,
                          num_CIN: parent.num_CIN,});
        setSubmitMessage("Parent ajouté avec succès !");
      }

      setSubmitSuccess(true);
      if (onSuccess) onSuccess();
    } catch (err) {
       console.error("Erreur lors de la mise à jour :", err);
      setSubmitSuccess(false);
      setSubmitMessage("Erreur lors de la requête");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto my-6 bg-white rounded-xl shadow-md overflow-hidden">
      {submitMessage && (
        <div className={`px-5 py-2 ${submitSuccess ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`}>
          {submitMessage}
        </div>
      )}
      <form onSubmit={handleSubmit} className="space-y-4 p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-y-2 gap-x-8">
          <Input name="nom" label="Nom" required value={userParent.nom} onChange={handleChange} error={errors.nom} />
          <Input name="nom_ar" label="الإسم العائلي" required arabicKeyboard  value={parent.nom_ar} onChange={handleChange} error={errors.nom_ar} />
          <Input name="prenom" label="Prénom" required value={userParent.prenom} onChange={handleChange} error={errors.prenom} />
          <Input name="prenom_ar" label="الإسم الشخصي" required arabicKeyboard value={parent.prenom_ar} onChange={handleChange} error={errors.prenom_ar} />
          <Input name="email" label="Email" type="text" required value={userParent.email} onChange={handleChange} error={errors.email} placeholder="<EMAIL>" />
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Sexe<span className="text-red-500 font-bold"> *</span></label>
            <div className="flex space-x-6 items-center">
              {sexeOptions.map((option) => (
                <label key={option.value} className="flex items-center">
                  <Input
                    type="radio"
                    name="sexe"
                    value={option.value}
                    checked={userParent.sexe === option.value}
                    onChange={handleChange}
                    className="mr-2"
                  />
                  {option.label}
                </label>
              ))}
            </div>
            {errors.sexe && <p className="text-red-500 text-sm mt-1">{errors.sexe}</p>}
          </div>
       
          <Input
            type="date"
            name="date_naissance"
            label="Date de naissance"
            required
            value={userParent.date_naissance}
            onChange={handleChange}
            error={errors.date_naissance}
            max={new Date().toISOString().split('T')[0]} // Empêche les dates futures
          />
          <Input name="lieu_naissance" label="Lieu de naissance" required value={userParent.lieu_naissance} onChange={handleChange} error={errors.lieu_naissance} />
          <Input name="num_CIN" label="CIN" required value={parent.num_CIN} onChange={handleChange} error={errors.num_CIN} placeholder="K326598" />
          <Input name="adresse" label="Adresse" required value={userParent.adresse || ""} onChange={handleChange} error={errors.adresse}  />
          <Input name="telephone" label="Téléphone" required value={userParent.telephone} onChange={handleChange} error={errors.telephone} placeholder="Ex: +212 6 12 34 56 78" />
          <Select name="nationalite" label="Nationalité" options={nationaliteOptions} value={userParent.nationalite || ""} onChange={handleChange} error={errors.nationalite} placeholder="Sélectionnez une nationalité" />
        </div>
        <div className="flex justify-center space-x-4 mt-6">
          <Button variant="secondary" onClick={() => { if (onSuccess) onSuccess(); }} disabled={isSubmitting}>
            Annuler
          </Button>
          <Button variant="success" disabled={isSubmitting}>
            {isSubmitting ? "Envoi..." : initialParent ? "Modifier" : "Ajouter"}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default ParentForm;
