import React, { useState, useEffect } from "react";
import { Clock, Users, BookOpen, MapPin, Calendar, AlertCircle } from "lucide-react";
import Button from "./Button";
import Input from "./Input";
import Select from "./Select";
import { addCours, updateCours, getUnites, getEnseignants, getClasses, getSalles, getMatieres, getMatieresEnseignant } from "../services/api";
import type { Cours, Unite, Enseignant, Classe, Salle, Matiere } from "../types";

interface CoursFormProps {
  cours?: Cours | null;
  onSubmit: () => void;
  onCancel: () => void;
}

const CoursForm: React.FC<CoursFormProps> = ({ cours, onSubmit, onCancel }) => {
  const [formData, setFormData] = useState({
    id_unite: "",
    id_enseignant: "",
    id_classe: "",
    id_salle: "",
    jour_semaine: "",
    heure_debut: "",
    heure_fin: "",
    id_matiere: "", // Ajout du champ matière
  });

  const [unites, setUnites] = useState<Unite[]>([]);
  const [filteredUnites, setFilteredUnites] = useState<Unite[]>([]);
  const [enseignants, setEnseignants] = useState<Enseignant[]>([]);
  const [classes, setClasses] = useState<Classe[]>([]);
  const [salles, setSalles] = useState<Salle[]>([]);
  const [matieres, setMatieres] = useState<Matiere[]>([]);
  const [allEnseignants, setAllEnseignants] = useState<Enseignant[]>([]); // Tous les enseignants
  const [filteredEnseignants, setFilteredEnseignants] = useState<Enseignant[]>([]); // Enseignants filtrés
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);
  const [fieldErrors, setFieldErrors] = useState<{[key: string]: string}>({});

  const joursOptions = [
    { value: "Lundi", label: "Lundi" },
    { value: "Mardi", label: "Mardi" },
    { value: "Mercredi", label: "Mercredi" },
    { value: "Jeudi", label: "Jeudi" },
    { value: "Vendredi", label: "Vendredi" },
  ];

  useEffect(() => {
    // Charger les vraies données depuis l'API
    fetchData();
  }, []);

  // Charger les données du cours quand les unités sont disponibles
  useEffect(() => {
    if (cours && unites.length > 0) {
      // Trouver la matière correspondante à l'unité
      const unite = unites.find(u => u.id_unite === cours.id_unite);
      const id_matiere = unite ? unite.id_matiere.toString() : '';

      setFormData({
        id_matiere: id_matiere,
        id_unite: cours.id_unite.toString(),
        id_enseignant: cours.id_enseignant.toString(),
        id_classe: cours.id_classe.toString(),
        id_salle: cours.id_salle.toString(),
        jour_semaine: cours.jour_semaine,
        heure_debut: cours.heure_debut,
        heure_fin: cours.heure_fin,
      });
    }
  }, [cours, unites]);

  // Filtrer les unités selon la matière sélectionnée
  useEffect(() => {
    if (formData.id_matiere) {
      const unitesFiltered = unites.filter(unite =>
        unite.id_matiere.toString() === formData.id_matiere
      );
      setFilteredUnites(unitesFiltered);

      // Si l'unité actuellement sélectionnée n'appartient pas à la nouvelle matière, la réinitialiser
      if (formData.id_unite && !unitesFiltered.some(unite => unite.id_unite.toString() === formData.id_unite)) {
        setFormData(prev => ({ ...prev, id_unite: '' }));
      }
    } else {
      setFilteredUnites([]);
      setFormData(prev => ({ ...prev, id_unite: '' }));
    }
  }, [formData.id_matiere, unites]);

  const fetchData = async () => {
    try {
      // Charger les unités
      try {
        const unitesRes = await getUnites();
        if (unitesRes.data.success) setUnites(unitesRes.data.data);
      } catch (error) {
        console.error("Erreur lors du chargement des unités:", error);
      }

      // Charger les enseignants
      try {
        const enseignantsRes = await getEnseignants();
        console.log("Réponse enseignants:", enseignantsRes.data);
        if (enseignantsRes.data.success) {
          console.log("Premier enseignant:", enseignantsRes.data.data[0]);
          setAllEnseignants(enseignantsRes.data.data);
          setFilteredEnseignants(enseignantsRes.data.data); // Initialement, tous les enseignants
          setEnseignants(enseignantsRes.data.data);
        } else if (enseignantsRes.data && Array.isArray(enseignantsRes.data)) {
          // Si la réponse est directement un tableau
          setAllEnseignants(enseignantsRes.data);
          setFilteredEnseignants(enseignantsRes.data);
          setEnseignants(enseignantsRes.data);
        }
      } catch (error) {
        console.error("Erreur lors du chargement des enseignants:", error);
      }

      // Charger les matières
      try {
        const matieresRes = await getMatieres();
        if (matieresRes.data.success) {
          setMatieres(matieresRes.data.data);
        } else if (matieresRes.data && Array.isArray(matieresRes.data)) {
          setMatieres(matieresRes.data);
        }
      } catch (error) {
        console.error("Erreur lors du chargement des matières:", error);
      }

      // Charger les classes
      try {
        const classesRes = await getClasses();
        if (classesRes.data.success) {
          setClasses(classesRes.data.data);
        } else if (classesRes.data && Array.isArray(classesRes.data)) {
          setClasses(classesRes.data);
        }
      } catch (error) {
        console.error("Erreur lors du chargement des classes:", error);
      }

      // Charger les salles
      try {
        const sallesRes = await getSalles();
        if (sallesRes.data.success) {
          setSalles(sallesRes.data.data);
        } else if (sallesRes.data && Array.isArray(sallesRes.data)) {
          setSalles(sallesRes.data);
        }
      } catch (error) {
        console.error("Erreur lors du chargement des salles:", error);
      }
    } catch (error) {
      console.error("Erreur générale lors du chargement des données:", error);
    }
  };

  const validateForm = () => {
    const newErrors: string[] = [];
    const newFieldErrors: {[key: string]: string} = {};

    if (!formData.id_matiere) {
      newErrors.push("La matière est requise");
      newFieldErrors.id_matiere = "La matière est requise";
    }
    if (!formData.id_unite) {
      newErrors.push("L'unité est requise");
      newFieldErrors.id_unite = "L'unité est requise";
    }
    if (!formData.id_enseignant) {
      newErrors.push("L'enseignant est requis");
      newFieldErrors.id_enseignant = "L'enseignant est requis";
    }
    if (!formData.id_classe) {
      newErrors.push("La classe est requise");
      newFieldErrors.id_classe = "La classe est requise";
    }
    if (!formData.id_salle) {
      newErrors.push("La salle est requise");
      newFieldErrors.id_salle = "La salle est requise";
    }
    if (!formData.jour_semaine) {
      newErrors.push("Le jour de la semaine est requis");
      newFieldErrors.jour_semaine = "Le jour de la semaine est requis";
    }
    if (!formData.heure_debut) {
      newErrors.push("L'heure de début est requise");
      newFieldErrors.heure_debut = "L'heure de début est requise";
    }
    if (!formData.heure_fin) {
      newErrors.push("L'heure de fin est requise");
      newFieldErrors.heure_fin = "L'heure de fin est requise";
    }

    // Vérifier que l'heure de fin est après l'heure de début
    if (formData.heure_debut && formData.heure_fin) {
      if (formData.heure_debut >= formData.heure_fin) {
        newErrors.push("L'heure de fin doit être supérieure à l'heure de début");
        newFieldErrors.heure_fin = "L'heure de fin doit être supérieure à l'heure de début";
      }
    }

    setErrors(newErrors);
    setFieldErrors(newFieldErrors);
    return newErrors.length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setLoading(true);
    setErrors([]);

    try {
      const coursData = {
        id_unite: parseInt(formData.id_unite),
        id_enseignant: parseInt(formData.id_enseignant),
        id_classe: parseInt(formData.id_classe),
        id_salle: parseInt(formData.id_salle),
        jour_semaine: formData.jour_semaine,
        heure_debut: formData.heure_debut,
        heure_fin: formData.heure_fin,
      };

      let response;
      if (cours) {
        response = await updateCours(cours.id_cours, coursData);
      } else {
        response = await addCours(coursData);
      }

      if (response.data.success) {
        onSubmit();
      } else {
        if (response.data.conflicts) {
          setErrors(response.data.conflicts);
        } else {
          setErrors([response.data.message || "Erreur lors de la sauvegarde"]);
        }
      }
    } catch (error: any) {
      console.error("Erreur lors de la sauvegarde:", error);

      // Gestion des erreurs du backend
      if (error.response?.data) {
        const errorData = error.response.data;

        // Si il y a des conflits spécifiques (comme conflit de salle)
        if (errorData.conflicts && Array.isArray(errorData.conflicts)) {
          setErrors(errorData.conflicts);
        }
        // Si il y a un message d'erreur général
        else if (errorData.message) {
          setErrors([errorData.message]);
        }
        // Si il y a des erreurs de validation par champ
        else if (errorData.errors) {
          const backendErrors = Array.isArray(errorData.errors)
            ? errorData.errors
            : Object.values(errorData.errors).flat();
          setErrors(backendErrors);
        }
        // Erreur générique
        else {
          setErrors(["Erreur lors de la sauvegarde du cours"]);
        }
      } else {
        setErrors(["Erreur de connexion au serveur"]);
      }
    } finally {
      setLoading(false);
    }
  };

  // Fonction pour filtrer les enseignants selon la matière sélectionnée
  const filterEnseignantsByMatiere = async (id_matiere: string) => {
    if (!id_matiere) {
      // Si aucune matière sélectionnée, afficher tous les enseignants
      setFilteredEnseignants(allEnseignants);
      setEnseignants(allEnseignants);
      return;
    }

    try {
      // Récupérer tous les enseignants qui enseignent cette matière
      const enseignantsFiltered = [];
      for (const enseignant of allEnseignants) {
        try {
          const matieresRes = await getMatieresEnseignant(enseignant.id_enseignant);
          if (matieresRes.data.success) {
            const enseignantMatieres = matieresRes.data.data;
            const enseigneMatiere = enseignantMatieres.some((m: any) => m.id_matiere.toString() === id_matiere);
            if (enseigneMatiere) {
              enseignantsFiltered.push(enseignant);
            }
          }
        } catch (error) {
          console.error(`Erreur lors de la vérification des matières pour l'enseignant ${enseignant.id_enseignant}:`, error);
        }
      }
      setFilteredEnseignants(enseignantsFiltered);
      setEnseignants(enseignantsFiltered);
    } catch (error) {
      console.error("Erreur lors du filtrage des enseignants:", error);
    }
  };

  // Fonction pour filtrer les matières selon l'enseignant sélectionné
  const filterMatieresByEnseignant = async (id_enseignant: string) => {
    if (!id_enseignant) {
      return;
    }

    try {
      const matieresRes = await getMatieresEnseignant(parseInt(id_enseignant));
      if (matieresRes.data.success) {
        const enseignantMatieres = matieresRes.data.data;
        console.log("Matières de l'enseignant:", enseignantMatieres);
        // Les matières sont déjà filtrées côté serveur, pas besoin de filtrer ici
        // Mais on peut mettre à jour l'état si nécessaire
      }
    } catch (error) {
      console.error("Erreur lors de la récupération des matières de l'enseignant:", error);
    }
  };

  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Logique de filtrage bidirectionnel
    if (field === 'id_matiere') {
      // Si on change la matière, filtrer les enseignants
      filterEnseignantsByMatiere(value);
      // Réinitialiser l'enseignant sélectionné si il n'enseigne pas cette matière
      if (formData.id_enseignant) {
        const enseignantActuel = filteredEnseignants.find(e => e.id_enseignant.toString() === formData.id_enseignant);
        if (!enseignantActuel) {
          setFormData(prev => ({ ...prev, id_enseignant: "" }));
        }
      }
    } else if (field === 'id_enseignant') {
      // Si on change l'enseignant, on peut optionnellement filtrer les matières
      filterMatieresByEnseignant(value);
    }

    // Effacer les erreurs quand l'utilisateur modifie le formulaire
    if (errors.length > 0) {
      setErrors([]);
    }
    if (fieldErrors[field]) {
      setFieldErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6 border-t border-gray-200">
      {/* Messages d'erreur */}
      {errors.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <AlertCircle className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                {errors.length === 1 ? 'Erreur' : 'Erreurs'} de validation
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <ul className="list-disc pl-5 space-y-1">
                  {errors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Matière */}
        <Select
          name="id_matiere"
          label="Matière"
          required
          value={formData.id_matiere}
          onChange={(e) => handleChange('id_matiere', e.target.value)}
          error={fieldErrors.id_matiere}
          placeholder="Sélectionner une matière"
          options={matieres.map((matiere) => ({
            value: matiere.id_matiere.toString(),
            label: matiere.nom_matiere_fr
          }))}
        />

        {/* Unité */}
        <Select
          name="id_unite"
          label={
            <span>
              Unité
              {formData.id_matiere && (
                <span className="text-sm text-gray-500 ml-2">
                  ({filteredUnites.length} disponible{filteredUnites.length > 1 ? 's' : ''})
                </span>
              )}
            </span>
          }
          required
          value={formData.id_unite}
          onChange={(e) => handleChange('id_unite', e.target.value)}
          error={fieldErrors.id_unite}
          disabled={!formData.id_matiere || filteredUnites.length === 0}
          placeholder={
            !formData.id_matiere
              ? "Sélectionnez d'abord une matière"
              : filteredUnites.length === 0
              ? "Aucune unité disponible pour cette matière"
              : "Sélectionner une unité"
          }
          options={filteredUnites.map((unite) => ({
            value: unite.id_unite.toString(),
            label: unite.nom_unite
          }))}
        />
         

        {/* Enseignant */}
        <div>
          <Select
            name="id_enseignant"
            label={
              <span>
                Enseignant
                {formData.id_matiere && (
                  <span className="text-sm text-gray-500 ml-2">
                    ({filteredEnseignants.length} disponible{filteredEnseignants.length > 1 ? 's' : ''})
                  </span>
                )}
              </span>
            }
            required
            value={formData.id_enseignant}
            onChange={(e) => handleChange('id_enseignant', e.target.value)}
            error={fieldErrors.id_enseignant}
            disabled={formData.id_matiere && filteredEnseignants.length === 0}
            placeholder={
              formData.id_matiere && filteredEnseignants.length === 0
                ? "Aucun enseignant disponible pour cette matière"
                : "Sélectionner un enseignant"
            }
            options={filteredEnseignants.map((enseignant) => {
              // Gérer différents formats de données
              let nom = 'N/A';
              let prenom = 'N/A';

              if (enseignant.user) {
                // Format avec sous-objet user
                nom = enseignant.user.nom || 'N/A';
                prenom = enseignant.user.prenom || 'N/A';
              } else if (enseignant.nom && enseignant.prenom) {
                // Format direct
                nom = enseignant.nom;
                prenom = enseignant.prenom;
              }

              return {
                value: enseignant.id_enseignant.toString(),
                label: `${prenom} ${nom}`
              };
            })}
          />
          {formData.id_matiere && filteredEnseignants.length === 0 && (
            <p className="mt-1 text-sm text-red-600">
              Aucun enseignant n'est assigné à cette matière. Veuillez d'abord assigner des enseignants à cette matière.
            </p>
          )}
        </div>

        {/* Classe */}
        <Select
          name="id_classe"
          label="Classe"
          required
          value={formData.id_classe}
          onChange={(e) => handleChange('id_classe', e.target.value)}
          error={fieldErrors.id_classe}
          placeholder="Sélectionner une classe"
          options={classes.map((classe) => ({
            value: classe.id_classe.toString(),
            label: classe.nom_classe
          }))}
        />

        {/* Salle */}
        <Select
          name="id_salle"
          label="Salle"
          required
          value={formData.id_salle}
          onChange={(e) => handleChange('id_salle', e.target.value)}
          error={fieldErrors.id_salle}
          placeholder="Sélectionner une salle"
          options={salles.map((salle) => ({
            value: salle.id_salle.toString(),
            label: `${salle.nom_salle} (Capacité: ${salle.capacite})`
          }))}
        />

        {/* Jour de la semaine */}
        <Select
          name="jour_semaine"
          label="Jour de la semaine"
          required
          value={formData.jour_semaine}
          onChange={(e) => handleChange('jour_semaine', e.target.value)}
          error={fieldErrors.jour_semaine}
          placeholder="Sélectionner un jour"
          options={joursOptions}
        />

        {/* Heure de début */}
        <Input
          type="time"
          name="heure_debut"
          label="Heure de début"
          required
          value={formData.heure_debut}
          onChange={(e) => handleChange('heure_debut', e.target.value)}
          error={fieldErrors.heure_debut}
        />

        {/* Heure de fin */}
        <Input
          type="time"
          name="heure_fin"
          label="Heure de fin"
          required
          value={formData.heure_fin}
          onChange={(e) => handleChange('heure_fin', e.target.value)}
          error={fieldErrors.heure_fin}
        />
      </div>

      {/* Boutons */}
      <div className="flex justify-end space-x-3 pt-6 ">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={loading}
        >
          Annuler
        </Button>
        <Button
          type="submit"
          variant="primary"
          disabled={loading}
        >
          {loading ? (
            <>
              <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
              {cours ? "Modification..." : "Création..."}
            </>
          ) : (
            cours ? "Modifier le cours" : "Créer le cours"
          )}
        </Button>
      </div>
    </form>
  );
};

export default CoursForm;
