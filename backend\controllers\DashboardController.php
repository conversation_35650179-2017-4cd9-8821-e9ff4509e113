<?php

class DashboardController {
    private $pdo;

    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    public function getStatistiquesGenerales() {
        try {
            // Statistiques de base
            $stats = [];
            
            // Total élèves
            $sql = "SELECT COUNT(DISTINCT i.id_eleve) AS total
                FROM Inscription i JOIN Annee_Scolaire a ON i.id_annee_scolaire = a.id_annee_scolaire
                WHERE a.est_active = TRUE;";
            $stmt = $this->pdo->query($sql);
            $stats['total_eleves'] = $stmt->fetch()['total'];
            
            // Total enseignants
            $sql = "SELECT COUNT(DISTINCT c.id_enseignant) AS total FROM Contrat c
                    WHERE c.statut = 'actif' AND c.poste = 'enseignant';";
            $stmt = $this->pdo->query($sql);
            $stats['total_enseignants'] = $stmt->fetch()['total'];
            
            // Total classes
            $stmt = $this->pdo->query("SELECT COUNT(*) as total FROM Classe ");
            $stats['total_classes'] = $stmt->fetch()['total'];
            
            // Statistiques activités
            $stmt = $this->pdo->query("SELECT COUNT(*) as total FROM Activite");
            $stats['total_activites'] = $stmt->fetch()['total'];
            
            // Activités planifiées (futures)
            $stmt = $this->pdo->query(" SELECT COUNT(*) as total FROM Activite 
                        WHERE date_debut >= CURDATE() OR date_debut IS NULL");
            $stats['activites_planifiees'] = $stmt->fetch()['total'];
            
            // Total participants aux activités
            $stmt = $this->pdo->query("SELECT COUNT(DISTINCT id_eleve) as total FROM Participation_Activite");
            $stats['total_participants_activites'] = $stmt->fetch()['total'];
            
            
            
            echo json_encode([
                'success' => true,
                'data' => $stats,
                'message' => 'Statistiques générales récupérées avec succès'
            ]);
            
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la récupération des statistiques: ' . $e->getMessage()
            ]);
        }
    }



}
?>
