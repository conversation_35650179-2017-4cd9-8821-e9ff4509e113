<?php

class Niveau
{
    private $pdo;
    private $table = "Niveau";

    public function __construct($db)
    {
        $this->pdo = $db;
    }

    public function create($data)
    {
        try {
            $query = "INSERT INTO " . $this->table . " (cycle, libelle, prix_mensuel, frais_inscription) 
                        VALUES (:cycle, :libelle, :prix_mensuel, :frais_inscription)";
            $stmt = $this->pdo->prepare($query);

            $stmt->bindParam(':cycle', $data['cycle'], PDO::PARAM_STR);
            $stmt->bindParam(':libelle', $data['libelle'], PDO::PARAM_STR);
            $stmt->bindParam(':prix_mensuel', $data['prix_mensuel'], PDO::PARAM_STR);
            $stmt->bindParam(':frais_inscription', $data['frais_inscription'], PDO::PARAM_STR);

            if ($stmt->execute()) {
                return $this->pdo->lastInsertId();
            } else {
                error_log("Erreur SQL Niveau : " . implode(", ", $stmt->errorInfo()));
                return false;
            }
        } catch (PDOException $e) {
            error_log("Erreur PDO dans Niveau::create : " . $e->getMessage());
            error_log("Données reçues : " . json_encode($data));
            return false;
        }
    }

    public function getAll()
    {
        try {
            $query = "SELECT * FROM " . $this->table . " ORDER BY 
                CASE cycle 
                    WHEN 'maternelle' THEN 1 
                    WHEN 'primaire' THEN 2 
                    WHEN 'collège' THEN 3 
                    WHEN 'lycée' THEN 4 
                END, libelle";
            $stmt = $this->pdo->prepare($query);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur dans getAll: " . $e->getMessage());
            return false;
        }
    }

    public function getById($id)
    {
        try {
            $query = "SELECT * FROM " . $this->table . " WHERE id_niveau = :id";
            $stmt = $this->pdo->prepare($query);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return $result ? $result : false;
        } catch (PDOException $e) {
            error_log("Erreur dans getById: " . $e->getMessage());
            return false;
        }
    }

    public function getByCycle($cycle)
    {
        try {
            $query = "SELECT * FROM " . $this->table . " WHERE cycle = :cycle ORDER BY libelle";
            $stmt = $this->pdo->prepare($query);
            $stmt->bindParam(':cycle', $cycle, PDO::PARAM_STR);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur dans getByCycle: " . $e->getMessage());
            return false;
        }
    }

    public function update($id, $data)
    {
        try {
            $query = "UPDATE " . $this->table . " SET 
                     cycle = :cycle,
                     libelle = :libelle,
                     prix_mensuel = :prix_mensuel,
                     frais_inscription = :frais_inscription
                     WHERE id_niveau = :id";
            $stmt = $this->pdo->prepare($query);

            $stmt->bindParam(':cycle', $data['cycle'], PDO::PARAM_STR);
            $stmt->bindParam(':libelle', $data['libelle'], PDO::PARAM_STR);
            $stmt->bindParam(':prix_mensuel', $data['prix_mensuel'], PDO::PARAM_STR);
            $stmt->bindParam(':frais_inscription', $data['frais_inscription'], PDO::PARAM_STR);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);

            if ($stmt->execute()) {
                return $stmt->rowCount() > 0;
            } else {
                error_log("Erreur SQL Update Niveau : " . implode(", ", $stmt->errorInfo()));
                return false;
            }
        } catch (PDOException $e) {
            error_log("Erreur PDO dans Niveau::update : " . $e->getMessage());
            return false;
        }
    }

    public function delete($id)
    {
        try {
            // Vérifier s'il y a des classes liées à ce niveau
            $checkStmt = $this->pdo->prepare("SELECT COUNT(*) FROM Classe WHERE id_niveau = :id");
            $checkStmt->bindParam(':id', $id, PDO::PARAM_INT);
            $checkStmt->execute();
            $count = $checkStmt->fetchColumn();

            if ($count > 0) {
                error_log("Impossible de supprimer le niveau $id : des classes y sont liées");
                return false;
            }

            $stmt = $this->pdo->prepare("DELETE FROM " . $this->table . " WHERE id_niveau = :id");
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            
            if ($stmt->execute()) {
                return $stmt->rowCount() > 0;
            } else {
                error_log("Erreur SQL Delete Niveau : " . implode(", ", $stmt->errorInfo()));
                return false;
            }
        } catch (PDOException $e) {
            error_log("Erreur PDO dans Niveau::delete : " . $e->getMessage());
            return false;
        }
    }

    public function exists($cycle, $libelle, $excludeId = null)
    {
        try {
            // Vérifier si le niveau existe déjà pour ce cycle et ce libellé
            $sql = "SELECT COUNT(*) FROM " . $this->table . " WHERE cycle = :cycle AND libelle = :libelle";
            if ($excludeId) {
                $sql .= " AND id_niveau != :excludeId";
            }
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':cycle', $cycle, PDO::PARAM_STR);
            $stmt->bindParam(':libelle', $libelle, PDO::PARAM_STR);
            if ($excludeId) {
                $stmt->bindParam(':excludeId', $excludeId, PDO::PARAM_INT);
            }
            
            $stmt->execute();
            return $stmt->fetchColumn() > 0; //fetchColumn(): extrait le COUNT(*).
        } catch (PDOException $e) {
            error_log("Erreur dans exists: " . $e->getMessage());
            return false;
        }
    }

    public function getStatistics()
    {
        try {
            $query = "SELECT 
                    cycle,
                    COUNT(*) as nombre_niveaux,
                    AVG(prix_mensuel) as prix_moyen,
                    MIN(prix_mensuel) as prix_min,
                    MAX(prix_mensuel) as prix_max
                FROM " . $this->table . " 
                GROUP BY cycle
                ORDER BY 
                    CASE cycle 
                        WHEN 'maternelle' THEN 1 
                        WHEN 'primaire' THEN 2 
                        WHEN 'collège' THEN 3 
                        WHEN 'lycée' THEN 4 
                    END";   
            $stmt = $this->pdo->prepare($query);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur dans getStatistics: " . $e->getMessage());
            return false;
        }
    }
}
