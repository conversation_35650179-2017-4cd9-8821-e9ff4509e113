import React, { useEffect, useState } from "react";
import {
  getSalles,
  addSalle,
  updateSalle,
  deleteSalle
} from "../../services/api";
import { useAuth } from "../../context/AuthContext";
import Table from "../../components/Table";
import Modal from "../../components/Modal";
import Button from "../../components/Button";
import { Search, Filter, Plus, Eye, Edit, Trash2, Users } from "lucide-react";
import { useDeleteConfirmation } from "../../hooks/useConfirmation";
import { Salle } from "../../types";
import Input from "../../components/Input";

const Salles: React.FC = () => {
  const { user } = useAuth();
  const { confirmDelete, ConfirmationComponent } = useDeleteConfirmation();

  const [salles, setSalles] = useState<Salle[]>([]);
  const [salleToEdit, setSalleToEdit] = useState<Salle | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [loading, setLoading] = useState(true);

  const [form, setForm] = useState({ nom_salle: "", capacite: 30 });

  const fetchSalles = async () => {
    try {
      setLoading(true);
      setErrorMessage("");
      const response = await getSalles();
      setSalles(response.data.data || []);
    } catch (error) {
      console.error("Erreur lors du chargement des salles :", error);
      setErrorMessage("Impossible de charger les salles.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSalles();
  }, []);

  const handleDelete = async (salle: Salle) => {
    const confirmed = await confirmDelete("Supprimer cette salle ?");
    if (confirmed) {
      await deleteSalle(salle.id_salle);
      fetchSalles();
    }
  };

  const handleEdit = (salle: Salle) => {
    setSalleToEdit(salle);
    setForm({ nom_salle: salle.nom_salle, capacite: salle.capacite });
    setShowModal(true);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (salleToEdit) {
        await updateSalle(salleToEdit.id_salle, form);
      } else {
        await addSalle(form);
      }
      setShowModal(false);
      setSalleToEdit(null);
      setForm({ nom_salle: "", capacite: 1 });
      fetchSalles();
    } catch (error) {
      console.error("Erreur lors de l'enregistrement :", error);
      setErrorMessage("Erreur lors de l'enregistrement de la salle.");
    }
  };

  const columns = [
    { header: "ID", accessor: "id_salle", className: "p-3" },
    { header: "Nom", accessor: "nom_salle", className: "p-3" },
    { header: "Capacité", accessor: "capacite", className: "p-3" },
    ...(user?.role === "admin"
      ? [{ header: "Actions", accessor: "actions", className: "p-3" }]
      : [])
  ];

  const renderRow = (salle: Salle) => (
    <tr key={salle.id_salle} className="border-b even:bg-gray-50 hover:bg-gray-50">
      <td className="p-3 font-semibold">{salle.id_salle}</td>
      <td className="p-3">{salle.nom_salle}</td>
      <td className="p-3">{salle.capacite}</td>
      {user?.role === "admin" && (
        <td className="p-3">
          <div className="flex space-x-2">
            
            <button
              className="text-amber-500 hover:text-amber-700"
              onClick={() => handleEdit(salle)}
            >
              <Edit size={18} />
            </button>
            <button
              className="text-red-500 hover:text-red-700"
              onClick={() => handleDelete(salle)}
              title="Supprimer la salle"
            >
              <Trash2 size={18} />
            </button>
          </div>
        </td>
      )}
    </tr>
  );

  const filteredSalles = salles.filter((s) =>
    s.nom_salle.toLowerCase().includes(searchTerm.toLowerCase()) ||
    s.capacite.toString().includes(searchTerm)
  );

  return (
    <div className="space-y-6">
      {errorMessage && (
        <div className="bg-red-100 border border-red-300 rounded-lg p-4 text-red-800">
          {errorMessage}
        </div>
      )}

      <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
        <div className="relative">
          <Search size={18} className="absolute left-3 top-2.5 text-gray-400" />
          <input
            type="text"
            placeholder="Rechercher une salle..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        </div>

        <div className="flex space-x-2">
          <Button icon={<Filter size={16} />} variant="outline">Filtres</Button>
          <Button
            icon={<Plus size={16} />}
            variant="primary"
            onClick={() => {
              setSalleToEdit(null);
              setForm({ nom_salle: "", capacite: 1 });
              setShowModal(true);
            }}
          >
            Ajouter une salle
          </Button>
        </div>
      </div>

      <div className="bg-white p-4 rounded-lg shadow-md">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2">Chargement...</span>
          </div>
        ) : filteredSalles.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Users className="w-12 h-12 mx-auto mb-2 text-gray-300" />
            <p>Aucune salle trouvée</p>
            {searchTerm && (
              <p className="text-sm mt-1">
                Essayez de modifier votre recherche ou{" "}
                <button
                  onClick={() => setSearchTerm('')}
                  className="text-blue-600 hover:text-blue-800 underline"
                >
                  effacer les filtres
                </button>
              </p>
            )}
          </div>
        ) : (
          <Table columns={columns} data={filteredSalles} renderRow={renderRow} />
        )}
      </div>

      <Modal
        isOpen={showModal}
        onClose={() => {
          setShowModal(false);
          setSalleToEdit(null);
          setForm({ nom_salle: "", capacite: 1 });
        }}
        title={salleToEdit ? "Modifier la salle" : "Ajouter une salle"}
      >
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Input
              type="text"
              name="nom_salle"
              label="Nom de la salle"
              value={form.nom_salle}
              onChange={(e) => setForm({ ...form, nom_salle: e.target.value })}
              required
            />
          </div>
          <div>
            <Input
              type="number"
              name="capacite"
              label="Capacité"
              value={form.capacite}
              onChange={(e) => setForm({ ...form, capacite: parseInt(e.target.value) || 1 })}
              min={1}
              required
            />
          </div>
          <div className="flex justify-end space-x-2">
            <Button variant="secondary" onClick={() => setShowModal(false)}>Annuler</Button>
            <Button type="submit" variant="primary">
              {salleToEdit ? "Modifier" : "Ajouter"}
            </Button>
          </div>
        </form>
      </Modal>

      <ConfirmationComponent />
    </div>
  );
};

export default Salles;
