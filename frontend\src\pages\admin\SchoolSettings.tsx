import React, { useState, useEffect, createContext, useContext } from 'react';
import { School, Calendar, GraduationCap, Users } from 'lucide-react';
import SchoolInfoTab from '../../components/school-settings/SchoolInfoTab';
import NiveauxTab from '../../components/school-settings/NiveauxTab';
import AnneeScolaireTab from '../../components/school-settings/AnneeScolaireTab';
import UtilisateursTab from '../../components/school-settings/UtilisateursTab';

// Contexte pour partager les données de l'école entre les onglets
interface SchoolContextType {
  cyclesProposed: string[];
  setCyclesProposed: (cycles: string[]) => void;
}

const SchoolContext = createContext<SchoolContextType | undefined>(undefined);

export const useSchoolContext = () => {
  const context = useContext(SchoolContext);
  if (!context) {
    throw new Error('useSchoolContext must be used within a SchoolProvider');
  }
  return context;
};

const SchoolSettings: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'info' | 'niveaux' | 'annee' | 'utilisateurs' | null>(null);
  const [cyclesProposed, setCyclesProposed] = useState<string[]>(['maternelle', 'primaire']); // Cycles par défaut

  const tabs = [
    {
      id: 'info' as const,
      label: 'Informations de l\'école',
      icon: School,
      description: 'Nom, adresse, contact, logo'
    },
    {
      id: 'niveaux' as const,
      label: 'Niveaux',
      icon: GraduationCap,
      description: 'Gestion des niveaux scolaires'
    },
    {
      id: 'annee' as const,
      label: 'Année scolaire',
      icon: Calendar,
      description: 'Configuration de l\'année scolaire'
    },
    {
      id: 'utilisateurs' as const,
      label: 'Utilisateurs',
      icon: Users,
      description: 'Gestion des utilisateurs'
    }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'info':
        return <SchoolInfoTab />;
      case 'niveaux':
        return <NiveauxTab />;
      case 'annee':
        return <AnneeScolaireTab />;
      case 'utilisateurs':
      return <UtilisateursTab />; 
      default:
        return null;
    }
  };
  
  useEffect(() => {
    const savedTab = localStorage.getItem("activeSchoolTab");
    if (savedTab && ['info', 'niveaux', 'annee', 'utilisateurs'].includes(savedTab)) {
      setActiveTab(savedTab as typeof activeTab);
    } else {
      setActiveTab('info');
    }
  }, []);

  useEffect(() => {
    if (activeTab) {
      localStorage.setItem("activeSchoolTab", activeTab);
    }
  }, [activeTab]);

  return (
    <SchoolContext.Provider value={{ cyclesProposed, setCyclesProposed }}>
  
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-sm">
          {/* Tabs Navigation */}
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6" aria-label="Tabs">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`${
                      activeTab === tab.id
                        ? 'border-blue-600 text-blue-600 bg-blue-50'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    } group inline-flex items-center py-4 px-3 border-b-2 font-medium text-sm rounded-t-lg transition-all duration-200`}
                  >
                    <Icon
                      className={`${
                        activeTab === tab.id ? 'text-blue-600' : 'text-gray-400 group-hover:text-gray-500'
                      } -ml-0.5 mr-2 h-5 w-5`}
                    />
                    <div className="text-left">
                      <div className="font-medium">{tab.label}</div>
                      <div className="text-xs text-gray-400 hidden sm:block">{tab.description}</div>
                    </div>
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6 transition-all duration-300">
            {renderTabContent()}
          </div>

        </div>
      </div>
    </SchoolContext.Provider>
  );
};

export default SchoolSettings;
