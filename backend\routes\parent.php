<?php
require_once __DIR__ . '/../controllers/ParentController.php';

function handleParentRoutes($uri, $method, $pdo)
{
    $controller = new ParentController($pdo);

    if ($uri === '/parents') {
        if ($method === 'GET') {
            $controller->getParents();
            return true;
        } elseif ($method === 'POST') {
            $controller->addParent();
            return true;
        }
    }
    // AJOUTE CETTE ROUTE POUR LA RELATION
    elseif ($uri === '/parents/relation' && $method === 'POST') {
        $controller->addRelation();
        return true;
    }
    // Route pour mettre à jour une relation
    elseif ($uri === '/parents/relation' && $method === 'PUT') {
        $controller->updateRelation();
        return true;
    }
    // Route pour récupérer les parents d'un élève
    elseif (preg_match('#^/parents/eleve/(\d+)$#', $uri, $matches) && $method === 'GET') {
        $id_eleve = (int)$matches[1];
        $controller->getParentsByEleve($id_eleve);
        return true;
    }
    // Route de recherche par CIN
    elseif (preg_match('#^/parents/search/cin/(.+)$#', $uri, $matches)) {
        $cin = $matches[1];
        if ($method === 'GET') {
            $controller->searchParentByCIN($cin);
            return true;
        }
    } elseif (preg_match('#^/parents/(\d+)$#', $uri, $matches)) {
        $id = (int)$matches[1];
        if ($method === 'GET') {
            $controller->getParent($id);
            return true;
        } elseif ($method === 'PUT') {
            $controller->updateParent($id);
            return true;
        } elseif ($method === 'DELETE') {
            $controller->deleteParent($id);
            return true;
        }
    }
    error_log("Route non reconnue dans handleParentRoutes : $uri");
    return false;
}
