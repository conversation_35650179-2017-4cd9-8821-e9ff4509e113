<?php

// Affichage des erreurs (debug)
// À utiliser en développement, jamais en production (risque de fuite d’informations sensibles).
ini_set('display_errors', 1); // Affiche les erreurs PHP dans le navigateur
ini_set('display_startup_errors', 1); // Affiche les erreurs au démarrage de PHP.
error_reporting(E_ALL); // Active tous les niveaux d’erreurs, avertissements et notices.

// Gestion du CORS(Cross-Origin Resource Sharing)
header("Access-Control-Allow-Origin: *");//http://localhost:5173 ou le domaine final.
header("Access-Control-Allow-Methods: GET, POST, PUT, PATCH, DELETE, OPTIONS");//Spécifie les méthodes HTTP autorisées.
header("Access-Control-Allow-Headers: Content-Type, Authorization"); //Précise quels headers personnalisés sont acceptés.
header("Access-Control-Allow-Credentials: true");//Autorise l'envoi de cookies/token avec les requêtes.
header("Content-Type: application/json");//indique que le backend va répondre en JSON.

// Réponse directe aux requêtes OPTIONS (pré-vol CORS)
// Le navigateur envoie une requête OPTIONS avant un POST, PUT, etc.
// On y répond immédiatement avec un code 200 OK, sans exécuter le reste du code.
// Cela permet au CORS de fonctionner correctement avec le front-end.
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/../routes/api.php';


?>