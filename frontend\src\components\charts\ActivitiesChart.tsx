import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON>lt<PERSON>, Responsive<PERSON>ontaine<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Cell, Legend } from 'recharts';
import Card from '../Card';
import { Activity, TrendingUp } from 'lucide-react';

interface ActivitiesData {
  type_activite: string;
  nombre: number;
  participants: number;
}

interface ActivitiesChartProps {
  data: ActivitiesData[];
  loading?: boolean;
  chartType?: 'bar' | 'pie';
}

const ActivitiesChart: React.FC<ActivitiesChartProps> = ({ 
  data, 
  loading, 
  chartType = 'bar' 
}) => {
  const COLORS = {
    'sportive': '#10B981',
    'culturelle': '#8B5CF6', 
    'autre': '#F59E0B'
  };

  const formatData = data.map(item => ({
    name: item.type_activite.charAt(0).toUpperCase() + item.type_activite.slice(1),
    activites: item.nombre,
    participants: item.participants,
    color: COLORS[item.type_activite as keyof typeof COLORS] || '#6B7280'
  }));

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.dataKey === 'activites' ? 'Activités' : 'Participants'}: {entry.value}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  if (loading) {
    return (
      <Card>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Activity className="w-5 h-5 text-green-600" />
            <h3 className="text-lg font-medium text-gray-900">Activités par type</h3>
          </div>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
        </div>
      </Card>
    );
  }

  const totalActivites = data.reduce((sum, item) => sum + item.nombre, 0);
  const totalParticipants = data.reduce((sum, item) => sum + item.participants, 0);

  return (
    <Card>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Activity className="w-5 h-5 text-green-600" />
          <h3 className="text-lg font-medium text-gray-900">Activités par type</h3>
        </div>
        <div className="text-sm text-gray-500">
          {totalActivites} activités • {totalParticipants} participants
        </div>
      </div>

      {data.length === 0 ? (
        <div className="flex items-center justify-center h-64 text-gray-500">
          <div className="text-center">
            <Activity className="w-12 h-12 mx-auto mb-2 text-gray-300" />
            <p>Aucune activité disponible</p>
          </div>
        </div>
      ) : (
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            {chartType === 'bar' ? (
              <BarChart data={formatData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip content={<CustomTooltip />} />
                <Bar dataKey="activites" fill="#10B981" name="Activités" />
                <Bar dataKey="participants" fill="#3B82F6" name="Participants" />
              </BarChart>
            ) : (
              <PieChart>
                <Pie
                  data={formatData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="activites"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {formatData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip content={<CustomTooltip />} />
              </PieChart>
            )}
          </ResponsiveContainer>
        </div>
      )}

      {/* Statistiques détaillées */}
      <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
        {formatData.map((item, index) => (
          <div key={index} className="text-center p-3 bg-gray-50 rounded-lg">
            <div 
              className="w-4 h-4 rounded-full mx-auto mb-1"
              style={{ backgroundColor: item.color }}
            ></div>
            <div className="text-sm font-medium text-gray-900">{item.name}</div>
            <div className="text-lg font-bold text-gray-900">{item.activites}</div>
            <div className="text-xs text-gray-500">{item.participants} participants</div>
          </div>
        ))}
      </div>
    </Card>
  );
};

export default ActivitiesChart;
