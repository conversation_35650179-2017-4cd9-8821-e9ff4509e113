import React from "react";
import { Edit, Trash2, Clock, Users, MapPin, User } from "lucide-react";
import Button from "./Button";
import { getMatiereColorClasses } from "../utils/matiereColors";
import type { Cours } from "../types";

interface EmploiDuTempsGridProps {
  cours: Cours[];
  onEdit: (cours: Cours) => void;
  onDelete: (cours: Cours) => void;
  creneauxDetailles?: boolean; // Option pour afficher les créneaux de 30 min
  userRole?: 'admin' | 'enseignant' | 'eleve' | 'parent' | 'personnel';
  showSelectionMessage?: boolean;
  selectedClasse?: number | null; // Pour savoir si on filtre par classe
  selectedEnseignant?: number | null; // Pour savoir si on filtre par enseignant
}

const EmploiDuTempsGrid: React.FC<EmploiDuTempsGridProps> = ({
  cours,
  onEdit,
  onDelete,
  creneauxDetailles = true,
  userRole = 'admin',
  showSelectionMessage = false,
  selectedClasse = null,
  selectedEnseignant = null
}) => {
  const jours = ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi'];

  // Créneaux horaires selon le mode choisi
  const heures = creneauxDetailles ? [
    '08:00', '08:30', '09:00', '09:30', '10:00', '10:30',
    '11:00', '11:30', '12:00', '12:30', '13:00', '13:30',
    '14:00', '14:30', '15:00', '15:30'
  ] : [
    '08:00', '09:00', '10:00', '11:00', '12:00',
    '13:00', '14:00', '15:00'
  ];

  // Fonction pour mapper une heure à un créneau
  const mapperHeureVersCreneaux = (heureDebut: string): string => {
    // Convertir l'heure en minutes pour faciliter la comparaison
    const [heures_debut, minutes_debut] = heureDebut.split(':').map(Number);
    const minutesTotal = heures_debut * 60 + minutes_debut;

    // Mapper chaque heure de cours vers le créneau approprié
    const mappingHeures: { [key: string]: string } = {};

    heures.forEach(creneau => {
      const [h, m] = creneau.split(':').map(Number);
      const minutesCreneau = h * 60 + m;
      mappingHeures[creneau] = creneau;
    });

    // Trouver le créneau exact ou le plus proche
    let meilleurCreneau = heures[0];
    let meilleureDifference = Infinity;

    heures.forEach(creneau => {
      const [h, m] = creneau.split(':').map(Number);
      const minutesCreneau = h * 60 + m;

      // Si c'est exactement l'heure du créneau
      if (minutesCreneau === minutesTotal) {
        meilleurCreneau = creneau;
        return;
      }

      // Si le cours commence après ce créneau mais avant le suivant
      if (minutesCreneau <= minutesTotal) {
        const difference = minutesTotal - minutesCreneau;
        if (difference < meilleureDifference) {
          meilleureDifference = difference;
          meilleurCreneau = creneau;
        }
      }
    });

    return meilleurCreneau;
  };

  // Organiser les cours par jour et heure
  const organiserCours = () => {
    const grille: { [jour: string]: { [heure: string]: Cours[] } } = {};

    jours.forEach(jour => {
      grille[jour] = {};
      heures.forEach(heure => {
        grille[jour][heure] = [];
      });
    });

    cours.forEach(cours => {
      const heureDebut = cours.heure_debut.substring(0, 5); // Format HH:MM
      const heureSlot = mapperHeureVersCreneaux(heureDebut);
console.log("cours", cours);

      console.log(`Cours ${cours.nom_matiere_fr} - Heure début: ${heureDebut} - Placé dans créneau: ${heureSlot}`);

      if (grille[cours.jour_semaine] && grille[cours.jour_semaine][heureSlot]) {
        grille[cours.jour_semaine][heureSlot].push(cours);
      }
    });

    return grille;
  };

  const grilleOrganisee = organiserCours();



  const CoursCard: React.FC<{ cours: Cours; colorClass: string; heureActuelle: string }> = ({ cours, colorClass, heureActuelle }) => {
    // Déterminer quoi afficher selon le filtre actif
    const getSecondaryInfo = () => {
      if (selectedClasse && !selectedEnseignant) {
        // Si on filtre par classe, afficher l'enseignant
        return cours.nom_enseignant || "Enseignant";
      } else if (selectedEnseignant && !selectedClasse) {
        // Si on filtre par enseignant, afficher la classe
        return cours.nom_classe;
      } else {
        // Par défaut, afficher l'enseignant
        return cours.nom_enseignant || "Enseignant";
      }
    };

    // Calculer la position et la hauteur exactes
    const heureDebut = cours.heure_debut.substring(0, 5);
    const heureFin = cours.heure_fin.substring(0, 5);

    // Convertir en minutes
    const [hDebut, mDebut] = heureDebut.split(':').map(Number);
    const [hFin, mFin] = heureFin.split(':').map(Number);
    const [hActuelle, mActuelle] = heureActuelle.split(':').map(Number);

    const minutesDebut = hDebut * 60 + mDebut;
    const minutesFin = hFin * 60 + mFin;
    const minutesActuelle = hActuelle * 60 + mActuelle;

    // Calculer l'offset depuis le début du créneau actuel
    const offsetMinutes = minutesDebut - minutesActuelle;
    const hauteurCellule = creneauxDetailles ? 40 : 60;
    const offsetPixels = (offsetMinutes / 30) * hauteurCellule;

    // Calculer la hauteur totale
    const dureeMinutes = minutesFin - minutesDebut;
    const hauteurTotale = (dureeMinutes / 30) * hauteurCellule;

    return (
      <div
        className={`p-1 rounded border-l-2 ${colorClass} group text-xs absolute left-0 right-0 flex flex-col`}
        style={{
          top: `${offsetPixels}px`,
          height: `${hauteurTotale}px`,
          zIndex: 10,
          border: '1px solid #e5e7eb'
        }}
      >
        <div className="font-medium truncate text-xs leading-tight">
          {cours.nom_matiere_fr}
        </div>
        <div className="flex flex-col space-y-1 mt-0.5 flex-1">
          {selectedClasse && !selectedEnseignant ? (
            <div className="flex items-center space-x-1 text-xs text-gray-600">
              <User size={10} />
              <span className="truncate">{cours.nom_enseignant || "Enseignant"}</span>
            </div>
          ) : selectedEnseignant && !selectedClasse ? (
            <div className="flex items-center space-x-1 text-xs text-gray-600">
              <Users size={10} />
              <span className="truncate">{cours.nom_classe}</span>
            </div>
          ) : (
            <div className="flex items-center space-x-1 text-xs text-gray-600">
              <User size={10} />
              <span className="truncate">{cours.nom_enseignant || "Enseignant"}</span>
            </div>
          )}
          <div className="flex items-center space-x-1 text-xs text-gray-500">
            <Clock size={10} />
            <span className="text-xs">
              {heureDebut}-{heureFin}
            </span>
          </div>
          <div className="flex items-center space-x-1 text-xs text-gray-500">
            <MapPin size={10} />
            <span className="text-xs">{cours.nom_salle}</span>
          </div>
        </div>

        {/* Boutons d'action (visibles au hover) */}
        <div className="absolute top-0 right-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-1">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onEdit(cours);
            }}
            className="p-1 bg-white rounded shadow-sm hover:bg-gray-50 border"
            title="Modifier"
          >
            <Edit size={12} className="text-blue-600" />
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              onDelete(cours);
            }}
            className="p-1 bg-white rounded shadow-sm hover:bg-gray-50 border"
            title="Supprimer"
          >
            <Trash2 size={12} className="text-red-600" />
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
      {cours.length > 0 && (
        <>
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Emploi du temps</h3>
            <p className="text-sm text-gray-500 mt-1">
              Cliquez sur un cours pour le modifier ou le supprimer
            </p>
          </div>

          <div className="overflow-x-auto">
        <table className="min-w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">
                Heure
              </th>
              {jours.map((jour) => (
                <th
                  key={jour}
                  className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  {jour}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {heures.map((heure) => (
              <tr key={heure} className="hover:bg-gray-50">
                <td
                  className="px-2 py-2 whitespace-nowrap text-sm font-medium text-gray-900 bg-gray-50"
                  style={{
                    height: creneauxDetailles ? '40px' : '60px',
                    width: '80px',
                    maxHeight: creneauxDetailles ? '40px' : '60px'
                  }}
                >
                  {heure}
                </td>
                {jours.map((jour) => (
                  <td
                    key={`${jour}-${heure}`}
                    className="px-1 py-1 text-sm text-gray-500 align-top border-r border-gray-100"
                    style={{
                      height: creneauxDetailles ? '40px' : '60px',
                      width: '140px',
                      maxHeight: creneauxDetailles ? '40px' : '60px',
                      overflow: 'visible'
                    }}
                  >
                    <div
                      className={creneauxDetailles ? "h-[30px]" : "h-[45px]"}
                      style={{
                        height: creneauxDetailles ? '30px' : '45px',
                        overflow: 'visible',
                        position: 'relative'
                      }}
                    >
                      {grilleOrganisee[jour][heure].map((cours, index) => (
                        <CoursCard
                          key={cours.id_cours}
                          cours={cours}
                          colorClass={getMatiereColorClasses(cours.nom_matiere_fr || 'Matière')}
                          heureActuelle={heure}
                        />
                      ))}
                    </div>
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Légende */}
      <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4 text-xs text-gray-500">
            <div className="flex items-center space-x-1">
              <Clock size={12} />
              <span>Horaires</span>
            </div>
            <div className="flex items-center space-x-1">
              <Users size={12} />
              <span>Classes</span>
            </div>
            <div className="flex items-center space-x-1">
              <MapPin size={12} />
              <span>Salles</span>
            </div>
          </div>
          <div className="text-xs text-gray-500">
            Survolez un cours pour voir les options de modification
          </div>
        </div>
      </div>
        </>
      )}

      {/* Message si aucun cours */}
      {cours.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
          {showSelectionMessage ? (
            <div>
              <p className="text-gray-500 mb-2">
                Veuillez sélectionner une classe ou un enseignant
              </p>
              <p className="text-sm text-gray-400">
                pour afficher l'emploi du temps correspondant
              </p>
            </div>
          ) : userRole === 'enseignant' ? (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun cours programmé</h3>
              <p className="text-gray-500 mb-4">
                Votre emploi du temps est actuellement vide
              </p>
            </div>
          ) : userRole === 'admin' ? (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun cours planifié</h3>
              <p className="text-gray-500 mb-4">
                Commencez par ajouter des cours à l'emploi du temps
              </p>
            </div>
          ) : (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun cours disponible</h3>
              <p className="text-gray-500 mb-4">
                Votre emploi du temps sera disponible une fois les cours programmés
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default EmploiDuTempsGrid;
