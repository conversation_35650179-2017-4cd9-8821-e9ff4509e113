<?php

require_once __DIR__ . '/../models/Parent.php';

class ParentController
{
    private $parent;

    public function __construct($db)
    {
        $this->parent = new ParentEleve($db);
    }

    public function getParents()
    {
        echo json_encode($this->parent->getAll());
    }

    public function getParent($id)
    {
        $parent = $this->parent->getById($id);
        if ($parent) echo json_encode($parent);
        else http_response_code(404);
    }

    public function addParent()
    {
        try {
            $data = json_decode(file_get_contents("php://input"), true);

            if (!$data) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Aucune donnée reçue.'
                ]);
                return;
            }

            // Vérifier si le CIN existe déjà
            if (!empty($data['num_CIN']) && $this->parent->getByCIN($data['num_CIN'])) {
                http_response_code(409);
                echo json_encode([
                    'success' => false,
                    'message' => 'Ce numéro CIN est déjà utilisé par un autre parent'
                ]);
                return;
            }

            $id_parent = $this->parent->create($data);
            if ($id_parent) {
                echo json_encode([
                    'success' => true,
                    'id_parent' => $id_parent,
                    'message' => 'Parent créé avec succès.'
                ]);
            } else {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la création du parent'
                ]);
            }
        } catch (Exception $e) {
            error_log("Erreur dans addParent: " . $e->getMessage());

            // Déterminer le code de statut approprié
            $statusCode = 500;
            $message = $e->getMessage();

            // Si c'est une erreur de contrainte (CIN dupliqué, etc.)
            if (strpos($message, 'CIN est déjà utilisé') !== false) {
                $statusCode = 409; // Conflict
            } elseif (strpos($message, 'existe déjà') !== false) {
                $statusCode = 409; // Conflict
            }

            http_response_code($statusCode);
            echo json_encode([
                'success' => false,
                'message' => $message
            ]);
        }
    }
    
    /**  Ajouter une relation parent-élève  */
    public function addRelation()
    {
        $data = json_decode(file_get_contents("php://input"), true);
        $success = $this->parent->createRelation($data);
        if ($success)
            echo json_encode(['success' => true, 'message' => 'Relation créé avec succès.']);
        else
            http_response_code(400);
    }
    public function updateParent($id)
    {
        try {
            $data = json_decode(file_get_contents("php://input"), true);

            if (!$data) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Aucune donnée reçue.'
                ]);
                return;
            }

            // Vérifier si le CIN existe déjà (pour un autre parent)
            if (!empty($data['num_CIN'])) {
                $existingParent = $this->parent->getByCIN($data['num_CIN']);
                if ($existingParent && $existingParent['id_parent'] != $id) {
                    http_response_code(409);
                    echo json_encode([
                        'success' => false,
                        'message' => 'Ce numéro CIN est déjà utilisé par un autre parent'
                    ]);
                    return;
                }
            }

            if ($this->parent->update($id, $data)) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Parent mis à jour avec succès'
                ]);
            } else {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la mise à jour du parent'
                ]);
            }
        } catch (Exception $e) {
            error_log("Erreur dans updateParent: " . $e->getMessage());

            // Déterminer le code de statut approprié
            $statusCode = 500;
            $message = $e->getMessage();

            // Si c'est une erreur de contrainte (CIN dupliqué, etc.)
            if (strpos($message, 'CIN est déjà utilisé') !== false) {
                $statusCode = 409; // Conflict
            } elseif (strpos($message, 'existe déjà') !== false) {
                $statusCode = 409; // Conflict
            }

            http_response_code($statusCode);
            echo json_encode([
                'success' => false,
                'message' => $message
            ]);
        }
    }

    public function deleteParent($id)
    {
        if ($this->parent->delete($id)) echo json_encode(['message' => 'Parent supprimé']);
        else http_response_code(400);
    }

    public function searchParentByCIN($cin)
    {
        try {
            error_log("Recherche parent par CIN: $cin");
            $parent = $this->parent->findByCIN($cin);
            if ($parent) {
                echo json_encode(['success' => true, 'data' => $parent, 'message' => 'Parent trouvé']);
            } else {
                echo json_encode(['success' => false, 'data' => null, 'message' => 'Aucun parent trouvé avec ce CIN']);
            }
        } catch (Exception $e) {
            error_log("Erreur dans searchParentByCIN: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    public function getParentsByEleve($id_eleve)
    {
        try {
            error_log("Recherche des parents pour l'élève ID: " . $id_eleve);
            $parents = $this->parent->getParentsByEleveId($id_eleve);
            error_log("Nombre de parents trouvés: " . (is_array($parents) ? count($parents) : 'false'));
            if ($parents !== false) {
                echo json_encode(['success' => true, 'data' => $parents]);
            } else {
                echo json_encode(['success' => false, 'data' => [], 'message' => 'Aucun parent trouvé pour cet élève']);
            }
        } catch (Exception $e) {
            error_log("Erreur dans getParentsByEleve: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    public function updateRelation()
    {
        try {
            $data = json_decode(file_get_contents("php://input"), true);
            $result = $this->parent->updateRelation($data['id_parent'], $data['id_eleve'], $data['type_relation']);
            if ($result) {
                echo json_encode(['success' => true, 'message' => 'Relation mise à jour avec succès']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Aucune relation trouvée à mettre à jour']);
            }
        } catch (Exception $e) {
            error_log("Erreur dans updateRelation: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }


}
