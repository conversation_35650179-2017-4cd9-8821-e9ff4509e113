<?php

require_once __DIR__ . '/../models/Utilisateur.php';
require_once __DIR__ . '/../utils/auth.php';

class UtilisateurController {
    private $utilisateurModel;
    private $pdo;

    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->utilisateurModel = new Utilisateur($pdo);
    }

    /**
     * Récupérer tous les utilisateurs
     * GET /utilisateurs
     */
    public function getAll() {
        try {
            $utilisateurs = $this->utilisateurModel->getAll();

            echo json_encode([
                'success' => true,
                'data' => $utilisateurs,
                'message' => 'Utilisateurs récupérés avec succès'
            ]);

        } catch (Exception $e) {
            error_log("Erreur dans getAll: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la récupération des utilisateurs: ' . $e->getMessage()
            ]);
        }
    }

    public function getAdmins() {
        try {
            $admins = $this->utilisateurModel->getAdmins();

            echo json_encode([
                'success' => true,
                'data' => $admins,
                'message' => 'Administrateurs récupérés avec succès'
            ]);

        } catch (Exception $e) {
            error_log("Erreur dans getAdmins: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la récupération des administrateurs: ' . $e->getMessage()
            ]);
        }
    }

    public function getById($id) {
        try {
            if (!is_numeric($id) || $id <= 0) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'ID utilisateur invalide'
                ]);
                return;
            }

            $utilisateur = $this->utilisateurModel->getById($id);

            if ($utilisateur) {
                echo json_encode([
                    'success' => true,
                    'data' => $utilisateur,
                    'message' => 'Utilisateur récupéré avec succès'
                ]);
            } else {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'message' => 'Utilisateur non trouvé'
                ]);
            }

        } catch (Exception $e) {
            error_log("Erreur dans getById: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la récupération de l\'utilisateur: ' . $e->getMessage()
            ]);
        }
    }

     public function register()
    {
        try {
            $data = json_decode(file_get_contents("php://input"), true);

            if (!$data) {
                http_response_code(400);
                echo json_encode([
                    "success" => false,
                    "message" => "Aucune donnée reçue."
                ]);
                return;
            }

            // Vérifier si l'email existe déjà
            if ($this->utilisateurModel->getByEmail($data['email'])) {
                http_response_code(409);
                echo json_encode([
                    'success' => false,
                    'message' => 'Cet email est déjà utilisé par un autre utilisateur'
                ]);
                return;
            }

            // Créer l'utilisateur (avec mot de passe temporaire)
            $id_user = $this->utilisateurModel->create($data);

            if ($id_user) {
                http_response_code(201);
                echo json_encode([
                    'success' => true,
                    'id_user' => $id_user,
                    'message' => 'Utilisateur créé avec succès. Un email avec le mot de passe temporaire a été envoyé.'
                ]);
            } else {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la création de l\'utilisateur.'
                ]);
            }
        } catch (Exception $e) {
            error_log("Erreur dans register: " . $e->getMessage());

            // Déterminer le code de statut approprié
            $statusCode = 500;
            $message = $e->getMessage();

            // Si c'est une erreur de contrainte (email dupliqué, etc.)
            if (strpos($message, 'email est déjà utilisé') !== false) {
                $statusCode = 409; // Conflict
            } elseif (strpos($message, 'existe déjà') !== false) {
                $statusCode = 409; // Conflict
            }

            http_response_code($statusCode);
            echo json_encode([
                "success" => false,
                "message" => $message
            ]);
        }
    }

     public function login()
    {
        try {
            $data = json_decode(file_get_contents("php://input"), true);

            if (!$data || !isset($data['email']) || !isset($data['password'])) {
                http_response_code(400);
                echo json_encode([
                    "success" => false,
                    "message" => "Email et mot de passe requis."
                ]);
                return;
            }

            $user = $this->utilisateurModel->authenticate($data);

            if ($user) {
                // Vérifier si l'utilisateur doit changer son mot de passe
                $mustChangePassword = $this->utilisateurModel->mustChangePassword($user['id_utilisateur']);

                // Générer le token
                $token = Auth::generateToken([
                    'id' => $user['id_utilisateur'],
                    'email' => $user['email'],
                    'role' => $user['role']
                ]);

                echo json_encode([
                    "success" => true,
                    "message" => "Connexion réussie.",
                    "user" => $user,
                    "token" => $token,
                    "must_change_password" => $mustChangePassword
                ]);
            } else {
                http_response_code(401);
                echo json_encode([
                    "success" => false,
                    "message" => "Email ou mot de passe invalide."
                ]);
            }
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                "success" => false,
                "message" => "Erreur serveur: " . $e->getMessage()
            ]);
        }
    }

    public function update($id) {
        try {
            if (!is_numeric($id) || $id <= 0) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'ID utilisateur invalide'
                ]);
                return;
            }

            // Récupérer les données JSON
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Données JSON invalides'
                ]);
                return;
            }

            $result = $this->utilisateurModel->update($id, $input);

            if ($result) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Utilisateur mis à jour avec succès'
                ]);
            } else {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la mise à jour de l\'utilisateur'
                ]);
            }

        } catch (Exception $e) {
            error_log("Erreur dans update: " . $e->getMessage());

            // Déterminer le code de statut approprié
            $statusCode = 500;
            $message = $e->getMessage();

            // Si c'est une erreur de contrainte (email dupliqué, etc.)
            if (strpos($message, 'email est déjà utilisé') !== false) {
                $statusCode = 409; // Conflict
            } elseif (strpos($message, 'existe déjà') !== false) {
                $statusCode = 409; // Conflict
            }

            http_response_code($statusCode);
            echo json_encode([
                'success' => false,
                'message' => $message
            ]);
        }
    }

    public function delete($id) {
        try {
            if (!is_numeric($id) || $id <= 0) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'ID utilisateur invalide'
                ]);
                return;
            }

            $result = $this->utilisateurModel->delete($id);

            if ($result) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Utilisateur supprimé avec succès'
                ]);
            } else {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la suppression de l\'utilisateur'
                ]);
            }

        } catch (Exception $e) {
            error_log("Erreur dans delete: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la suppression de l\'utilisateur: ' . $e->getMessage()
            ]);
        }
    }

    public function changePassword()
    {
        // S'assurer que les headers ne sont pas déjà envoyés
        if (!headers_sent()) {
            header('Content-Type: application/json; charset=utf-8');
        }

        try {

            $data = json_decode(file_get_contents("php://input"), true);

            if (!$data || !isset($data['user_id']) || !isset($data['current_password']) || !isset($data['new_password'])) {
                http_response_code(400);
                echo json_encode([
                    "success" => false,
                    "message" => "Données requises: user_id, current_password, new_password"
                ]);
                return;
            }

            // Vérifier le mot de passe actuel
            $user = $this->utilisateurModel->getById($data['user_id']);
            if (!$user) {
                http_response_code(404);
                echo json_encode([
                    "success" => false,
                    "message" => "Utilisateur non trouvé"
                ]);
                return;
            }

            // Récupérer le mot de passe hashé pour vérification
            $stmt = $this->pdo->prepare("SELECT mot_de_passe FROM Utilisateur WHERE id_utilisateur = :id");
            $stmt->bindValue(':id', $data['user_id'], PDO::PARAM_INT);
            $stmt->execute();
            $userPassword = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$userPassword || !password_verify($data['current_password'], $userPassword['mot_de_passe'])) {
                http_response_code(401);
                echo json_encode([
                    "success" => false,
                    "message" => "Mot de passe actuel incorrect"
                ]);
                return;
            }

            // Valider le nouveau mot de passe
            if (strlen($data['new_password']) < 6) {
                http_response_code(400);
                echo json_encode([
                    "success" => false,
                    "message" => "Le nouveau mot de passe doit contenir au moins 6 caractères"
                ]);
                return;
            }

            // Changer le mot de passe
            $success = $this->utilisateurModel->changePassword($data['user_id'], $data['new_password']);
            error_log("Résultat changePassword: " . ($success ? 'true' : 'false'));

            if ($success) {
                error_log("Changement de mot de passe réussi");
                echo json_encode([
                    "success" => true,
                    "message" => "Mot de passe modifié avec succès"
                ]);
            } else {
                error_log("Échec changement de mot de passe");
                http_response_code(500);
                echo json_encode([
                    "success" => false,
                    "message" => "Erreur lors du changement de mot de passe"
                ]);
            }
        } catch (Exception $e) {
            error_log("Exception dans changePassword: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            http_response_code(500);
            echo json_encode([
                "success" => false,
                "message" => "Erreur serveur: " . $e->getMessage()
            ]);
        }
    }

    /*Vérifier si l'utilisateur doit changer son mot de passe  */
    public function checkPasswordStatus($userId)
    {
        try {
            $mustChange = $this->utilisateurModel->mustChangePassword($userId);

            echo json_encode([
                "success" => true,
                "must_change_password" => $mustChange
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                "success" => false,
                "message" => "Erreur serveur: " . $e->getMessage()
            ]);
        }
    }

    
}

?>
