<?php

require_once __DIR__ . '/../controllers/EnseignantController.php';
require_once __DIR__ . '/../controllers/EnseignantMatiereController.php';

function handleEnseignantRoutes($uri, $method, $pdo) {
    $controller = new EnseignantController($pdo);
    $enseignantMatiereController = new EnseignantMatiereController($pdo);

    if ($uri === '/enseignants') {
        if ($method === 'GET') {
            $controller->getEnseignants();
            return true;
        } elseif ($method === 'POST') {
            $controller->addEnseignant();
            return true;
        }
    } elseif (preg_match('#^/enseignants/(\d+)$#', $uri, $matches)) {
        $id = (int)$matches[1];
        if ($method === 'GET') {
            $controller->getEnseignant($id);
            return true;
        } elseif ($method === 'PUT') {
            $controller->updateEnseignant($id);
            return true;
        } elseif ($method === 'DELETE') {
            $controller->deleteEnseignant($id);
            return true;
        }
    } elseif (preg_match('#^/enseignants/(\d+)/contrats/active$#', $uri, $matches)) {
        $id_enseignant = (int)$matches[1];
        if ($method === 'GET') {
            $controller->getActiveContratByEnseignant($id_enseignant);
            return true;
        }
    } elseif (preg_match('#^/enseignants/(\d+)/matieres$#', $uri, $matches)) {
        $id_enseignant = (int)$matches[1];
        if ($method === 'GET') {
            $enseignantMatiereController->getMatieresEnseignant($id_enseignant);
            return true;
        } elseif ($method === 'POST') {
            $enseignantMatiereController->assignMatieresEnseignant($id_enseignant);
            return true;
        } elseif ($method === 'PUT') {
            $enseignantMatiereController->updateMatieresEnseignant($id_enseignant);
            return true;
        } elseif ($method === 'DELETE') {
            $enseignantMatiereController->deleteMatieresEnseignant($id_enseignant);
            return true;
        }
    } elseif (preg_match('#^/enseignants/(\d+)/eleves$#', $uri, $matches)) {
        $id_enseignant = (int)$matches[1];
        if ($method === 'GET') {
            $controller->getElevesEnseignant($id_enseignant);
            return true;
        }
    } elseif (preg_match('#^/enseignants/(\d+)/classes$#', $uri, $matches)) {
        $id_enseignant = (int)$matches[1];
        if ($method === 'GET') {
            $controller->getClassesEnseignant($id_enseignant);
            return true;
        }
    } elseif (preg_match('#^/enseignants/(\d+)/cours-aujourdhui$#', $uri, $matches)) {
        $id_enseignant = (int)$matches[1];
        if ($method === 'GET') {
            $controller->getCoursAujourdhui($id_enseignant);
            return true;
        }
    }
    error_log("Route non reconnue dans handleEnseignantRoutes : $uri");
    return false;
}
