<?php

require_once __DIR__ . '/../controllers/UniteController.php';

function handleUniteRoutes($uri, $method, $pdo) {
    $controller = new UniteController($pdo);

    // GET /unites - Récupérer toutes les unités
    if ($uri === '/unites' && $method === 'GET') {
        $controller->getAll();
        return true;
    }

    // POST /unites - Créer une nouvelle unité
    if ($uri === '/unites' && $method === 'POST') {
        $controller->create();
        return true;
    }

    // GET /unites/{id} - Récupérer une unité par ID
    if (preg_match('#^/unites/(\d+)$#', $uri, $matches) && $method === 'GET') {
        $id = (int)$matches[1];
        $controller->getById($id);
        return true;
    }

    // PUT /unites/{id} - Mettre à jour une unité
    if (preg_match('#^/unites/(\d+)$#', $uri, $matches) && $method === 'PUT') {
        $id = (int)$matches[1];
        $controller->update($id);
        return true;
    }

    // DELETE /unites/{id} - Supprimer une unité
    if (preg_match('#^/unites/(\d+)$#', $uri, $matches) && $method === 'DELETE') {
        $id = (int)$matches[1];
        $controller->delete($id);
        return true;
    }

    return false;
}

?>
