import React, { useState, useEffect } from "react";
import { Plus, Search, Eye, Edit, Trash2, Book } from "lucide-react";
import Modal from "../../components/Modal";
import MatiereForm from "../../components/MatiereForm";
import Button from "../../components/Button";
import Table from "../../components/Table";
import { getMatieres, deleteMatiere } from "../../services/api";
import type { Matiere } from "../../types";
import { useAuth } from "../../context/AuthContext";
import { useDeleteConfirmation } from "../../hooks/useConfirmation";

const Matieres: React.FC = () => {
  const { user } = useAuth();
  const [matieres, setMatieres] = useState<Matiere[]>([]);
  const [filteredMatieres, setFilteredMatieres] = useState<Matiere[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [matiereToEdit, setMatiereToEdit] = useState<Matiere | null>(null);
  const [matiereToView, setMatiereToView] = useState<Matiere | null>(null);
  const [errorMessage, setErrorMessage] = useState("");
  const [loading, setLoading] = useState(true);

  // Hook pour la confirmation de suppression
  const { confirmDelete, ConfirmationComponent } = useDeleteConfirmation();

  const fetchMatieres = async () => {
    try {
      setLoading(true);
      const response = await getMatieres();
      console.log("API Response:", response.data);
      
      if (response.data.success && Array.isArray(response.data.data)) {
        setMatieres(response.data.data);
        setFilteredMatieres(response.data.data);
      } else {
        console.error("Format de réponse inattendu:", response.data);
        setErrorMessage("Erreur lors du chargement des matières");
        setMatieres([]);
        setFilteredMatieres([]);
      }
    } catch (error) {
      console.error("Erreur lors du chargement des matières:", error);
      setErrorMessage("Erreur lors du chargement des matières");
      setMatieres([]);
      setFilteredMatieres([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMatieres();
  }, []);

  // Filtrage des matières
  const filteredMatieresData = matieres.filter((m) => {
    const matchesSearch = !searchTerm ||
      m.nom_matiere_fr?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      m.nom_matiere_ar?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      m.description?.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesSearch;
  });

  const handleAddMatiere = () => {
    setMatiereToEdit(null);
    setShowModal(true);
  };

  const handleEditMatiere = (matiere: Matiere) => {
    setMatiereToEdit(matiere);
    setShowModal(true);
  };

  const handleViewMatiere = (matiere: Matiere) => {
    setMatiereToView(matiere);
    setShowDetailModal(true);
  };

  const handleDelete = async (id: number) => {
    try {
      const response = await deleteMatiere(id);
      if (response.data.success) {
        await fetchMatieres();
      } else {
        setErrorMessage("Erreur lors de la suppression de la matière");
      }
    } catch (error) {
      console.error("Erreur lors de la suppression:", error);
      setErrorMessage("Erreur lors de la suppression de la matière");
    }
  };

  const handleDeleteClick = (matiere: Matiere) => {
    const matiereName = matiere.nom_matiere_fr;
    confirmDelete(
      () => handleDelete(matiere.id_matiere),
      matiereName,
      "Cette action supprimera définitivement cette matière et toutes ses données associées."
    );
  };

  const handleCloseDetailModal = () => {
    setShowDetailModal(false);
    setMatiereToView(null);
  };

  // Configuration des colonnes pour le tableau
  const columns = [
    { header: "Nom (Français)", accessor: "nom_matiere_fr", className: "p-3" },
    { header: "Nom (Arabe)", accessor: "nom_matiere_ar", className: "p-3" },
    { header: "Description", accessor: "description", className: "p-3 hidden lg:table-cell" },
    ...(user?.role === "admin" ? [{ header: "Actions", accessor: "actions", className: "p-3" }] : []),
  ];

  const renderRow = (matiere: Matiere) => (
    <tr key={matiere.id_matiere} className="border-b even:bg-gray-50 hover:bg-gray-50">
      <td className="p-3 font-semibold">{matiere.nom_matiere_fr}</td>
      <td className="p-3" dir="rtl">{matiere.nom_matiere_ar}</td>
      <td className="p-3 hidden lg:table-cell">{matiere.description || "-"}</td>
      {user?.role === "admin" && (
        <td className="p-3">
          <div className="flex space-x-2">
            <button
              onClick={() => handleViewMatiere(matiere)}
              className="text-blue-500 hover:text-blue-700"
              title="Voir les détails"
            >
              <Eye size={18} />
            </button>
            <button
              onClick={() => handleEditMatiere(matiere)}
              className="text-amber-500 hover:text-amber-700"
              title="Modifier"
            >
              <Edit size={18} />
            </button>
            <button
              onClick={() => handleDeleteClick(matiere)}
              className="text-red-500 hover:text-red-700"
              title="Supprimer"
            >
              <Trash2 size={18} />
            </button>
          </div>
        </td>
      )}
    </tr>
  );



  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-lg">Chargement des matières...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {errorMessage && (
        <div className="bg-red-100 border border-red-300 p-4 rounded text-red-800">
          {errorMessage}
        </div>
      )}

      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search size={18} className="text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Rechercher par nom ou description..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="block w-64 pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        </div>
        <div className="flex items-center gap-2">
          {user?.role === "admin" && (
            <Button icon={<Plus size={16} />} variant="primary" onClick={handleAddMatiere}>
              Ajouter une matière
            </Button>
          )}
        </div>
      </div>

      <div className="bg-white p-4 rounded-lg shadow">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full"></div>
            <span className="ml-2">Chargement...</span>
          </div>
        ) : filteredMatieresData.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Book className="mx-auto mb-2 w-10 h-10" />
            <p>Aucune matière trouvée</p>
            {searchTerm && (
              <p className="text-sm">
                Essayez de modifier votre recherche ou{" "}
                <button onClick={() => setSearchTerm('')} className="text-blue-600 underline">
                  effacer les filtres
                </button>
              </p>
            )}
          </div>
        ) : (
          <Table columns={columns} data={filteredMatieresData} renderRow={renderRow} />
        )}
      </div>

      {/* Modal d'ajout/modification */}
      <Modal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        title={matiereToEdit ? "Modification de la matière" : "Ajout d'une matière"}
      >
        <MatiereForm
          initialMatiere={matiereToEdit}
          onSuccess={() => {
            setShowModal(false);
            fetchMatieres();
          }}
        />
      </Modal>

      {/* Modal de détails */}
      {matiereToView && (
        <Modal
          isOpen={showDetailModal}
          onClose={handleCloseDetailModal}
          title="Détails de la matière"
        >
          <div className="p-6">
            <div className="grid grid-cols-1 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Nom de la matière (Français)</label>
                <p className="mt-1 text-sm text-gray-900">{matiereToView.nom_matiere_fr}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Nom de la matière (Arabe)</label>
                <p className="mt-1 text-sm text-gray-900" dir="rtl">{matiereToView.nom_matiere_ar}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Description</label>
                <p className="mt-1 text-sm text-gray-900">{matiereToView.description || "Aucune description"}</p>
              </div>
            </div>
          </div>
        </Modal>
      )}

      {/* Composant de confirmation de suppression */}
      <ConfirmationComponent />
    </div>
  );
};

export default Matieres;
