import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, GraduationCap, DollarSign, BookOpen, X } from 'lucide-react';
import Button from '../Button';
import Input from '../Input';
import Select from '../Select';
import { NiveauDetaille } from '../../types';
import { getNiveaux, addNiveau, updateNiveau, deleteNiveau } from '../../services/api';
import { useSchoolContext } from '../../pages/admin/SchoolSettings';
import { extractErrorMessage, extractSuccessMessage } from '../../utils/errorUtils';

const NiveauxTab: React.FC = () => {
  const { cyclesProposed } = useSchoolContext();
  const [niveaux, setNiveaux] = useState<NiveauDetaille[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [editingNiveau, setEditingNiveau] = useState<NiveauDetaille | null>(null);
  const [formData, setFormData] = useState<Partial<NiveauDetaille>>({
    cycle: cyclesProposed[0] as any || 'primaire', // Premier cycle proposé par défaut
    libelle: '',
    prix_mensuel: 0,
    frais_inscription: 0
  });
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [validationErrors, setValidationErrors] = useState<{ [key: string]: string }>({});

  // Créer les options de cycle basées sur les cycles proposés par l'école
  const allCycleLabels = {
    'maternelle': 'Maternelle',
    'primaire': 'Primaire',
    'collège': 'Collège',
    'lycée': 'Lycée'
  };

  const cycleOptions = cyclesProposed.map(cycle => ({
    value: cycle,
    label: allCycleLabels[cycle as keyof typeof allCycleLabels] || cycle
  }));

  const cycleColors = {
    maternelle: 'bg-pink-100 text-pink-800',
    primaire: 'bg-blue-100 text-blue-800',
    collège: 'bg-green-100 text-green-800',
    lycée: 'bg-purple-100 text-purple-800'
  };

  useEffect(() => {
    loadNiveaux();
  }, []);

  // Effet pour faire disparaître le message après 3 secondes
  useEffect(() => {
    if (message) {
      const timer = setTimeout(() => {
        setMessage(null);
      }, 3000); // 3 secondes

      return () => clearTimeout(timer);
    }
  }, [message]);



  const loadNiveaux = async () => {
    setIsLoading(true);
    try {
      const response = await getNiveaux();
      if (response.data.success) {
        setNiveaux(response.data.data);
      } else {
        const errorMessage = response.data.message || 'Erreur lors du chargement des niveaux';
        setMessage({ type: 'error', text: errorMessage });
      }
    } catch (error: any) {
      console.error('Erreur lors du chargement des niveaux:', error);
      const errorMessage = extractErrorMessage(error, 'Erreur lors du chargement des niveaux');
      setMessage({ type: 'error', text: errorMessage });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'prix_mensuel' || name === 'frais_inscription' ? parseFloat(value) || 0 : value
    }));

    // Effacer l'erreur de validation pour ce champ
    if (validationErrors[name]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Fonction pour valider les champs
  const validateForm = (): boolean => {
    const errors: { [key: string]: string } = {};

    if (!formData.cycle) {
      errors.cycle = 'Le cycle est requis';
    }

    if (!formData.libelle?.trim()) {
      errors.libelle = 'Le libellé est requis';
    }

    if (!formData.prix_mensuel || formData.prix_mensuel < 0) {
      errors.prix_mensuel = 'Le prix mensuel doit être positif';
    }

    if (!formData.frais_inscription || formData.frais_inscription < 0) {
      errors.frais_inscription = 'Les frais d\'inscription doivent être positifs';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Fonction pour extraire les erreurs de validation du backend
  const extractValidationErrors = (error: any): void => {
    const errors: { [key: string]: string } = {};

    if (error.response?.data?.errors) {
      // Si le backend renvoie des erreurs de validation structurées
      Object.keys(error.response.data.errors).forEach(field => {
        errors[field] = error.response.data.errors[field][0]; // Premier message d'erreur
      });
    } else if (error.response?.data?.message) {
      // Analyser le message d'erreur pour extraire les champs
      const message = error.response.data.message.toLowerCase();

      if (message.includes('cycle')) {
        errors.cycle = error.response.data.message;
      } else if (message.includes('libellé')) {
        errors.libelle = error.response.data.message;
      } else if (message.includes('prix') && message.includes('négatif')) {
        errors.prix_mensuel = error.response.data.message;
        errors.frais_inscription = error.response.data.message;
      } else if (message.includes('existe déjà')) {
        errors.libelle = error.response.data.message;
      }
    }

    setValidationErrors(errors);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Effacer les erreurs de validation précédentes
    setValidationErrors({});

    // Valider le formulaire côté client
    if (!validateForm()) {
      return;
    }

    try {
      if (editingNiveau) {
        // Mise à jour
        const response = await updateNiveau(editingNiveau.id_niveau, formData);
        if (response.data.success) {
          const successMessage = extractSuccessMessage(response, 'Niveau mis à jour avec succès');
          setMessage({ type: 'success', text: successMessage });
          loadNiveaux(); // Recharger la liste
          resetForm(); // Fermer la modal seulement en cas de succès
        } else {
          const errorMessage = response.data.message || 'Erreur lors de la mise à jour';
          setMessage({ type: 'error', text: errorMessage });
        }
      } else {
        // Création
        const response = await addNiveau(formData);
        if (response.data.success) {
          const successMessage = extractSuccessMessage(response, 'Niveau créé avec succès');
          setMessage({ type: 'success', text: successMessage });
          loadNiveaux(); // Recharger la liste
          resetForm(); // Fermer la modal seulement en cas de succès
        } else {
          const errorMessage = response.data.message || 'Erreur lors de la création';
          setMessage({ type: 'error', text: errorMessage });
        }
      }
    } catch (error: any) {
      console.error('Erreur lors de la sauvegarde:', error);

      // Extraire les erreurs de validation du backend
      extractValidationErrors(error);

      // Afficher aussi le message d'erreur général si pas d'erreurs de validation spécifiques
      if (Object.keys(validationErrors).length === 0) {
        const errorMessage = extractErrorMessage(error, 'Erreur lors de la sauvegarde');
        setMessage({ type: 'error', text: errorMessage });
      }
    }
  };

  const handleEdit = (niveau: NiveauDetaille) => {
    setEditingNiveau(niveau);
    setFormData({
      cycle: niveau.cycle,
      libelle: niveau.libelle,
      prix_mensuel: niveau.prix_mensuel,
      frais_inscription: niveau.frais_inscription
    });
    setShowForm(true);
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce niveau ?')) {
      try {
        const response = await deleteNiveau(id);
        if (response.data.success) {
          const successMessage = extractSuccessMessage(response, 'Niveau supprimé avec succès');
          setMessage({ type: 'success', text: successMessage });
          loadNiveaux(); // Recharger la liste
        } else {
          const errorMessage = response.data.message || 'Erreur lors de la suppression';
          setMessage({ type: 'error', text: errorMessage });
        }
      } catch (error: any) {
        console.error('Erreur lors de la suppression:', error);
        const errorMessage = extractErrorMessage(error, 'Erreur lors de la suppression');
        setMessage({ type: 'error', text: errorMessage });
      }
    }
  };

  const resetForm = () => {
    setFormData({
      cycle: cyclesProposed[0] as any || 'primaire',
      libelle: '',
      prix_mensuel: 0,
      frais_inscription: 0
    });
    setEditingNiveau(null);
    setShowForm(false);
    setValidationErrors({}); // Effacer les erreurs de validation
  };

  // Filtrer les niveaux pour ne montrer que ceux des cycles proposés
  const filteredNiveaux = niveaux.filter(niveau => cyclesProposed.includes(niveau.cycle));

  const groupedNiveaux = filteredNiveaux.reduce((acc, niveau) => {
    if (!acc[niveau.cycle]) {
      acc[niveau.cycle] = [];
    }
    acc[niveau.cycle].push(niveau);
    return acc;
  }, {} as Record<string, NiveauDetaille[]>);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Chargement...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Message de statut - seulement quand la modal n'est pas ouverte */}
      {message && !showForm && (
        <div className={`p-4 rounded-lg relative ${
          message.type === 'success' ? 'bg-green-50 text-green-800 border border-green-200' : 'bg-red-50 text-red-800 border border-red-200'
        }`}>
          <div className="flex items-center justify-between">
            <span>{message.text}</span>
            <button
              onClick={() => setMessage(null)}
              className="ml-4 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Gestion des niveaux</h3>
          <p className="text-sm text-gray-500">Configurez les niveaux scolaires et leurs tarifs</p>
        </div>
        {cyclesProposed.length > 0 && (
          <Button
            onClick={() => setShowForm(true)}
            className="flex items-center space-x-2"
          >
            <Plus className="h-4 w-4" />
            <span>Ajouter un niveau</span>
          </Button>
        )}
      </div>

      {/* Message si aucun cycle proposé */}
      {cyclesProposed.length === 0 && (
        <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
          <div className="flex items-center space-x-3">
            <div className="bg-yellow-100 p-2 rounded-full">
              <GraduationCap className="h-5 w-5 text-yellow-600" />
            </div>
            <div>
              <h4 className="text-yellow-800 font-medium">Aucun cycle configuré</h4>
              <p className="text-yellow-700 text-sm">
                Veuillez d'abord sélectionner les cycles proposés dans l'onglet "Informations de l'école"
                avant de pouvoir ajouter des niveaux.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Modal Formulaire */}
      {showForm && cyclesProposed.length > 0 && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            {/* En-tête de la modal */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">
                {editingNiveau ? 'Modifier le niveau' : 'Ajouter un nouveau niveau'}
              </h3>
              <button
                onClick={resetForm}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Message d'erreur dans la modal */}
            {message && (
              <div className={`mx-6 mt-4 p-4 rounded-lg relative ${
                message.type === 'success' ? 'bg-green-50 text-green-800 border border-green-200' : 'bg-red-50 text-red-800 border border-red-200'
              }`}>
                <div className="flex items-center justify-between">
                  <span>{message.text}</span>
                  <button
                    onClick={() => setMessage(null)}
                    className="ml-4 text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>
            )}

            {/* Corps de la modal */}
            <form onSubmit={handleSubmit} className="p-6 space-y-4">
              <Select
                name="cycle"
                label="Cycle"
                value={formData.cycle || ''}
                onChange={handleInputChange}
                options={cycleOptions}
                required
                error={validationErrors.cycle}
              />

              <Input
                name="libelle"
                label="Libellé du niveau"
                value={formData.libelle || ''}
                onChange={handleInputChange}
                required
                placeholder="Ex: CP, CE1, 6ème..."
                error={validationErrors.libelle}
              />

              <Input
                name="prix_mensuel"
                label="Prix mensuel (MAD)"
                type="number"
                value={formData.prix_mensuel || ''}
                onChange={handleInputChange}
                required
                min="0"
                step="0.01"
                error={validationErrors.prix_mensuel}
              />

              <Input
                name="frais_inscription"
                label="Frais d'inscription (MAD)"
                type="number"
                value={formData.frais_inscription || ''}
                onChange={handleInputChange}
                required
                min="0"
                step="0.01"
                error={validationErrors.frais_inscription}
              />

              {/* Pied de la modal */}
              <div className="flex justify-end space-x-3 pt-4">
                <Button
                  type="button"
                  variant="secondary"
                  onClick={resetForm}
                >
                  Annuler
                </Button>
                <Button type="submit" variant="primary">
                  {editingNiveau ? 'Modifier' : 'Ajouter'}
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Liste des niveaux groupés par cycle */}
      <div className="space-y-6">
        {Object.entries(groupedNiveaux).map(([cycle, niveauxDuCycle]) => (
          <div key={cycle} className="bg-white border rounded-lg overflow-hidden">
            <div className="bg-gray-50 px-6 py-3 border-b">
              <div className="flex items-center space-x-3">
                <GraduationCap className="h-5 w-5 text-gray-600" />
                <h4 className="text-lg font-medium text-gray-900 capitalize">{cycle}</h4>
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${cycleColors[cycle as keyof typeof cycleColors]}`}>
                  {niveauxDuCycle.length} niveau{niveauxDuCycle.length > 1 ? 'x' : ''}
                </span>
              </div>
            </div>
            
            <div className="divide-y divide-gray-200">
              {niveauxDuCycle.map((niveau) => (
                <div key={niveau.id_niveau} className="px-6 py-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <BookOpen className="h-4 w-4 text-gray-400" />
                        <h5 className="text-md font-medium text-gray-900">{niveau.libelle}</h5>
                      </div>
                      <div className="mt-2 flex items-center space-x-6 text-sm text-gray-500">
                        <div className="flex items-center space-x-1">
                          <DollarSign className="h-4 w-4" />
                          <span>Mensuel: {niveau.prix_mensuel.toLocaleString()} MAD</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <DollarSign className="h-4 w-4" />
                          <span>Inscription: {niveau.frais_inscription.toLocaleString()} MAD</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleEdit(niveau)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDelete(niveau.id_niveau)}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {filteredNiveaux.length === 0 && cyclesProposed.length > 0 && (
        <div className="text-center py-12">
          <GraduationCap className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Aucun niveau configuré</h3>
          <p className="mt-1 text-sm text-gray-500">Commencez par ajouter un niveau scolaire pour les cycles proposés.</p>
          <div className="mt-6">
            <Button onClick={() => setShowForm(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Ajouter le premier niveau
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default NiveauxTab;
