const express = require('express');
const router = express.Router();
const db = require('../config/database');
const { authenticateToken, requireRole } = require('../middleware/auth');

// Middleware d'authentification pour toutes les routes
router.use(authenticateToken);

// GET /notes - Récupérer toutes les notes
router.get('/', async (req, res) => {
  try {
    const query = `
      SELECT 
        n.*,
        u.nom as nom_eleve,
        u.prenom as prenom_eleve,
        el.code_massar,
        e.type_examen,
        e.date_examen,
        m.nom_matiere_fr,
        c.nom_classe
      FROM Note n
      JOIN Eleve el ON n.id_eleve = el.id_eleve
      JOIN User u ON el.id_user = u.id_user
      JOIN Examen e ON n.id_examen = e.id_examen
      JOIN Matiere m ON e.id_matiere = m.id_matiere
      JOIN Classe c ON e.id_classe = c.id_classe
      ORDER BY e.date_examen DESC, u.nom, u.prenom
    `;

    const [notes] = await db.execute(query);

    // Formater les données
    const notesFormatees = notes.map(note => ({
      ...note,
      eleve: {
        nom: note.nom_eleve,
        prenom: note.prenom_eleve,
        code_massar: note.code_massar
      },
      examen: {
        type_examen: note.type_examen,
        date_examen: note.date_examen,
        matiere: {
          nom_matiere_fr: note.nom_matiere_fr
        },
        classe: {
          nom_classe: note.nom_classe
        }
      }
    }));

    res.json({
      success: true,
      data: notesFormatees,
      message: 'Notes récupérées avec succès'
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des notes:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des notes',
      error: error.message
    });
  }
});

// GET /examens/:id/notes - Récupérer les notes d'un examen
router.get('/examens/:id/notes', async (req, res) => {
  try {
    const { id } = req.params;

    const query = `
      SELECT 
        n.*,
        u.nom as nom_eleve,
        u.prenom as prenom_eleve,
        el.code_massar
      FROM Note n
      JOIN Eleve el ON n.id_eleve = el.id_eleve
      JOIN User u ON el.id_user = u.id_user
      WHERE n.id_examen = ?
      ORDER BY u.nom, u.prenom
    `;

    const [notes] = await db.execute(query, [id]);

    // Formater les données
    const notesFormatees = notes.map(note => ({
      ...note,
      eleve: {
        nom: note.nom_eleve,
        prenom: note.prenom_eleve,
        code_massar: note.code_massar
      }
    }));

    res.json({
      success: true,
      data: notesFormatees,
      message: 'Notes de l\'examen récupérées avec succès'
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des notes de l\'examen:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des notes de l\'examen',
      error: error.message
    });
  }
});

// GET /eleves/:id/notes - Récupérer les notes d'un élève
router.get('/eleves/:id/notes', async (req, res) => {
  try {
    const { id } = req.params;

    const query = `
      SELECT 
        n.*,
        e.type_examen,
        e.date_examen,
        e.semestre,
        m.nom_matiere_fr,
        c.nom_classe
      FROM Note n
      JOIN Examen e ON n.id_examen = e.id_examen
      JOIN Matiere m ON e.id_matiere = m.id_matiere
      JOIN Classe c ON e.id_classe = c.id_classe
      WHERE n.id_eleve = ?
      ORDER BY e.date_examen DESC
    `;

    const [notes] = await db.execute(query, [id]);

    // Formater les données
    const notesFormatees = notes.map(note => ({
      ...note,
      examen: {
        type_examen: note.type_examen,
        date_examen: note.date_examen,
        semestre: note.semestre,
        matiere: {
          nom_matiere_fr: note.nom_matiere_fr
        },
        classe: {
          nom_classe: note.nom_classe
        }
      }
    }));

    res.json({
      success: true,
      data: notesFormatees,
      message: 'Notes de l\'élève récupérées avec succès'
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des notes de l\'élève:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des notes de l\'élève',
      error: error.message
    });
  }
});

// POST /notes - Créer une nouvelle note
router.post('/', requireRole(['admin', 'enseignant']), async (req, res) => {
  try {
    const { id_eleve, id_examen, note } = req.body;

    // Validation des données
    if (!id_eleve || !id_examen || note === undefined || note === null) {
      return res.status(400).json({
        success: false,
        message: 'Tous les champs sont obligatoires'
      });
    }

    // Validation de la note (entre 0 et 20)
    if (note < 0 || note > 20) {
      return res.status(400).json({
        success: false,
        message: 'La note doit être comprise entre 0 et 20'
      });
    }

    // Vérifier que l'élève existe
    const [eleves] = await db.execute('SELECT id_eleve FROM Eleve WHERE id_eleve = ?', [id_eleve]);
    if (eleves.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Élève non trouvé'
      });
    }

    // Vérifier que l'examen existe
    const [examens] = await db.execute('SELECT id_examen FROM Examen WHERE id_examen = ?', [id_examen]);
    if (examens.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Examen non trouvé'
      });
    }

    // Vérifier qu'une note n'existe pas déjà pour cet élève et cet examen
    const [notesExistantes] = await db.execute(
      'SELECT id_eleve FROM Note WHERE id_eleve = ? AND id_examen = ?',
      [id_eleve, id_examen]
    );

    if (notesExistantes.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Une note existe déjà pour cet élève et cet examen'
      });
    }

    const query = 'INSERT INTO Note (id_eleve, id_examen, note) VALUES (?, ?, ?)';
    await db.execute(query, [id_eleve, id_examen, note]);

    res.status(201).json({
      success: true,
      message: 'Note créée avec succès'
    });

  } catch (error) {
    console.error('Erreur lors de la création de la note:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la création de la note',
      error: error.message
    });
  }
});

// PUT /notes/:id_eleve/:id_examen - Modifier une note
router.put('/:id_eleve/:id_examen', requireRole(['admin', 'enseignant']), async (req, res) => {
  try {
    const { id_eleve, id_examen } = req.params;
    const { note } = req.body;

    // Validation de la note
    if (note === undefined || note === null) {
      return res.status(400).json({
        success: false,
        message: 'La note est obligatoire'
      });
    }

    if (note < 0 || note > 20) {
      return res.status(400).json({
        success: false,
        message: 'La note doit être comprise entre 0 et 20'
      });
    }

    // Vérifier que la note existe
    const [notes] = await db.execute(
      'SELECT id_eleve FROM Note WHERE id_eleve = ? AND id_examen = ?',
      [id_eleve, id_examen]
    );

    if (notes.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Note non trouvée'
      });
    }

    const query = 'UPDATE Note SET note = ? WHERE id_eleve = ? AND id_examen = ?';
    await db.execute(query, [note, id_eleve, id_examen]);

    res.json({
      success: true,
      message: 'Note modifiée avec succès'
    });

  } catch (error) {
    console.error('Erreur lors de la modification de la note:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la modification de la note',
      error: error.message
    });
  }
});

// DELETE /notes/:id_eleve/:id_examen - Supprimer une note
router.delete('/:id_eleve/:id_examen', requireRole(['admin']), async (req, res) => {
  try {
    const { id_eleve, id_examen } = req.params;

    // Vérifier que la note existe
    const [notes] = await db.execute(
      'SELECT id_eleve FROM Note WHERE id_eleve = ? AND id_examen = ?',
      [id_eleve, id_examen]
    );

    if (notes.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Note non trouvée'
      });
    }

    await db.execute('DELETE FROM Note WHERE id_eleve = ? AND id_examen = ?', [id_eleve, id_examen]);

    res.json({
      success: true,
      message: 'Note supprimée avec succès'
    });

  } catch (error) {
    console.error('Erreur lors de la suppression de la note:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression de la note',
      error: error.message
    });
  }
});

module.exports = router;
