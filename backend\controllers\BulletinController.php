<?php
// controllers/BulletinController.php
require_once __DIR__ . '/../models/Bulletin.php';
require_once __DIR__ . '/../config/db.php';
require_once __DIR__ . '/../utils/auth.php';
require_once __DIR__ . '/../vendor/autoload.php'; // Pour TCPDF

use TCPDF;

class BulletinController {
    private $pdo;
    private $bulletin;

    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->bulletin = new Bulletin($pdo);
    }

    // Récupérer les données du bulletin d'un élève
    public function getBulletinData($id_eleve, $semestre) {
        try {
            $data = $this->bulletin->getBulletinData($id_eleve, $semestre);
            
            if (!$data) {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'message' => 'Aucune donnée trouvée pour ce bulletin'
                ]);
                return;
            }

            http_response_code(200);
            echo json_encode([
                'success' => true,
                'data' => $data,
                'message' => 'Données du bulletin récupérées avec succès'
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la récupération du bulletin',
                'error' => $e->getMessage()
            ]);
        }
    }

    // Générer et télécharger le bulletin en PDF
    public function generatePDF($id_eleve, $semestre) {
        try {
            // Récupérer les données du bulletin
            $bulletinData = $this->bulletin->getBulletinData($id_eleve, $semestre);
            
            if (!$bulletinData) {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'message' => 'Aucune donnée trouvée pour ce bulletin'
                ]);
                return;
            }

            // Créer le PDF
            $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
            
            // Configuration du PDF
            $pdf->SetCreator('ScolaNova');
            $pdf->SetAuthor('ScolaNova');
            $pdf->SetTitle('Bulletin de Notes - ' . $bulletinData['eleve']['prenom'] . ' ' . $bulletinData['eleve']['nom']);
            $pdf->SetSubject('Bulletin Scolaire');

            // Supprimer header/footer par défaut
            $pdf->setPrintHeader(false);
            $pdf->setPrintFooter(false);

            // Marges
            $pdf->SetMargins(15, 15, 15);
            $pdf->SetAutoPageBreak(TRUE, 15);

            // Ajouter une page
            $pdf->AddPage();

            // Générer le contenu HTML du bulletin
            $html = $this->generateBulletinHTML($bulletinData);
            
            // Écrire le HTML dans le PDF
            $pdf->writeHTML($html, true, false, true, false, '');

            // Nom du fichier
            $filename = 'Bulletin_' . $bulletinData['eleve']['nom'] . '_' . $bulletinData['eleve']['prenom'] . '_' . $semestre . '.pdf';

            // Envoyer le PDF au navigateur
            $pdf->Output($filename, 'D'); // 'D' pour téléchargement

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la génération du PDF',
                'error' => $e->getMessage()
            ]);
        }
    }

    // Générer le HTML du bulletin
    private function generateBulletinHTML($data) {
        $eleve = $data['eleve'];
        $notes = $data['notes'];
        $moyennes = $data['moyennes'];
        $semestre = $data['semestre'];
        $annee = $data['annee_scolaire'];

        $html = '
        <style>
            .header { text-align: center; margin-bottom: 20px; }
            .school-name { font-size: 18px; font-weight: bold; color: #2563eb; }
            .bulletin-title { font-size: 16px; font-weight: bold; margin: 10px 0; }
            .student-info { margin: 15px 0; }
            .info-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
            .info-table td { padding: 5px; border: 1px solid #ddd; }
            .notes-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
            .notes-table th, .notes-table td { padding: 8px; border: 1px solid #333; text-align: center; }
            .notes-table th { background-color: #f3f4f6; font-weight: bold; }
            .matiere { text-align: left !important; }
            .moyenne-generale { background-color: #e5f3ff; font-weight: bold; }
            .mention { text-align: center; margin: 15px 0; font-size: 14px; font-weight: bold; }
            .footer { margin-top: 30px; text-align: right; }
        </style>

        <div class="header">
            <div class="school-name">ÉCOLE SCOLANOVA</div>
            <div class="bulletin-title">BULLETIN DE NOTES - ' . strtoupper($semestre) . '</div>
            <div>Année Scolaire : ' . $annee['nom_annee'] . '</div>
        </div>

        <div class="student-info">
            <table class="info-table">
                <tr>
                    <td><strong>Nom :</strong> ' . strtoupper($eleve['nom']) . '</td>
                    <td><strong>Prénom :</strong> ' . ucfirst($eleve['prenom']) . '</td>
                </tr>
                <tr>
                    <td><strong>Classe :</strong> ' . $eleve['classe'] . '</td>
                    <td><strong>Code Massar :</strong> ' . $eleve['code_massar'] . '</td>
                </tr>
                <tr>
                    <td><strong>Date de naissance :</strong> ' . date('d/m/Y', strtotime($eleve['date_naissance'])) . '</td>
                    <td><strong>Lieu de naissance :</strong> ' . $eleve['lieu_naissance'] . '</td>
                </tr>
            </table>
        </div>

        <table class="notes-table">
            <thead>
                <tr>
                    <th rowspan="2" class="matiere">MATIÈRES</th>
                    <th colspan="3">NOTES</th>
                    <th rowspan="2">MOYENNE</th>
                    <th rowspan="2">APPRÉCIATION</th>
                </tr>
                <tr>
                    <th>Contrôle</th>
                    <th>Devoir</th>
                    <th>Examen</th>
                </tr>
            </thead>
            <tbody>';

        $totalCoefficients = 0;
        $totalPoints = 0;

        foreach ($notes as $matiere => $noteMatiere) {
            $html .= '<tr>';
            $html .= '<td class="matiere">' . $matiere . '</td>';
            $html .= '<td>' . ($noteMatiere['controle'] ?? '-') . '</td>';
            $html .= '<td>' . ($noteMatiere['devoir'] ?? '-') . '</td>';
            $html .= '<td>' . ($noteMatiere['examen'] ?? '-') . '</td>';
            $html .= '<td><strong>' . number_format($noteMatiere['moyenne'], 2) . '</strong></td>';
            $html .= '<td>' . $this->getAppreciation($noteMatiere['moyenne']) . '</td>';
            $html .= '</tr>';

            // Calcul pour moyenne générale (coefficient 1 pour chaque matière)
            $totalPoints += $noteMatiere['moyenne'];
            $totalCoefficients += 1;
        }

        $moyenneGenerale = $totalCoefficients > 0 ? $totalPoints / $totalCoefficients : 0;

        $html .= '
                <tr class="moyenne-generale">
                    <td colspan="4"><strong>MOYENNE GÉNÉRALE</strong></td>
                    <td><strong>' . number_format($moyenneGenerale, 2) . ' / 20</strong></td>
                    <td><strong>' . $this->getAppreciation($moyenneGenerale) . '</strong></td>
                </tr>
            </tbody>
        </table>

        <div class="mention">
            <strong>MENTION : ' . $this->getMention($moyenneGenerale) . '</strong>
        </div>

        <div class="footer">
            <p>Le Directeur</p>
            <br><br>
            <p>_____________________</p>
            <p><em>Généré le ' . date('d/m/Y à H:i') . '</em></p>
        </div>';

        return $html;
    }

    // Obtenir l'appréciation selon la note
    private function getAppreciation($note) {
        if ($note >= 18) return 'Excellent';
        if ($note >= 16) return 'Très bien';
        if ($note >= 14) return 'Bien';
        if ($note >= 12) return 'Assez bien';
        if ($note >= 10) return 'Passable';
        if ($note >= 8) return 'Insuffisant';
        return 'Très insuffisant';
    }

    // Obtenir la mention selon la moyenne générale
    private function getMention($moyenne) {
        if ($moyenne >= 18) return 'EXCELLENT';
        if ($moyenne >= 16) return 'TRÈS BIEN';
        if ($moyenne >= 14) return 'BIEN';
        if ($moyenne >= 12) return 'ASSEZ BIEN';
        if ($moyenne >= 10) return 'PASSABLE';
        return 'INSUFFISANT';
    }

    // Récupérer les bulletins d'une classe
    public function getBulletinsClasse($id_classe, $semestre) {
        try {
            $bulletins = $this->bulletin->getBulletinsClasse($id_classe, $semestre);
            
            http_response_code(200);
            echo json_encode([
                'success' => true,
                'data' => $bulletins,
                'message' => 'Bulletins de la classe récupérés avec succès'
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la récupération des bulletins',
                'error' => $e->getMessage()
            ]);
        }
    }
}
?>
