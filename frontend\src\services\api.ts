import axios from 'axios';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>e<PERSON><PERSON><PERSON>,
  Classe,
  Inscription,
  Matiere,
  ExamenSimple,
  NoteSimple
} from "../types";
import { extractErrorMessage, handleGlobalError, isAuthError, isNetworkError } from '../utils/errorUtils';

const API_BASE = "http://localhost/ScolaNova/backend/public/index.php";

// Création d'une instance Axios avec intercepteur
const api = axios.create({
  baseURL: API_BASE,
  // headers: { "Content-Type": "application/json" },
});

// Ajout automatique du token à chaque requête si présent
api.interceptors.request.use((config) => {
  const token = localStorage.getItem("token");
  if (token) {
    config.headers.Authorization = `Bearer ${token}`; // ajout du token
  }
  return config;
});

// Intercepteur de réponse pour gérer les erreurs globalement
api.interceptors.response.use(
  (response) => {
    // Retourner la réponse si tout va bien
    return response;
  },
  (error) => {
    // Gestion globale des erreurs
    console.log('🔍 Intercepteur d\'erreur activé:', error);

    // Erreurs de réseau
    if (isNetworkError(error)) {
      error.userMessage = 'Problème de connexion. Vérifiez votre connexion internet.';
      return Promise.reject(error);
    }

    // Erreurs d'authentification
    if (isAuthError(error)) {
      error.userMessage = 'Session expirée. Veuillez vous reconnecter.';
      // Optionnel: rediriger vers la page de connexion
      // window.location.href = '/login';
      return Promise.reject(error);
    }

    // Extraire le message d'erreur du backend
    const backendMessage = extractErrorMessage(error, 'Une erreur est survenue');
    error.userMessage = backendMessage;

    // Logger l'erreur pour le debugging
    console.group('🚨 Erreur API interceptée');
    console.error('URL:', error.config?.url);
    console.error('Méthode:', error.config?.method?.toUpperCase());
    console.error('Status:', error.response?.status);
    console.error('Message backend:', backendMessage);
    console.error('Erreur complète:', error);
    console.groupEnd();

    return Promise.reject(error);
  }
);


export const register = (data: User) => {
  try{
  return api.post("/register", data);
  }catch (error) {
    console.error("❌ Erreur API register:", error);
    throw error;
  }
};

export const login = (data: { email: string; password: string }) => {
  return api.post("/login", data);
};

export const changePassword = (data: { user_id: number; current_password: string; new_password: string }) => {
  try {
    return api.post("/change-password", data);
  } catch (error) {
    console.error("❌ Erreur API changePassword:", error);
    throw error;
  }
};

export const updateUser = async (id: number, data: Partial<User>) => {
  try {
    // Filtrer les données pour ne pas envoyer le mot de passe vide ou des champs non nécessaires
    const filteredData = {
      nom: data.nom,
      prenom: data.prenom,
      email: data.email,
      sexe: data.sexe,
      date_naissance: data.date_naissance,
      lieu_naissance: data.lieu_naissance,
      nationalite: data.nationalite,
      telephone: data.telephone,
      adresse: data.adresse,
      // Ne pas inclure le mot de passe dans la mise à jour
    };

    console.log(`🔄 Mise à jour utilisateur ${id}:`, filteredData);
    return await api.put(`/utilisateurs/${id}`, filteredData);
  } catch (error: any) {
    console.error(`❌ Erreur lors de la mise à jour de l'utilisateur ${id} :`, error);
    throw error;
  }
};
export const deleteUser = (id: number) => {
  try {
    return api.delete(`/utilisateurs/${id}`);

  } catch (error) {
    console.error(`❌ Erreur lors de la suppression de l'utilisateur  ${id} :`, error);
    throw error;
  }
};

export const getUtilisateurs = () => {
  try {
    console.log("🔍 Appel API getUtilisateurs (admins)");
    return api.get("/utilisateurs/admins");
  } catch (error) {
    console.error("❌ Erreur lors de la récupération des utilisateurs :", error);
    throw error;
  }
};

// ==================== COURS ====================

export const getCours = () => {
  try {
    console.log("🔍 Appel API getCours");
    return api.get("/cours");
  } catch (error) {
    console.error("❌ Erreur lors de la récupération des cours :", error);
    throw error;
  }
};

export const getCoursByClasse = (id_classe: number) => {
  try {
    console.log(`🔍 Appel API getCoursByClasse pour classe ${id_classe}`);
    return api.get(`/cours/classe/${id_classe}`);
  } catch (error) {
    console.error("❌ Erreur lors de la récupération des cours par classe :", error);
    throw error;
  }
};

export const getCoursByEnseignant = (id_enseignant: number) => {
  try {
    console.log(`🔍 Appel API getCoursByEnseignant pour enseignant ${id_enseignant}`);
    return api.get(`/cours/enseignant/${id_enseignant}`);
  } catch (error) {
    console.error("❌ Erreur lors de la récupération des cours par enseignant :", error);
    throw error;
  }
};

export const addCours = (coursData: any) => {
  try {
    console.log("➕ Appel API addCours avec données :", coursData);
    return api.post("/cours", coursData);
  } catch (error) {
    console.error("❌ Erreur lors de l'ajout du cours :", error);
    throw error;
  }
};

export const updateCours = (id: number, coursData: any) => {
  try {
    console.log(`✏️ Appel API updateCours pour cours ${id} avec données :`, coursData);
    return api.put(`/cours/${id}`, coursData);
  } catch (error) {
    console.error("❌ Erreur lors de la mise à jour du cours :", error);
    throw error;
  }
};

export const deleteCours = (id: number) => {
  try {
    console.log(`🗑️ Appel API deleteCours pour cours ${id}`);
    return api.delete(`/cours/${id}`);
  } catch (error) {
    console.error("❌ Erreur lors de la suppression du cours :", error);
    throw error;
  }
};

// ==================== UNITES ====================

export const getUnites = () => {
  try {
    console.log("🔍 Appel API getUnites");
    return api.get("/unites");
  } catch (error) {
    console.error("❌ Erreur lors de la récupération des unités :", error);
    throw error;
  }
};

// ==================== EMPLOI DU TEMPS ELEVE ====================

export const getEmploiTempsEleve = (eleveId: number) => {
  try {
    console.log(`🔍 Appel API getEmploiTempsEleve pour élève ${eleveId}`);
    return api.get(`/emploi-temps/eleve/${eleveId}`);
  } catch (error) {
    console.error("❌ Erreur lors de la récupération de l'emploi du temps :", error);
    throw error;
  }
};

export const getCoursAujourdhui = (eleveId: number) => {
  try {
    console.log(`🔍 Appel API getCoursAujourdhui pour élève ${eleveId}`);
    const today = new Date().toISOString().split('T')[0]; // Format YYYY-MM-DD
    return api.get(`/cours/eleve/${eleveId}/jour/${today}`);
  } catch (error) {
    console.error("❌ Erreur lors de la récupération des cours d'aujourd'hui :", error);
    throw error;
  }
};
export const getEleves = (id_annee: number | null = null) => {
    const url = id_annee ? `/eleves/annee/${id_annee}` : `/eleves`;
    return api.get(url);
};

export const getEleve = (id: number) => {
  try {
    return api.get(`/eleves/${id}`);
  } catch (error) {
    console.error(`❌ Erreur lors de la récupération de l'élève ${id} :`, error);
    throw error;
  }
};
export const addEleve = (data: Eleve) => {
  try {
    return api.post("/eleves", data);

  } catch (error) {
    console.error("❌ Erreur lors de l'ajout de l'élève :", error);
    throw error;
  }
};

export const updateEleve = (id: number, data: Eleve) => {
  try {
    return api.put(`/eleves/${id}`, data);

  } catch (error) {
    console.error(`❌ Erreur lors de la mise à jour de l'élève ${id} :`, error);
    throw error;
  }
};
export const deleteEleve = (id: number) => {
  try {
    return api.delete(`/eleves/${id}`);

  } catch (error) {
    console.error(`❌ Erreur lors de la suppression de l'élève ${id} :`, error);
    throw error;
  }
};

export const getParents = () => {
  return api.get("/parents");
};

export const getParent = (id: number) => {
try {
  return api.get(`/parents/${id}`);
} catch (error) {
  console.error(`❌ Erreur lors de la récupération de l'élève ${id} :`, error);
  throw error;
}
};
export const addParent = (data: { id_utilisateur: number, nom_ar: string, prenom_ar: string, num_CIN: string,}) => {
try {
  return api.post("/parents", data);
} catch (error) {
  console.error("❌ Erreur lors de l'ajout du parent :", error);
  throw error;
}
};

export const updateParent = async (
  id: number,
  data: Pick<Parent, "nom_ar" | "prenom_ar" | "num_CIN">
) => {
  try {
    return await api.put(`/parents/${id}`, {
      nom_ar: data.nom_ar,
      prenom_ar: data.prenom_ar,
      num_CIN: data.num_CIN,
    });
  } catch (error: any) {
    console.error(`❌ Erreur lors de la mise à jour du parent ${id} :`, error);
    throw error;
  }
};

export const deleteParent = (id: number) => {
try {
  return api.delete(`/parents/${id}`);
} catch (error) {
  console.error(`❌ Erreur lors de la suppression de parent ${id} :`, error);
  throw error;
}
};

export const searchParentByCIN = (cin: string) => {
try {
  console.log(`🔍 Recherche parent par CIN: ${cin}`);
  return api.get(`/parents/search/cin/${cin}`);
} catch (error) {
  console.error(`❌ Erreur lors de la recherche du parent par CIN ${cin} :`, error);
  throw error;
}
};

export const addRelation = (data: Relation) => {
  try {
    return api.post("/parents/relation", data);

  } catch (error) {
    console.error("❌ Erreur lors de l'ajout de l'élève :", error);
    throw error;
  }
  };

export const getParentsByEleve = (id_eleve: number) => {
  try {
    return api.get(`/parents/eleve/${id_eleve}`);
  } catch (error) {
    console.error(`❌ Erreur lors de la récupération des parents de l'élève ${id_eleve} :`, error);
    throw error;
  }
};

export const updateRelation = (data: Relation) => {
  try {
    return api.put("/parents/relation", data);
  } catch (error) {
    console.error("❌ Erreur lors de la mise à jour de la relation :", error);
    throw error;
  }
};

// Anciennes écoles
export const getAnciennesEcoles = () => {
  try {
    return api.get("/anciennes-ecoles");
  } catch (error) {
    console.error("❌ Erreur lors de la récupération des anciennes écoles :", error);
    throw error;
  }
};

export const getAncienneEcole = (code_gresa: string) => {
  try {
    return api.get(`/anciennes-ecoles/${code_gresa}`);
  } catch (error) {
    console.error(`❌ Erreur lors de la récupération de l'ancienne école ${code_gresa} :`, error);
    throw error;
  }
};

export const addAncienneEcole = (data: any) => {
  try {
    return api.post("/anciennes-ecoles", data);
  } catch (error) {
    console.error("❌ Erreur lors de l'ajout de l'ancienne école :", error);
    throw error;
  }
};

export const updateAncienneEcole = (code_gresa: string, data: any) => {
  try {
    return api.put(`/anciennes-ecoles/${code_gresa}`, data);
  } catch (error) {
    console.error(`❌ Erreur lors de la mise à jour de l'ancienne école ${code_gresa} :`, error);
    throw error;
  }
};

export const deleteAncienneEcole = (code_gresa: string) => {
  try {
    return api.delete(`/anciennes-ecoles/${code_gresa}`);
  } catch (error) {
    console.error(`❌ Erreur lors de la suppression de l'ancienne école ${code_gresa} :`, error);
    throw error;
  }
};

// Niveaux
export const getNiveaux = () => {
  try {
    return api.get("/niveaux");
  } catch (error) {
    console.error("❌ Erreur lors de la récupération des niveaux :", error);
    throw error;
  }
};

export const getNiveau = (id: number) => {
  try {
    return api.get(`/niveaux/${id}`);
  } catch (error) {
    console.error(`❌ Erreur lors de la récupération du niveau ${id} :`, error);
    throw error;
  }
};

export const getNiveauxByCycle = (cycle: string) => {
  try {
    return api.get(`/niveaux/cycle/${cycle}`);
  } catch (error) {
    console.error(`❌ Erreur lors de la récupération des niveaux du cycle ${cycle} :`, error);
    throw error;
  }
};

export const addNiveau = (data: any) => {
  try {
    return api.post("/niveaux", data);
  } catch (error) {
    console.error("❌ Erreur lors de l'ajout du niveau :", error);
    throw error;
  }
};

export const updateNiveau = (id: number, data: any) => {
  try {
    return api.put(`/niveaux/${id}`, data);
  } catch (error) {
    console.error(`❌ Erreur lors de la mise à jour du niveau ${id} :`, error);
    throw error;
  }
};

export const deleteNiveau = (id: number) => {
  try {
    return api.delete(`/niveaux/${id}`);
  } catch (error) {
    console.error(`❌ Erreur lors de la suppression du niveau ${id} :`, error);
    throw error;
  }
};



// Années scolaires
export const getAnneesScolaires = () => {
  try {
    return api.get("/annees-scolaires");
  } catch (error) {
    console.error("❌ Erreur lors de la récupération des années scolaires :", error);
    throw error;
  }
};

export const getAnneeScolaire = (id: number) => {
  try {
    return api.get(`/annees-scolaires/${id}`);
  } catch (error) {
    console.error(`❌ Erreur lors de la récupération de l'année scolaire ${id} :`, error);
    throw error;
  }
};

export const getAnneeScolaireActive = () => {
  try {
    return api.get("/annees-scolaires/active");
  } catch (error) {
    console.error("❌ Erreur lors de la récupération de l'année scolaire active :", error);
    throw error;
  }
};

export const addAnneeScolaire = (data: any) => {
  try {
    return api.post("/annees-scolaires", data);
  } catch (error) {
    console.error("❌ Erreur lors de l'ajout de l'année scolaire :", error);
    throw error;
  }
};

export const updateAnneeScolaire = (id: number, data: any) => {
  try {
    return api.put(`/annees-scolaires/${id}`, data);
  } catch (error) {
    console.error(`❌ Erreur lors de la mise à jour de l'année scolaire ${id} :`, error);
    throw error;
  }
};

export const deleteAnneeScolaire = (id: number) => {
  try {
    return api.delete(`/annees-scolaires/${id}`);
  } catch (error) {
    console.error(`❌ Erreur lors de la suppression de l'année scolaire ${id} :`, error);
    throw error;
  }
};

export const toggleAnneeScolaireActive = (id: number) => {
  try {
    return api.patch(`/annees-scolaires/${id}/toggle-active`);
  } catch (error) {
    console.error(`❌ Erreur lors du changement de statut de l'année scolaire ${id} :`, error);
    throw error;
  }
};

export const getAnneesScolairesStatistics = () => {
  try {
    return api.get("/annees-scolaires/statistics");
  } catch (error) {
    console.error("❌ Erreur lors de la récupération des statistiques des années scolaires :", error);
    throw error;
  }
};

export const validateAnneeScolaireDates = (data: any) => {
  try {
    return api.post("/annees-scolaires/validate-dates", data);
  } catch (error) {
    console.error("❌ Erreur lors de la validation des dates :", error);
    throw error;
  }
};

// Inscriptions

export const getInscriptionByEleve = (id_eleve: number) => api.get(`/inscriptions/eleve/${id_eleve}`);

export const addInscription = (data: { id_eleve: number; id_annee_scolaire: number; id_classe: number }) => {
  try {
    return api.post("/inscriptions", data);
  } catch (error) {
    console.error("❌ Erreur lors de l'ajout de l'inscription :", error);
    throw error;
  }
};

export const updateInscription = (data: { id_eleve: number; id_annee_scolaire: number; old_id_classe: number; new_id_classe: number }) => {
  try {
    return api.put(`/inscriptions/${data.id_eleve}`, data); // Assuming the PUT route uses eleve ID for update
  } catch (error) {
    console.error("❌ Erreur lors de la mise à jour de l'inscription :", error);
    throw error;
  }
};

// Classes
export const getClasses = () => {
  try {
    return api.get("/classes");
  } catch (error) {
    console.error("❌ Erreur lors de la récupération des classes :", error);
    throw error;
  }
};

export const getClasse = (id: number) => {
  try {
    return api.get(`/classes/${id}`);
  } catch (error) {
    console.error(`❌ Erreur lors de la récupération de la classe ${id} :`, error);
    throw error;
  }
};

export const getClassesByNiveau = (idNiveau: number) => {
  try {
    return api.get(`/classes/niveau/${idNiveau}`);
  } catch (error) {
    console.error(`❌ Erreur lors de la récupération des classes du niveau ${idNiveau} :`, error);
    throw error;
  }
};

export const getClasseEleves = (idClasse: number) => {
  try {
    console.log("🔍 Appel API getClasseEleves - ID classe:", idClasse);
    return api.get(`/classes/${idClasse}/eleves`);
  } catch (error) {
    console.error(`❌ Erreur lors de la récupération des élèves de la classe ${idClasse} :`, error);
    throw error;
  }
};
// export const getClasseEleves = (id: number) => {
//   try {
//     return api.get(`/classes/${id}/eleves`);
//   } catch (error) {
//     console.error(`❌ Erreur lors de la récupération des élèves de la classe ${id} :`, error);
//     throw error;
//   }
// };

export const addClasse = (data: any) => {
  try {
    return api.post("/classes", data);
  } catch (error) {
    console.error("❌ Erreur lors de l'ajout de la classe :", error);
    throw error;
  }
};

export const updateClasse = (id: number, data: any) => {
  try {
    return api.put(`/classes/${id}`, data);
  } catch (error) {
    console.error(`❌ Erreur lors de la mise à jour de la classe ${id} :`, error);
    throw error;
  }
};

export const deleteClasse = (id: number) => {
  try {
    return api.delete(`/classes/${id}`);
  } catch (error) {
    console.error(`❌ Erreur lors de la suppression de la classe ${id} :`, error);
    throw error;
  }
};



// Activités
export const getActivites = () => {
  try {
    // TODO: Implémenter l'API des activités côté backend
    // Pour l'instant, retourner des données vides pour éviter l'erreur
    return Promise.resolve({
      data: {
        success: true,
        data: []
      }
    });
  } catch (error) {
    console.error("❌ Erreur lors de la récupération des activités :", error);
    throw error;
  }
};

// ==================================== Gestions des enseignant ====================================

export const getEnseignants = () => {
  return api.get("/enseignants");
};
export const getEnseignant = (id: number) => {
try {
  return api.get(`/enseignants/${id}`);
} catch (error) {
  console.error(`❌ Erreur lors de la récupération de l'enseignant ${id} :`, error);
  throw error;
}
};
export const addEnseignant = (data: Partial<Enseignant>) => {
try {
  return api.post("/enseignants", data);
} catch (error) {
  console.error("❌ Erreur lors de l'ajout de l'enseignant :", error);
  throw error;
}
};

export const updateEnseignant = (id: number, data: Enseignant) => {
try {
  return api.put(`/enseignants/${id}`, data);
} catch (error) {
  console.error(`❌ Erreur lors de la mise à jour de l'enseignant ${id} :`, error);
  throw error;
}
};
export const deleteEnseignant = (id: number) => {
try {
  return api.delete(`/enseignants/${id}`);
} catch (error) {
  console.error(`❌ Erreur lors de la suppression de l'enseignant ${id} :`, error);
  throw error;
}
};

// Récupérer les élèves d'un enseignant
export const getElevesEnseignant = (id_enseignant: number) => {
  try {
    console.log("🔍 Appel API getElevesEnseignant - ID:", id_enseignant);
    return api.get(`/enseignants/${id_enseignant}/eleves`);
  } catch (error) {
    console.error(`❌ Erreur lors de la récupération des élèves de l'enseignant ${id_enseignant} :`, error);
    throw error;
  }
};

// Récupérer les classes d'un enseignant
export const getClassesEnseignant = (id_enseignant: number) => {
  try {
    console.log("🔍 Appel API getClassesEnseignant - ID:", id_enseignant);
    return api.get(`/enseignants/${id_enseignant}/classes`);
  } catch (error) {
    console.error(`❌ Erreur lors de la récupération des classes de l'enseignant ${id_enseignant} :`, error);
    throw error;
  }
};

// Récupérer les cours d'aujourd'hui d'un enseignant
export const getCoursAujourdhuiEnseignant = (id_enseignant: number) => {
  try {
    console.log("🔍 Appel API getCoursAujourdhuiEnseignant - ID:", id_enseignant);
    return api.get(`/enseignants/${id_enseignant}/cours-aujourdhui`);
  } catch (error) {
    console.error(`❌ Erreur lors de la récupération des cours d'aujourd'hui de l'enseignant ${id_enseignant} :`, error);
    throw error;
  }
};
export const getDiplomes = () => {
  return api.get("/diplomes");
};

export const getDiplome = (id: number) => {
try {
  return api.get(`/diplomes/${id}`);
} catch (error) {
  console.error(`❌ Erreur lors de la récupération de l'Diplome ${id} :`, error);
  throw error;
}
};
export const addDiplome = (data: Partial<Diplome>) => {
try {
  return api.post("/diplomes", data);
} catch (error) {
  console.error("❌ Erreur lors de l'ajout de l'diplome :", error);
  throw error;
}
};

export const updateDiplome = (id: number, data: Diplome) => {
try {
  return api.put(`/diplomes/${id}`, data);
} catch (error) {
  console.error(`❌ Erreur lors de la mise à jour de l'diplome ${id} :`, error);
  throw error;
}
};
export const deleteDiplome = (id: number) => {
try {
  return api.delete(`/diplomes/${id}`);
} catch (error) {
  console.error(`❌ Erreur lors de la suppression de l'diplome ${id} :`, error);
  throw error;
}
};

export const getDiplomesByEnseignant = (id_enseignant: number) => {
try {
  const url = `/enseignants/${id_enseignant}/diplomes`;
  console.log(`🔍 Appel API getDiplomesByEnseignant - URL: ${url}`);
  return api.get(url);
} catch (error) {
  console.error(`❌ Erreur lors de la récupération des diplômes de l'enseignant ${id_enseignant} :`, error);
  throw error;
}
};
export const getContrats = () => {
  return api.get("/contrats");
};

export const getContrat = (id: number) => {
try {
  return api.get(`/contrats/${id}`);
} catch (error) {
  console.error(`❌ Erreur lors de la récupération de l'contrat ${id} :`, error);
  throw error;
}
};
export const addContrat = (data: Partial<Contrat>) => {
try {
  return api.post("/contrats", data);
} catch (error) {
  console.error("❌ Erreur lors de l'ajout de l'contrat :", error);
  throw error;
}
};

export const updateContrat = (id: number, data: Contrat) => {
try {
  return api.put(`/contrats/${id}`, data);
} catch (error) {
  console.error(`❌ Erreur lors de la mise à jour de l'contrat ${id} :`, error);
  throw error;
}
};
export const deleteContrat = (id: number) => {
try {
  return api.delete(`/contrats/${id}`);
} catch (error) {
  console.error(`❌ Erreur lors de la suppression de l'contrat ${id} :`, error);
  throw error;
}
};

export const getActiveContratByEnseignant = (id_enseignant: number) => {
try {
  const url = `/enseignants/${id_enseignant}/contrats/active`;
  console.log(`🔍 Appel API getActiveContratByEnseignant - URL: ${url}`);
  return api.get(url);
} catch (error) {
  console.error(`❌ Erreur lors de la récupération du contrat actif de l'enseignant ${id_enseignant} :`, error);
  throw error;
}
};

//=================================== Tableau de bord =====================================
export const getStatistiquesGenerales = () => {
  try {
    return api.get("/dashboard/stats");
    } catch (error) {
      console.error("❌ Erreur lors de la récupération des statistiques générales :",
        error);
        throw error;
        }
        };

//=================================== Tableau de bord =====================================
export const getSalles = () => api.get("/salles");
export const getSalleById = (id: number) => api.get(`/salles/${id}`);
export const addSalle = (data: { nom_salle: string; capacite: number }) => api.post("/salles", data);
export const updateSalle = (id: number, data: { nom_salle: string; capacite: number }) => api.put(`/salles/${id}`, data);
export const deleteSalle = (id: number) => api.delete(`/salles/${id}`);
export const getElevesByParent = () => {
  return api.get("/eleves/mes-enfants");
};
export const getIdEleveByUtilisateur = (id_utilisateur: number) => {
  return api.get(`/eleves/id-eleve-by-utilisateur/${id_utilisateur}`);
};

// ==================================== Gestion des matières ====================================
export const getMatieres = () => {
  try {
    console.log("🔍 Appel API getMatieres");
    return api.get("/matieres");
  } catch (error) {
    console.error("❌ Erreur lors de la récupération des matières :", error);
    throw error;
  }
};

export const getMatiere = (id: number) => {
  try {
    console.log("🔍 Appel API getMatiere - ID:", id);
    return api.get(`/matieres/${id}`);
  } catch (error) {
    console.error(`❌ Erreur lors de la récupération de la matière ${id} :`, error);
    throw error;
  }
};

export const addMatiere = (matiere: Partial<Matiere>) => {
  try {
    console.log("➕ Appel API addMatiere - Données:", matiere);
    return api.post("/matieres", matiere);
  } catch (error) {
    console.error("❌ Erreur lors de l'ajout de la matière :", error);
    throw error;
  }
};

export const updateMatiere = (id: number, matiere: Partial<Matiere>) => {
  try {
    console.log("🔄 Appel API updateMatiere - ID:", id, "Données:", matiere);
    return api.put(`/matieres/${id}`, matiere);
  } catch (error) {
    console.error(`❌ Erreur lors de la mise à jour de la matière ${id} :`, error);
    throw error;
  }
};

export const deleteMatiere = (id: number) => {
  try {
    console.log("🗑️ Appel API deleteMatiere - ID:", id);
    return api.delete(`/matieres/${id}`);
  } catch (error) {
    console.error(`❌ Erreur lors de la suppression de la matière ${id} :`, error);
    throw error;
  }
};

// ==================================== Gestion des affectations enseignant-matière ====================================
export const getMatieresEnseignant = (id_enseignant: number) => {
  try {
    console.log("🔍 Appel API getMatieresEnseignant - ID:", id_enseignant);
    return api.get(`/enseignants/${id_enseignant}/matieres`);
  } catch (error) {
    console.error(`❌ Erreur lors de la récupération des matières de l'enseignant ${id_enseignant} :`, error);
    throw error;
  }
};

// ==================================== Gestion des paiements ====================================
export const getPaiements = () => {
  try {
    console.log("🔍 Appel API getPaiements");
    return api.get("/paiements");
  } catch (error) {
    console.error("❌ Erreur lors de la récupération des paiements :", error);
    throw error;
  }
};

export const getPaiement = (id: number) => {
  try {
    console.log("🔍 Appel API getPaiement - ID:", id);
    return api.get(`/paiements/${id}`);
  } catch (error) {
    console.error(`❌ Erreur lors de la récupération du paiement ${id} :`, error);
    throw error;
  }
};

export const addPaiement = (paiementData: any) => {
  try {
    console.log("➕ Appel API addPaiement - Données:", paiementData);
    return api.post("/paiements", paiementData);
  } catch (error) {
    console.error("❌ Erreur lors de l'ajout du paiement :", error);
    throw error;
  }
};

export const updatePaiement = (id: number, paiementData: any) => {
  try {
    console.log("✏️ Appel API updatePaiement - ID:", id, "Données:", paiementData);
    return api.put(`/paiements/${id}`, paiementData);
  } catch (error) {
    console.error(`❌ Erreur lors de la modification du paiement ${id} :`, error);
    throw error;
  }
};

export const deletePaiement = (id: number) => {
  try {
    console.log("🗑️ Appel API deletePaiement - ID:", id);
    return api.delete(`/paiements/${id}`);
  } catch (error) {
    console.error(`❌ Erreur lors de la suppression du paiement ${id} :`, error);
    throw error;
  }
};

export const getPaiementsByEleve = (id_eleve: number) => {
  try {
    console.log("🔍 Appel API getPaiementsByEleve - ID élève:", id_eleve);
    return api.get(`/paiements/eleve/${id_eleve}`);
  } catch (error) {
    console.error(`❌ Erreur lors de la récupération des paiements de l'élève ${id_eleve} :`, error);
    throw error;
  }
};

export const getStatistiquesPaiements = (anneeId?: number) => {
  try {
    console.log("🔍 Appel API getStatistiquesPaiements", anneeId ? `pour l'année ${anneeId}` : "");
    const url = anneeId ? `/paiements/statistiques?annee_scolaire=${anneeId}` : "/paiements/statistiques";
    return api.get(url);
  } catch (error) {
    console.error("❌ Erreur lors de la récupération des statistiques de paiements :", error);
    throw error;
  }
};

export const getPaiementsEnRetard = () => {
  try {
    console.log("🔍 Appel API getPaiementsEnRetard");
    return api.get("/paiements/retards");
  } catch (error) {
    console.error("❌ Erreur lors de la récupération des paiements en retard :", error);
    throw error;
  }
};

export const genererEcheancier = (eleveId: number, anneeId: number, typeData: any) => {
  try {
    console.log("📅 Appel API genererEcheancier - Élève:", eleveId, "Année:", anneeId, "Type:", typeData);
    return api.post(`/paiements/echeancier`, { id_eleve: eleveId, id_annee_scolaire: anneeId, ...typeData });
  } catch (error) {
    console.error("❌ Erreur lors de la génération de l'échéancier :", error);
    throw error;
  }
};

export const genererPaiementsMensuels = (mois?: string, annee?: number, typePaiement?: string) => {
  try {
    console.log("📅 Appel API genererPaiementsMensuels - Mois:", mois, "Année:", annee, "Type:", typePaiement);
    return api.post(`/paiements/generer-mensuels`, {
      mois: mois || new Date().toLocaleString('fr-FR', { month: 'long' }),
      annee: annee || new Date().getFullYear(),
      type_paiement: typePaiement || 'scolarité'
    });
  } catch (error) {
    console.error("❌ Erreur lors de la génération des paiements mensuels :", error);
    throw error;
  }
};

export const verifierPaiementsMensuels = (mois?: string, annee?: number) => {
  try {
    console.log("🔍 Appel API verifierPaiementsMensuels - Mois:", mois, "Année:", annee);
    return api.get(`/paiements/verifier-mensuels`, {
      params: {
        mois: mois || new Date().toLocaleString('fr-FR', { month: 'long' }),
        annee: annee || new Date().getFullYear()
      }
    });
  } catch (error) {
    console.error("❌ Erreur lors de la vérification des paiements mensuels :", error);
    throw error;
  }
};

export const getNiveauEleve = (id_eleve: number) => {
  try {
    console.log("🔍 Appel API getNiveauEleve - ID élève:", id_eleve);
    return api.get(`/eleves/${id_eleve}/niveau`);
  } catch (error) {
    console.error(`❌ Erreur lors de la récupération du niveau de l'élève ${id_eleve} :`, error);
    throw error;
  }
};





export const assignMatieresEnseignant = (id_enseignant: number, matieres: number[]) => {
  try {
    console.log("➕ Appel API assignMatieresEnseignant - Enseignant:", id_enseignant, "Matières:", matieres);
    return api.post(`/enseignants/${id_enseignant}/matieres`, { matieres });
  } catch (error) {
    console.error(`❌ Erreur lors de l'affectation des matières à l'enseignant ${id_enseignant} :`, error);
    throw error;
  }
};

export const updateMatieresEnseignant = (id_enseignant: number, matieres: number[]) => {
  try {
    console.log("🔄 Appel API updateMatieresEnseignant - Enseignant:", id_enseignant, "Matières:", matieres);
    return api.put(`/enseignants/${id_enseignant}/matieres`, { matieres });
  } catch (error) {
    console.error(`❌ Erreur lors de la mise à jour des matières de l'enseignant ${id_enseignant} :`, error);
    throw error;
  }
};

// ==================== EXAMENS ====================

// Examens
export const getExamens = () => {
  try {
    console.log("🔍 Appel API getExamens");
    return api.get("/examens");
  } catch (error) {
    console.error("❌ Erreur lors de la récupération des examens :", error);
    throw error;
  }
};

export const getExamen = (id: number) => {
  try {
    console.log("🔍 Appel API getExamen - ID:", id);
    return api.get(`/examens/${id}`);
  } catch (error) {
    console.error(`❌ Erreur lors de la récupération de l'examen ${id} :`, error);
    throw error;
  }
};

export const addExamen = (examenData: any) => {
  try {
    console.log("➕ Appel API addExamen - Données:", examenData);
    return api.post("/examens", examenData);
  } catch (error) {
    console.error("❌ Erreur lors de l'ajout de l'examen :", error);
    throw error;
  }
};

export const updateExamen = (id: number, examenData: any) => {
  try {
    console.log("✏️ Appel API updateExamen - ID:", id, "Données:", examenData);
    return api.put(`/examens/${id}`, examenData);
  } catch (error) {
    console.error(`❌ Erreur lors de la modification de l'examen ${id} :`, error);
    throw error;
  }
};

export const deleteExamen = (id: number) => {
  try {
    console.log("🗑️ Appel API deleteExamen - ID:", id);
    return api.delete(`/examens/${id}`);
  } catch (error) {
    console.error(`❌ Erreur lors de la suppression de l'examen ${id} :`, error);
    throw error;
  }
};

// Notes
export const getNotes = () => {
  try {
    console.log("🔍 Appel API getNotes");
    return api.get("/notes");
  } catch (error) {
    console.error("❌ Erreur lors de la récupération des notes :", error);
    throw error;
  }
};

export const getNotesExamen = (idExamen: number) => {
  try {
    console.log("🔍 Appel API getNotesExamen - ID examen:", idExamen);
    return api.get(`/examens/${idExamen}/notes`);
  } catch (error) {
    console.error(`❌ Erreur lors de la récupération des notes de l'examen ${idExamen} :`, error);
    throw error;
  }
};

export const getNotesEleve = (idEleve: number) => {
  try {
    console.log("🔍 Appel API getNotesEleve - ID élève:", idEleve);
    return api.get(`/eleves/${idEleve}/notes`);
  } catch (error) {
    console.error(`❌ Erreur lors de la récupération des notes de l'élève ${idEleve} :`, error);
    throw error;
  }
};

export const addNote = (noteData: any) => {
  try {
    console.log("➕ Appel API addNote - Données:", noteData);
    return api.post("/notes", noteData);
  } catch (error) {
    console.error("❌ Erreur lors de l'ajout de la note :", error);
    throw error;
  }
};

export const updateNote = (idEleve: number, idExamen: number, noteData: any) => {
  try {
    console.log("✏️ Appel API updateNote - Élève:", idEleve, "Examen:", idExamen, "Données:", noteData);
    return api.put(`/notes/${idEleve}/${idExamen}`, noteData);
  } catch (error) {
    console.error(`❌ Erreur lors de la modification de la note (élève ${idEleve}, examen ${idExamen}) :`, error);
    throw error;
  }
};

export const deleteNote = (idEleve: number, idExamen: number) => {
  try {
    console.log("🗑️ Appel API deleteNote - Élève:", idEleve, "Examen:", idExamen);
    return api.delete(`/notes/${idEleve}/${idExamen}`);
  } catch (error) {
    console.error(`❌ Erreur lors de la suppression de la note (élève ${idEleve}, examen ${idExamen}) :`, error);
    throw error;
  }
};