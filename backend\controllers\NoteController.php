<?php
// controllers/NoteController.php
require_once __DIR__ . '/../models/Note.php';
require_once __DIR__ . '/../config/db.php';
require_once __DIR__ . '/../utils/auth.php';

class NoteController {
    private $pdo;
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    public function getAll() {
        try {
            $query = "
                SELECT
                    n.*,
                    u.nom as nom_eleve,
                    u.prenom as prenom_eleve,
                    el.code_massar,
                    e.type_examen,
                    e.date_examen,
                    m.nom_matiere_fr,
                    c.nom_classe
                FROM Note n
                JOIN Eleve el ON n.id_eleve = el.id_eleve
                JOIN Utilisateur u ON el.id_utilisateur = u.id_utilisateur
                JOIN Examen e ON n.id_examen = e.id_examen
                JOIN Matiere m ON e.id_matiere = m.id_matiere
                JOIN Classe c ON e.id_classe = c.id_classe
                ORDER BY e.date_examen DESC, u.nom, u.prenom
            ";

            $stmt = $this->pdo->prepare($query);
            $stmt->execute();
            $notes = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Formater les données
            $notesFormatees = array_map(function($note) {
                return [
                    'id_eleve' => (int)$note['id_eleve'],
                    'id_examen' => (int)$note['id_examen'],
                    'note' => (float)$note['note'],
                    'eleve' => [
                        'nom' => $note['nom_eleve'],
                        'prenom' => $note['prenom_eleve'],
                        'code_massar' => $note['code_massar']
                    ],
                    'examen' => [
                        'type_examen' => $note['type_examen'],
                        'date_examen' => $note['date_examen'],
                        'matiere' => [
                            'nom_matiere_fr' => $note['nom_matiere_fr']
                        ],
                        'classe' => [
                            'nom_classe' => $note['nom_classe']
                        ]
                    ]
                ];
            }, $notes);

            http_response_code(200);
            echo json_encode([
                'success' => true,
                'data' => $notesFormatees,
                'message' => 'Notes récupérées avec succès'
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la récupération des notes',
                'error' => $e->getMessage()
            ]);
        }
    }

    public function getByExamen($id_examen) {
        try {
            $query = "
                SELECT
                    n.*,
                    u.nom as nom_eleve,
                    u.prenom as prenom_eleve,
                    el.code_massar
                FROM Note n
                JOIN Eleve el ON n.id_eleve = el.id_eleve
                JOIN Utilisateur u ON el.id_utilisateur = u.id_utilisateur
                WHERE n.id_examen = ?
                ORDER BY u.nom, u.prenom
            ";

            $stmt = $this->pdo->prepare($query);
            $stmt->execute([$id_examen]);
            $notes = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Formater les données
            $notesFormatees = array_map(function($note) {
                return [
                    'id_eleve' => (int)$note['id_eleve'],
                    'id_examen' => (int)$note['id_examen'],
                    'note' => (float)$note['note'],
                    'eleve' => [
                        'nom' => $note['nom_eleve'],
                        'prenom' => $note['prenom_eleve'],
                        'code_massar' => $note['code_massar']
                    ]
                ];
            }, $notes);

            http_response_code(200);
            echo json_encode([
                'success' => true,
                'data' => $notesFormatees,
                'message' => 'Notes de l\'examen récupérées avec succès'
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la récupération des notes de l\'examen',
                'error' => $e->getMessage()
            ]);
        }
    }

    public function getByEleve($id_eleve) {
        try {
            $query = "
                SELECT
                    n.*,
                    e.type_examen,
                    e.date_examen,
                    e.semestre,
                    m.nom_matiere_fr,
                    c.nom_classe
                FROM Note n
                JOIN Examen e ON n.id_examen = e.id_examen
                JOIN Matiere m ON e.id_matiere = m.id_matiere
                JOIN Classe c ON e.id_classe = c.id_classe
                WHERE n.id_eleve = ?
                ORDER BY e.date_examen DESC
            ";

            $stmt = $this->pdo->prepare($query);
            $stmt->execute([$id_eleve]);
            $notes = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Formater les données
            $notesFormatees = array_map(function($note) {
                return [
                    'id_eleve' => (int)$note['id_eleve'],
                    'id_examen' => (int)$note['id_examen'],
                    'note' => (float)$note['note'],
                    'examen' => [
                        'type_examen' => $note['type_examen'],
                        'date_examen' => $note['date_examen'],
                        'semestre' => $note['semestre'],
                        'matiere' => [
                            'nom_matiere_fr' => $note['nom_matiere_fr']
                        ],
                        'classe' => [
                            'nom_classe' => $note['nom_classe']
                        ]
                    ]
                ];
            }, $notes);

            http_response_code(200);
            echo json_encode([
                'success' => true,
                'data' => $notesFormatees,
                'message' => 'Notes de l\'élève récupérées avec succès'
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la récupération des notes de l\'élève',
                'error' => $e->getMessage()
            ]);
        }
    }

    public function getAllByExamen($id_examen) {
        $stmt = $this->pdo->prepare('SELECT * FROM Note WHERE id_examen = ?');
        $stmt->execute([$id_examen]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getById($id_eleve, $id_examen) {
        $stmt = $this->pdo->prepare('SELECT * FROM Note WHERE id_eleve = ? AND id_examen = ?');
        $stmt->execute([$id_eleve, $id_examen]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function create() {
        try {
            $input = json_decode(file_get_contents('php://input'), true);

            // Validation des données
            if (!isset($input['id_eleve']) || !isset($input['id_examen']) || !isset($input['note'])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Tous les champs sont obligatoires'
                ]);
                return;
            }

            $id_eleve = (int)$input['id_eleve'];
            $id_examen = (int)$input['id_examen'];
            $note = (float)$input['note'];

            // Validation de la note (entre 0 et 20)
            if ($note < 0 || $note > 20) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'La note doit être comprise entre 0 et 20'
                ]);
                return;
            }

            // Vérifier que l'élève existe
            $stmt = $this->pdo->prepare('SELECT id_eleve FROM Eleve WHERE id_eleve = ?');
            $stmt->execute([$id_eleve]);
            if (!$stmt->fetch()) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Élève non trouvé'
                ]);
                return;
            }

            // Vérifier que l'examen existe
            $stmt = $this->pdo->prepare('SELECT id_examen FROM Examen WHERE id_examen = ?');
            $stmt->execute([$id_examen]);
            if (!$stmt->fetch()) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Examen non trouvé'
                ]);
                return;
            }

            // Vérifier qu'une note n'existe pas déjà
            $stmt = $this->pdo->prepare('SELECT id_eleve FROM Note WHERE id_eleve = ? AND id_examen = ?');
            $stmt->execute([$id_eleve, $id_examen]);
            if ($stmt->fetch()) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Une note existe déjà pour cet élève et cet examen'
                ]);
                return;
            }

            $sql = 'INSERT INTO Note (id_eleve, id_examen, note) VALUES (?, ?, ?)';
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$id_eleve, $id_examen, $note]);

            http_response_code(201);
            echo json_encode([
                'success' => true,
                'message' => 'Note créée avec succès'
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la création de la note',
                'error' => $e->getMessage()
            ]);
        }
    }

    public function update($id_eleve, $id_examen) {
        try {
            $input = json_decode(file_get_contents('php://input'), true);

            // Validation des données
            if (!isset($input['note'])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'La note est obligatoire'
                ]);
                return;
            }

            $note = (float)$input['note'];

            // Validation de la note (entre 0 et 20)
            if ($note < 0 || $note > 20) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'La note doit être comprise entre 0 et 20'
                ]);
                return;
            }

            // Vérifier que la note existe
            $stmt = $this->pdo->prepare('SELECT id_eleve FROM Note WHERE id_eleve = ? AND id_examen = ?');
            $stmt->execute([$id_eleve, $id_examen]);
            if (!$stmt->fetch()) {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'message' => 'Note non trouvée'
                ]);
                return;
            }

            $sql = 'UPDATE Note SET note=? WHERE id_eleve=? AND id_examen=?';
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$note, $id_eleve, $id_examen]);

            http_response_code(200);
            echo json_encode([
                'success' => true,
                'message' => 'Note modifiée avec succès'
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la modification de la note',
                'error' => $e->getMessage()
            ]);
        }
    }

    public function delete($id_eleve, $id_examen) {
        $stmt = $this->pdo->prepare('DELETE FROM Note WHERE id_eleve=? AND id_examen=?');
        $stmt->execute([$id_eleve, $id_examen]);
        return $stmt->rowCount();
    }
}
