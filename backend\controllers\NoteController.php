<?php
// controllers/NoteController.php
require_once __DIR__ . '/../models/Note.php';
require_once __DIR__ . '/../config/db.php';

class NoteController {
    private $pdo;
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    public function getAllByExamen($id_examen) {
        $stmt = $this->pdo->prepare('SELECT * FROM Note WHERE id_examen = ?');
        $stmt->execute([$id_examen]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getById($id_eleve, $id_examen) {
        $stmt = $this->pdo->prepare('SELECT * FROM Note WHERE id_eleve = ? AND id_examen = ?');
        $stmt->execute([$id_eleve, $id_examen]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function create($data) {
        $sql = 'INSERT INTO Note (id_eleve, id_examen, note) VALUES (?, ?, ?)';
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([
            $data['id_eleve'],
            $data['id_examen'],
            $data['note']
        ]);
        return $stmt->rowCount();
    }

    public function update($id_eleve, $id_examen, $note) {
        $sql = 'UPDATE Note SET note=? WHERE id_eleve=? AND id_examen=?';
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$note, $id_eleve, $id_examen]);
        return $stmt->rowCount();
    }

    public function delete($id_eleve, $id_examen) {
        $stmt = $this->pdo->prepare('DELETE FROM Note WHERE id_eleve=? AND id_examen=?');
        $stmt->execute([$id_eleve, $id_examen]);
        return $stmt->rowCount();
    }
}
