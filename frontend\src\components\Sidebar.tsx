import {Home,Users,GraduationCap,UsersRound,BookOpen,Calendar,ClipboardList,
        UserCheck,Euro,Bus,Boxes,LogOut, Building,Book,X} from "lucide-react";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../context/AuthContext";
import { ReactElement } from "react";
// import { User } from "../types";

type MenuItem = {
  icon: ReactElement;
  label: string;
  path: string;
};

type Role = "admin" | "enseignant" | "eleve" | "parent";

type MenuItems = {
  [key in Role]: MenuItem[];  
};

interface SidebarProps {
  onItemClick?: () => void;
}

export default function Sidebar({ onItemClick }: SidebarProps = {}) {
const navigate = useNavigate();
const { user, setUser} = useAuth();
// const items = menuItems[user.role as keyof typeof menuItems];
// const user : { role: Role; nom?: string }  = JSON.parse(localStorage.getItem("user") || '{}');
const [activeItem, setActiveItem] = useState<string>("Tableau de bord");

const menuItems: MenuItems = {
  admin: [
    { icon: <Home size={20} />, label: "Tableau de bord", path: "/admin/dashboard" },
    { icon: <Users size={20} />, label: "Élèves", path: "/admin/eleves"},
    { icon: <GraduationCap size={20} />, label: "Enseignants", path: "/admin/enseignants"  },
    { icon: <UsersRound size={20} />, label: "Parents", path: "/admin/parents" },
    { icon: <BookOpen size={20} />, label: "Classes", path: "/admin/classes" },
    { icon: <Building size={20} />, label: "Salles", path: "/admin/salles" },
    { icon: <Book size={20} />, label: "Matières", path: "/admin/matieres" },
    { icon: <Calendar size={20} />, label: "Emploi du temps", path: "/admin/emploi-du-temps" },
    { icon: <ClipboardList size={20} />, label: "Examens", path: "/admin/examens" },
    { icon: <ClipboardList size={20} />, label: "Bulletins", path: "/admin/bulletins" },
    { icon: <UserCheck size={20} />, label: "Présences", path: "/admin/presences" },
    { icon: <Euro size={20} />, label: "Paiements", path: "/admin/paiements" },
    { icon: <Bus size={20} />, label: "Transport", path: "/admin/transport" },
    { icon: <Boxes size={20} />, label: "Activités", path: "/admin/activites" }
  ],
  enseignant: [
    { icon: <Home size={20} />, label: "Tableau de bord", path: "/enseignant/dashboard" },
    { icon: <Users size={20} />, label: "Élèves", path: "/enseignant/eleves"},
    { icon: <BookOpen size={20} />, label: "Classes", path: "/enseignant/classes" },
    { icon: <Calendar size={20} />, label: "Emploi du temps", path: "/enseignant/emploi-du-temps" },
    { icon: <ClipboardList size={20} />, label: "Examens", path: "/enseignant/examens" },
    { icon: <ClipboardList size={20} />, label: "Bulletins", path: "/enseignant/bulletins" },
    { icon: <UserCheck size={20} />, label: "Présences", path: "/enseignant/presences" },
    { icon: <Boxes size={20} />, label: "Activités", path: "/enseignant/activites" }
  ],
  eleve: [
    { icon: <Home size={20} />, label: "Tableau de bord", path: "/eleve/dashboard" },
    { icon: <Calendar size={20} />, label: "Emploi du temps", path: "/eleve/emploi-du-temps" },
    { icon: <ClipboardList size={20} />, label: "Examens", path: "/eleve/examens" },
    { icon: <ClipboardList size={20} />, label: "Bulletins", path: "/eleve/bulletins" },
    { icon: <UserCheck size={20} />, label: "Présence", path: "/eleve/presence" },
    { icon: <Boxes size={20} />, label: "Activités", path: "/eleve/activites" }
  ],
  parent: [
    { icon: <Home size={20} />, label: "Tableau de bord", path: "/parent/dashboard" },
    { icon: <Calendar size={20} />, label: "Emploi du temps", path: "/parent/emploi-du-temps" },
    { icon: <ClipboardList size={20} />, label: "Examens", path: "/parent/examens" },
    { icon: <ClipboardList size={20} />, label: "Bulletins", path: "/parent/bulletins" },
    { icon: <UserCheck size={20} />, label: "Présence", path: "/parent/presence" },
    { icon: <Euro size={20} />, label: "Paiements", path: "/parent/paiements" },
    { icon: <Boxes size={20} />, label: "Activités", path: "/parent/activites" }
  ],
};

const handleLogout = () => {
  localStorage.removeItem("token");
  localStorage.removeItem("user");
  setUser(null);
  navigate("/login");

};

return (
  <div className="h-screen w-full bg-white flex flex-col justify-between border-r shadow-md overflow-y-auto sidebar-scrollbar">
    <div>
      {/*  Logo */}
      <div className="flex justify-between items-center p-4">
        <div className="flex justify-start items-center text-secondary">
          <img src="../../public/logo.svg" width={32} height={32} alt="Logo" />
          <span className="block md:hidden lg:block text-xl font-bold">ScolaNova</span>
        </div>
        {/* Bouton de fermeture pour mobile */}
        <button
          onClick={onItemClick}
          className="md:hidden text-gray-500 hover:text-gray-700"
          aria-label="Fermer le menu"
        >
          <X size={24} />
        </button>
      </div>

      {/* Navbar */}
      <nav role="navigation" aria-label="Menu principal" className="space-y-1 px-3">
          {menuItems[user?.role as Role]?.map((item) => (
            <div
              key={item.label}
              onClick={() => {
                // console.log("🔗 Clic sur menu:", item.label, "→", item.path);
                setActiveItem(item.label);
                navigate(item.path);
                onItemClick?.(); // Fermer le menu mobile après navigation
              }}
              className={`flex items-center justify-start md:justify-center lg:justify-start p-2 space-x-3 text-sm cursor-pointer
                ${activeItem === item.label ? 'bg-blue-100 text-primary' : 'text-gray-700 hover:bg-background'}
                rounded-md transition-colors duration-200`}
            >
              {item.icon}
              <span className="block md:hidden lg:block">{item.label}</span>
            </div>
          ))}
      </nav>
    </div>

    {/* Profile and Logout */}
    <div className="border-t">
      <div className="flex items-center justify-start md:justify-center lg:justify-start mt-2 px-3">
        <div className="bg-primary text-white w-8 h-8 mr-2 md:mr-0 lg:mr-2 flex items-center justify-center rounded-full font-semibold text-sm">
          { (user ? user.nom.charAt(0).toUpperCase() : "") +
            (user ? user.prenom.charAt(0).toUpperCase() : "")}
        </div>
        <div className="block md:hidden lg:block">
          <div className="text-sm font-semibold">
            {user ? [user.nom, user.prenom].filter(Boolean)
                .map(s => s.charAt(0).toUpperCase() + s.slice(1).toLowerCase())
                .join(" ") : "Utilisateur"}
          </div>
          <div className="text-xs text-gray-500">{user?.role || 'Role'}</div>
        </div>
      </div>
      <button
        onClick={handleLogout}
        className="flex items-center justify-start md:justify-center lg:justify-start w-full mt-2 px-3 py-2 text-sm text-red-600 rounded-md hover:bg-red-50 transition-colors duration-200">
          <LogOut size={20}  className="mr-2 md:mr-0 lg:mr-2"/>
          <span className="block md:hidden lg:block">Déconnexion</span>
      </button>
    </div>
  </div>
);
}

