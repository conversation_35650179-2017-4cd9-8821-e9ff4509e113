import { useState, useEffect } from 'react';
import { getEnseignants } from '../services/api';
import { useAuth } from '../context/AuthContext';
import type { Enseignant } from '../types';

/**
 * Hook personnalisé pour gérer l'enseignant connecté
 * Évite la duplication de code entre les différentes pages
 */
export const useEnseignantConnecte = () => {
  const { user } = useAuth();
  const [enseignants, setEnseignants] = useState<Enseignant[]>([]);
  const [enseignantConnecte, setEnseignantConnecte] = useState<Enseignant | null>(null);
  const [loading, setLoading] = useState(true);

  // Fonction pour trouver l'enseignant connecté
  const getEnseignantConnecte = (enseignantsList?: Enseignant[]) => {
    const liste = enseignantsList || enseignants;
    if (user?.role === 'enseignant' && liste.length > 0) {
      return liste.find(e => e.id_utilisateur === user.id_utilisateur) || null;
    }
    return null;
  };

  // Charger les enseignants au montage du composant
  useEffect(() => {
    const fetchEnseignants = async () => {
      setLoading(true);
      try {
        const response = await getEnseignants();
        if (response.data.success) {
          const enseignantsData = response.data.data;
          setEnseignants(enseignantsData);
          
          // Si c'est un enseignant, le trouver automatiquement
          if (user?.role === 'enseignant') {
            const enseignant = getEnseignantConnecte(enseignantsData);
            setEnseignantConnecte(enseignant);
          }
        }
      } catch (error) {
        console.error("Erreur lors du chargement des enseignants:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchEnseignants();
  }, [user]);

  return {
    enseignants,
    enseignantConnecte,
    getEnseignantConnecte,
    loading,
    isEnseignant: user?.role === 'enseignant',
    isAdmin: user?.role === 'admin'
  };
};
