<?php

require_once __DIR__ . '/../models/Contrat.php';

class ContratController
{
    private $contrat;

    public function __construct($db)
    {
        $this->contrat = new Contrat($db);
    }

    public function getContrats()
    {
        try {
            $contrats = $this->contrat->getAll();
            echo json_encode(['success' => true, 'data' => $contrats]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur lors de la récupération des contrats']);
        }
    }

    public function getContrat($id)
    {
        try {
            $contrat = $this->contrat->getById($id);
            if ($contrat) {
                echo json_encode(['success' => true, 'data' => $contrat]);
            } else {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Contrat non trouvé']);
            }
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur lors de la récupération du contrat']);
        }
    }

    public function getContratsByEnseignant($id_enseignant)
    {
        try {
            $contrats = $this->contrat->getByEnseignantId($id_enseignant);
            echo json_encode(['success' => true, 'data' => $contrats]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur lors de la récupération des contrats']);
        }
    }

    public function getActiveContratByEnseignant($id_enseignant)
    {
        try {
            $contrat = $this->contrat->getActiveByEnseignantId($id_enseignant);
            if ($contrat) {
                echo json_encode(['success' => true, 'data' => $contrat]);
            } else {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Aucun contrat actif trouvé']);
            }
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur lors de la récupération du contrat actif']);
        }
    }

    public function addContrat()
    {
        try {
            $data = json_decode(file_get_contents("php://input"), true);
            error_log("Données reçues pour Contrat : " . json_encode($data));

            if (!$data) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Aucune donnée reçue']);
                return;
            }

            // Validation des champs requis
            $required_fields = ['id_enseignant', 'type_contrat', 'poste', 'date_debut', 'salaire_base'];
            foreach ($required_fields as $field) {
                if (!isset($data[$field]) || (is_string($data[$field]) && empty(trim($data[$field])))) {
                    http_response_code(400);
                    echo json_encode(['success' => false, 'message' => "Le champ $field est requis"]);
                    return;
                }
            }

            // Validation du salaire
            if (!is_numeric($data['salaire_base']) || $data['salaire_base'] <= 0) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Le salaire doit être un nombre supérieur à 0']);
                return;
            }

            // Validation des dates
            if (isset($data['date_fin']) && !empty($data['date_fin'])) {
                if (strtotime($data['date_fin']) <= strtotime($data['date_debut'])) {
                    http_response_code(400);
                    echo json_encode(['success' => false, 'message' => 'La date de fin doit être postérieure à la date de début']);
                    return;
                }
            }

            $id_contrat = $this->contrat->create($data);
            if ($id_contrat) {
                http_response_code(201);
                echo json_encode([
                    'success' => true,
                    'id_contrat' => $id_contrat,
                    'message' => 'Contrat créé avec succès'
                ]);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Erreur lors de la création du contrat']);
            }
        } catch (Exception $e) {
            error_log("Exception dans addContrat: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    public function updateContrat($id)
    {
        try {
            $data = json_decode(file_get_contents("php://input"), true);
            error_log("Mise à jour contrat $id avec données : " . json_encode($data));

            if (!$data) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Aucune donnée reçue']);
                return;
            }

            $result = $this->contrat->update($id, $data);
            // Succès si aucune exception n'est levée
            echo json_encode(['success' => true, 'message' => 'Contrat mis à jour avec succès']);
        } catch (Exception $e) {
            error_log("Exception dans updateContrat: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    public function deleteContrat($id)
    {
        try {
            if ($this->contrat->delete($id)) {
                echo json_encode(['success' => true, 'message' => 'Contrat supprimé avec succès']);
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Erreur lors de la suppression du contrat']);
            }
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur']);
        }
    }

    public function terminateContrat($id)
    {
        try {
            if ($this->contrat->terminateContract($id)) {
                echo json_encode(['success' => true, 'message' => 'Contrat terminé avec succès']);
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Erreur lors de la terminaison du contrat']);
            }
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur']);
        }
    }
}
