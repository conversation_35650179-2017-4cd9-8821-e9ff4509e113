<?php

require_once __DIR__ . '/../controllers/ContratController.php';

function handleContratRoutes($uri, $method, $pdo)
{
    $controller = new ContratController($pdo);
    error_log("ContratRoutes - URI: $uri, Method: $method");

    if ($uri === '/contrats') {
        if ($method === 'GET') {
            $controller->getContrats();
            return true;
        } elseif ($method === 'POST') {
            $controller->addContrat();
            return true;
        }
    } elseif (preg_match('#^/contrats/(\d+)$#', $uri, $matches)) {
        $id = (int)$matches[1];
        if ($method === 'GET') {
            $controller->getContrat($id);
            return true;
        } elseif ($method === 'PUT') {
            $controller->updateContrat($id);
            return true;
        } elseif ($method === 'DELETE') {
            $controller->deleteContrat($id);
            return true;
        }
    } elseif (preg_match('#^/contrats/(\d+)/terminate$#', $uri, $matches)) {
        $id = (int)$matches[1];
        if ($method === 'PUT') {
            $controller->terminateContrat($id);
            return true;
        }
    } elseif (preg_match('#^/enseignants/(\d+)/contrats$#', $uri, $matches)) {
        $id_enseignant = (int)$matches[1];
        if ($method === 'GET') {
            $controller->getContratsByEnseignant($id_enseignant);
            return true;
        }
    } elseif (preg_match('#^/enseignants/(\d+)/contrats/active$#', $uri, $matches)) {
        $id_enseignant = (int)$matches[1];
        if ($method === 'GET') {
            $controller->getActiveContratByEnseignant($id_enseignant);
            return true;
        }
    }

    error_log("Route non reconnue dans handleContratRoutes : $uri");
    return false;
}
