export default function Table({columns, renderRow, data}: {
    columns: { header: string; accessor: string; className?: string }[];
    renderRow: (item: any) => React.ReactNode;
    data: any[];
  }) {
    return (
      <div className="overflow-x-auto table-scrollbar">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-100" >
            <tr  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {columns.map((col) => (
                <th key={col.accessor} className={col.className}>{col.header}</th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white">
            {data.map((item) => renderRow(item))}
          </tbody>
        </table>
      </div>
    );
  };
  
  