<?php

class Paiement
{
    private $pdo;
    private $table = "Paiement";

    public function __construct($db)
    {
        $this->pdo = $db;
    }

    /**
     * R<PERSON>cupérer tous les paiements avec les détails des élèves
     */
    public function getAllWithDetails()
    {
        $query = "SELECT p.*, u.nom, u.prenom, c.nom_classe, a.libelle as annee_libelle
                  FROM {$this->table} p
                  LEFT JOIN Eleve e ON p.id_eleve = e.id_eleve
                  LEFT JOIN Utilisateur u ON e.id_utilisateur = u.id_utilisateur
                  LEFT JOIN Inscription i ON e.id_eleve = i.id_eleve
                  LEFT JOIN Classe c ON i.id_classe = c.id_classe
                  LEFT JOIN Annee_Scolaire a ON p.id_annee_scolaire = a.id_annee_scolaire
                  ORDER BY p.date_paiement DESC";
        
        $stmt = $this->pdo->prepare($query);
        $stmt->execute();
        
        $paiements = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Restructurer les données pour correspondre au format attendu par le frontend
        return array_map(function($paiement) {
            return [
                'id_paiement' => (int)$paiement['id_paiement'],
                'id_eleve' => (int)$paiement['id_eleve'],
                'id_annee_scolaire' => (int)$paiement['id_annee_scolaire'],
                'montant' => (float)$paiement['montant'],
                'mois' => $paiement['mois'],
                'date_paiement' => $paiement['date_paiement'],
                'type_paiement' => $paiement['type_paiement'],
                'mode_paiement' => $paiement['mode_paiement'],
                'statut' => $paiement['statut'],
                'description' => $paiement['description'],
                'eleve' => [
                    'nom' => $paiement['nom'],
                    'prenom' => $paiement['prenom'],
                    'classe' => $paiement['nom_classe']
                ],
                'annee_scolaire' => $paiement['annee_libelle']
            ];
        }, $paiements);
    }

    /**
     * Récupérer un paiement par ID avec les détails
     */
    public function getByIdWithDetails($id)
    {
        $query = "SELECT p.*, u.nom, u.prenom, c.nom_classe, a.libelle as annee_libelle
                  FROM {$this->table} p
                  LEFT JOIN Eleve e ON p.id_eleve = e.id_eleve
                  LEFT JOIN Utilisateur u ON e.id_utilisateur = u.id_utilisateur
                  LEFT JOIN Inscription i ON e.id_eleve = i.id_eleve
                  LEFT JOIN Classe c ON i.id_classe = c.id_classe
                  LEFT JOIN Annee_Scolaire a ON p.id_annee_scolaire = a.id_annee_scolaire
                  WHERE p.id_paiement = ?";
        
        $stmt = $this->pdo->prepare($query);
        $stmt->execute([$id]);
        
        $paiement = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$paiement) {
            return null;
        }

        return [
            'id_paiement' => (int)$paiement['id_paiement'],
            'id_eleve' => (int)$paiement['id_eleve'],
            'id_annee_scolaire' => (int)$paiement['id_annee_scolaire'],
            'montant' => (float)$paiement['montant'],
            'mois' => $paiement['mois'],
            'date_paiement' => $paiement['date_paiement'],
            'type_paiement' => $paiement['type_paiement'],
            'mode_paiement' => $paiement['mode_paiement'],
            'statut' => $paiement['statut'],
            'description' => $paiement['description'],
            'eleve' => [
                'nom' => $paiement['nom'],
                'prenom' => $paiement['prenom'],
                'classe' => $paiement['nom_classe']
            ],
            'annee_scolaire' => $paiement['annee_libelle']
        ];
    }

    /**
     * Récupérer un paiement par ID simple
     */
    public function getById($id)
    {
        $query = "SELECT * FROM {$this->table} WHERE id_paiement = ?";
        $stmt = $this->pdo->prepare($query);
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Créer un nouveau paiement
     */
    public function create($data)
    {
        $query = "INSERT INTO {$this->table} (
                    id_eleve, id_annee_scolaire, montant, mois, date_paiement,
                    type_paiement, mode_paiement, statut, description
                  ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $this->pdo->prepare($query);
        
        if ($stmt->execute([
            $data['id_eleve'],
            $data['id_annee_scolaire'],
            $data['montant'],
            $data['mois'],
            $data['date_paiement'],
            $data['type_paiement'],
            $data['mode_paiement'],
            $data['statut'],
            $data['description'] ?? null
        ])) {
            return $this->pdo->lastInsertId();
        }
        
        return false;
    }

    /**
     * Mettre à jour un paiement
     */
    public function update($id, $data)
    {
        $query = "UPDATE {$this->table} SET 
                    id_eleve = ?, id_annee_scolaire = ?, montant = ?, mois = ?, 
                    date_paiement = ?, type_paiement = ?, mode_paiement = ?, 
                    statut = ?, description = ?
                  WHERE id_paiement = ?";
        
        $stmt = $this->pdo->prepare($query);
        
        return $stmt->execute([
            $data['id_eleve'],
            $data['id_annee_scolaire'],
            $data['montant'],
            $data['mois'],
            $data['date_paiement'],
            $data['type_paiement'],
            $data['mode_paiement'],
            $data['statut'],
            $data['description'] ?? null,
            $id
        ]);
    }

    /**
     * Supprimer un paiement
     */
    public function delete($id)
    {
        $query = "DELETE FROM {$this->table} WHERE id_paiement = ?";
        $stmt = $this->pdo->prepare($query);
        return $stmt->execute([$id]);
    }

    /**
     * Récupérer les paiements d'un élève
     */
    public function getByEleve($id_eleve)
    {
        $query = "SELECT p.*, a.libelle as annee_libelle
                  FROM {$this->table} p
                  LEFT JOIN Annee_Scolaire a ON p.id_annee_scolaire = a.id_annee_scolaire
                  WHERE p.id_eleve = ?
                  ORDER BY p.date_paiement DESC";
        
        $stmt = $this->pdo->prepare($query);
        $stmt->execute([$id_eleve]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Récupérer les statistiques des paiements
     */
    public function getStatistiques($annee_scolaire_id = null)
    {
        $query = "SELECT
                    SUM(CASE WHEN statut = 'payé' THEN montant ELSE 0 END) as total_paiements,
                    COUNT(CASE WHEN statut = 'payé' THEN 1 END) as nombre_payes,
                    COUNT(CASE WHEN statut = 'en attente' THEN 1 END) as nombre_en_attente,
                    COUNT(CASE WHEN statut = 'retard' THEN 1 END) as nombre_retard
                  FROM {$this->table}";

        $params = [];

        // Ajouter le filtre par année scolaire si spécifié
        if ($annee_scolaire_id) {
            $query .= " WHERE id_annee_scolaire = ?";
            $params[] = $annee_scolaire_id;
        }

        $stmt = $this->pdo->prepare($query);
        $stmt->execute($params);

        $stats = $stmt->fetch(PDO::FETCH_ASSOC);

        return [
            'total_paiements' => (float)($stats['total_paiements'] ?? 0),
            'nombre_payes' => (int)($stats['nombre_payes'] ?? 0),
            'nombre_en_attente' => (int)($stats['nombre_en_attente'] ?? 0),
            'nombre_retard' => (int)($stats['nombre_retard'] ?? 0)
        ];
    }

    /**
     * Vérifier si un paiement existe déjà
     */
    public function exists($id_eleve, $id_annee_scolaire, $type_paiement, $mois, $excludeId = null)
    {
        $query = "SELECT COUNT(*) FROM {$this->table} 
                  WHERE id_eleve = ? 
                  AND id_annee_scolaire = ? 
                  AND type_paiement = ? 
                  AND mois = ?";
        
        $params = [$id_eleve, $id_annee_scolaire, $type_paiement, $mois];
        
        if ($excludeId) {
            $query .= " AND id_paiement != ?";
            $params[] = $excludeId;
        }
        
        $stmt = $this->pdo->prepare($query);
        $stmt->execute($params);
        
        return $stmt->fetchColumn() > 0;
    }

    /**
     * Vérifier les paiements mensuels
     */
    public function verifierPaiementsMensuels($mois, $annee)
    {
        // Récupérer l'année scolaire active
        $queryAnnee = "SELECT id_annee_scolaire FROM Annee_Scolaire WHERE est_active = 1";
        $stmtAnnee = $this->pdo->prepare($queryAnnee);
        $stmtAnnee->execute();
        $anneeActive = $stmtAnnee->fetch(PDO::FETCH_ASSOC);
        
        if (!$anneeActive) {
            throw new Exception('Aucune année scolaire active trouvée');
        }
        
        $id_annee_scolaire = $anneeActive['id_annee_scolaire'];

        // Compter le total d'élèves actifs
        $queryTotal = "SELECT COUNT(*) as total FROM Eleve e 
                       JOIN Inscription i ON e.id_eleve = i.id_eleve 
                       WHERE i.id_annee_scolaire = ?";
        $stmtTotal = $this->pdo->prepare($queryTotal);
        $stmtTotal->execute([$id_annee_scolaire]);
        $totalEleves = $stmtTotal->fetch(PDO::FETCH_ASSOC)['total'];

        // Compter les élèves qui ont déjà payé ce mois
        $queryPayes = "SELECT COUNT(DISTINCT id_eleve) as payes 
                       FROM {$this->table} 
                       WHERE mois = ? 
                       AND id_annee_scolaire = ?
                       AND type_paiement = 'scolarité'";
        $stmtPayes = $this->pdo->prepare($queryPayes);
        $stmtPayes->execute([$mois, $id_annee_scolaire]);
        $dejaPayes = $stmtPayes->fetch(PDO::FETCH_ASSOC)['payes'];

        return [
            'total_eleves' => (int)$totalEleves,
            'deja_payes' => (int)$dejaPayes,
            'a_generer' => (int)($totalEleves - $dejaPayes)
        ];
    }

    /**
     * Générer les paiements mensuels
     */
    public function genererPaiementsMensuels($mois, $annee, $type_paiement = 'scolarité')
    {
        // Récupérer l'année scolaire active
        $queryAnnee = "SELECT id_annee_scolaire FROM Annee_Scolaire WHERE est_active = 1";
        $stmtAnnee = $this->pdo->prepare($queryAnnee);
        $stmtAnnee->execute();
        $anneeActive = $stmtAnnee->fetch(PDO::FETCH_ASSOC);
        
        if (!$anneeActive) {
            throw new Exception('Aucune année scolaire active trouvée');
        }
        
        $id_annee_scolaire = $anneeActive['id_annee_scolaire'];

        // Récupérer les élèves qui n'ont pas encore de paiement pour ce mois et ce type
        $query = "SELECT e.id_eleve, n.prix_mensuel, n.frais_inscription
                  FROM Eleve e
                  JOIN Inscription i ON e.id_eleve = i.id_eleve
                  JOIN Classe c ON i.id_classe = c.id_classe
                  JOIN Niveau n ON c.id_niveau = n.id_niveau
                  WHERE i.id_annee_scolaire = ?
                  AND NOT EXISTS (
                      SELECT 1 FROM {$this->table} p 
                      WHERE p.id_eleve = e.id_eleve 
                      AND p.mois = ? 
                      AND p.id_annee_scolaire = ?
                      AND p.type_paiement = ?
                  )";
        
        $stmt = $this->pdo->prepare($query);
        $stmt->execute([$id_annee_scolaire, $mois, $id_annee_scolaire, $type_paiement]);
        $eleves = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $paiementsCrees = 0;
        $elevesConernes = count($eleves);

        // Créer les paiements
        foreach ($eleves as $eleve) {
            $montant = $type_paiement === 'inscription' ? $eleve['frais_inscription'] : $eleve['prix_mensuel'];
            
            $paiementData = [
                'id_eleve' => $eleve['id_eleve'],
                'id_annee_scolaire' => $id_annee_scolaire,
                'montant' => $montant,
                'mois' => $mois,
                'date_paiement' => date('Y-m-d'),
                'type_paiement' => $type_paiement,
                'mode_paiement' => 'espèces',
                'statut' => 'en attente',
                'description' => "Paiement automatique - $mois $annee"
            ];

            if ($this->create($paiementData)) {
                $paiementsCrees++;
            }
        }

        return [
            'paiements_crees' => $paiementsCrees,
            'eleves_concernes' => $elevesConernes
        ];
    }
}
