<?php

require_once __DIR__ . '/../controllers/DiplomeController.php';

function handleDiplomeRoutes($uri, $method, $pdo)
{
    $controller = new DiplomeController($pdo);
    error_log("DiplomeRoutes - URI: $uri, Method: $method");

    if ($uri === '/diplomes') {
        if ($method === 'GET') {
            $controller->getDiplomes();
            return true;
        } elseif ($method === 'POST') {
            $controller->addDiplome();
            return true;
        }
    } elseif (preg_match('#^/diplomes/(\d+)$#', $uri, $matches)) {
        $id = (int)$matches[1];
        if ($method === 'GET') {
            $controller->getDiplome($id);
            return true;
        } elseif ($method === 'PUT') {
            $controller->updateDiplome($id);
            return true;
        } elseif ($method === 'DELETE') {
            $controller->deleteDiplome($id);
            return true;
        }
    } elseif (preg_match('#^/enseignants/(\d+)/diplomes$#', $uri, $matches)) {
        $id_enseignant = (int)$matches[1];
        if ($method === 'GET') {
            $controller->getDiplomesByEnseignant($id_enseignant);
            return true;
        }
    }

    error_log("Route non reconnue dans handleDiplomeRoutes : $uri");
    return false;
}
