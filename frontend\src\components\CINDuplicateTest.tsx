import React, { useState } from 'react';
import { addParent, register } from '../services/api';
import { extractErrorMessage } from '../utils/errorUtils';
import Button from './Button';
import Input from './Input';

/**
 * Composant de test pour vérifier la validation des CIN dupliqués
 */
const CINDuplicateTest: React.FC = () => {
  const [cin, setCin] = useState('AB123456');
  const [message, setMessage] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  const testCreateDuplicateCIN = async () => {
    setIsLoading(true);
    setMessage('');

    try {
      // Créer d'abord un utilisateur
      const userData = {
        nom: 'Test',
        prenom: 'Parent',
        email: `parent${Date.now()}@test.com`, // Email unique
        role: 'parent',
        sexe: 'homme',
        telephone: '0612345678',
        date_naissance: '1980-01-01',
        lieu_naissance: 'Casablanca',
        nationalite: 'Marocaine',
        adresse: 'Test Address',
        est_actif: true
      };

      const userResponse = await register(userData);
      
      // Ensuite créer le parent avec le CIN
      const parentData = {
        id_utilisateur: userResponse.data.id_user,
        nom_ar: 'اختبار',
        prenom_ar: 'والد',
        num_CIN: cin
      };

      await addParent(parentData);
      
      setMessage('✅ Premier parent créé avec succès');
      
      // Maintenant essayer de créer un deuxième parent avec le même CIN
      setTimeout(async () => {
        try {
          const userData2 = {
            ...userData,
            email: `parent2${Date.now()}@test.com`, // Email différent
            prenom: 'Parent2'
          };

          const userResponse2 = await register(userData2);
          
          const parentData2 = {
            id_utilisateur: userResponse2.data.id_user,
            nom_ar: 'اختبار2',
            prenom_ar: 'والد2',
            num_CIN: cin // Même CIN - devrait échouer
          };

          await addParent(parentData2);
          setMessage('❌ Erreur : Le deuxième parent a été créé (ne devrait pas arriver)');
        } catch (error: any) {
          const errorMessage = error.userMessage || extractErrorMessage(error, 'Erreur par défaut');
          setMessage(`✅ Validation réussie : ${errorMessage}`);
        }
        setIsLoading(false);
      }, 1000);
      
    } catch (error: any) {
      console.log('🔍 Erreur création premier parent:', error);
      
      const errorMessage = error.userMessage || extractErrorMessage(error, 'Erreur par défaut');
      setMessage(`❌ Erreur premier parent: ${errorMessage}`);
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-md mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-xl font-bold mb-4">Test CIN Dupliqué</h2>
      
      <div className="space-y-4">
        <Input
          label="Numéro CIN à tester"
          value={cin}
          onChange={(e) => setCin(e.target.value)}
          placeholder="AB123456"
        />
        
        <Button 
          onClick={testCreateDuplicateCIN}
          disabled={isLoading}
          variant="primary"
          className="w-full"
        >
          {isLoading ? 'Test en cours...' : 'Tester CIN Dupliqué'}
        </Button>
      </div>
      
      {message && (
        <div className={`mt-4 p-4 rounded-lg ${
          message.includes('✅') 
            ? 'bg-green-50 text-green-800 border border-green-200'
            : 'bg-red-50 text-red-800 border border-red-200'
        }`}>
          <div className="whitespace-pre-line">{message}</div>
        </div>
      )}
      
      <div className="mt-4 text-sm text-gray-600">
        <h3 className="font-semibold mb-2">Test :</h3>
        <ol className="list-decimal list-inside space-y-1">
          <li>Crée un premier parent avec le CIN spécifié</li>
          <li>Essaie de créer un deuxième parent avec le même CIN</li>
          <li>Devrait échouer avec le message : "Ce numéro CIN est déjà utilisé par un autre parent"</li>
        </ol>
        
        <h3 className="font-semibold mt-4 mb-2">Messages attendus :</h3>
        <ul className="list-disc list-inside space-y-1">
          <li><strong>Avant :</strong> "Request failed with status code 500"</li>
          <li><strong>Après :</strong> "Ce numéro CIN est déjà utilisé par un autre parent"</li>
        </ul>
      </div>
    </div>
  );
};

export default CINDuplicateTest;
