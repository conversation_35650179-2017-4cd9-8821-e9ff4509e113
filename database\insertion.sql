INSERT INTO Annee_<PERSON>ire (libelle, date_debut, date_fin, est_active)
VALUES
('2023-2024', '2023-09-01', '2024-06-30', TRUE),
('2024-2025', '2024-09-01', '2025-06-30', FALSE)

INSERT INTO Utilisateur (nom, prenom, email, mot_de_passe, role, sexe, date_naissance, lieu_naissance, nationalite, telephone, adresse, est_valide, est_actif)
VALUES
('Elhadi', 'Rachid', '<EMAIL>', '$2y$10$CwT7Bbyu3j5lWA9hH6Q5.eFw2of8HcVJErFZ.vGyOa2.Jjqz9/j8m', 'eleve', 'garçon', '2010-06-15', 'Casablanca', 'marocain', '0612345678', 'Casablanca, Maroc', TRUE, TRUE),
('Amina', 'Noura', '<EMAIL>', '$2y$10$6jfgDkjgFu5Lls1pqZhB8ETsGrA8xx6qPgl3bK3kwM6X6mgn2RPKP', 'eleve', 'fille', '2011-04-21', 'Rabat', 'marocain', '0623456789', 'Rabat, Maroc', TRUE, TRUE),
('Yassir', 'Mohamed', '<EMAIL>', '$2y$10$EKVtUgIpbVt6WxXXfF2Qm.D13v7FJ1WvTSnN67emc.wLt25RieW7m', 'eleve', 'garçon', '2010-03-01', 'Fes', 'marocain', '0634567890', 'Fes, Maroc', TRUE, TRUE),
('Laila', 'Ahmed', '<EMAIL>', '$2y$10$3XIHVJkBkvOghs7P4V4ztkDbX3txgFqH7vGd1l.xcOClAOb2iBOre', 'eleve', 'fille', '2011-07-30', 'Marrakech', 'marocain', '0645678901', 'Marrakech, Maroc', TRUE, TRUE),
('Omar', 'Salim', '<EMAIL>', '$2y$10$GmnRswzVuUqD6h2zjm0yYuNYVNh8PUNHH3cHpGR/2C7gDZZ.xSowu', 'eleve', 'garçon', '2010-10-10', 'Agadir', 'marocain', '0656789012', 'Agadir, Maroc', TRUE, TRUE),
('Imane', 'Lahbabi', '<EMAIL>', '$2y$10$gGzXmbOYd7bhn9uG9n60o4Ae3Bh3zD9rboRP1Coc9noQCrw2xXgza', 'eleve', 'fille', '2011-01-18', 'Tangier', 'marocain', '**********', 'Tangier, Maroc', TRUE, TRUE),
('Mounir', 'Tazi', '<EMAIL>', '$2y$10$GbzJHlNhbi82bbJmfkQSfG6d4lzPpCQqAa4pRH9qDVEpWg2T2fw1W', 'eleve', 'garçon', '2010-11-25', 'Meknes', 'marocain', '0678901234', 'Meknes, Maroc', TRUE, TRUE),
('Sanae', 'Mahfoud', '<EMAIL>', '$2y$10$sKXFXz6LzO8Pksd0y1vScMOZGjf9Rax0k7.DM0ubzO9OeMF4ABgDi', 'eleve', 'fille', '2010-12-05', 'Tétouan', 'marocain', '0689012345', 'Tétouan, Maroc', TRUE, TRUE),
('Anas', 'Bouhaddou', '<EMAIL>', '$2y$10$EMbBhrvCOUCOwmi7UeHwi0T9A28Us7MB0i6jeZGOMmnNGgSKjyh6G', 'eleve', 'garçon', '2011-08-13', 'Marrakech', 'marocain', '0690123456', 'Marrakech, Maroc', TRUE, TRUE),
('Meryem', 'Laaziz', '<EMAIL>', '$2y$10$PwYtgd6.kZgPbFHLPhiJ3Ul8IcDbjDZ5hHM0wZVPOaXhDQZCqTckC', 'eleve', 'fille', '2011-02-27', 'Rabat', 'marocain', '0612345678', 'Rabat, Maroc', TRUE, TRUE),
('Hamza', 'Rachid', '<EMAIL>', '$2y$10$HjREz.n0KbeWdrwDzxxATfXg8JmLPyUEdszSmMBZScSuHmV4ZG7wm', 'eleve', 'garçon', '2010-09-02', 'Casablanca', 'marocain', '0623456789', 'Casablanca, Maroc', TRUE, TRUE),
('Asma', 'Zeroual', '<EMAIL>', '$2y$10$uH3Kh.XERhr3R63ZJ0uMNzP5Admd1m1IaQUndUvkNwSYj9R.NKYqa', 'eleve', 'fille', '2010-11-18', 'Salé', 'marocain', '0634567890', 'Salé, Maroc', TRUE, TRUE);


INSERT INTO Ancienne_Ecole (code_gresa, nom, type, cycle, adresse)
VALUES
('GRESA001', 'École primaire Al Amal', 'publique', 'primaire', 'Casablanca, Maroc'),
('GRESA002', 'École Jabrane', 'publique', 'primaire', 'Rabat, Maroc'),
('GRESA003', 'École Al Imane', 'privée', 'primaire', 'Fès, Maroc'),
('GRESA004', 'École Al Hidaya', 'publique', 'primaire', 'Marrakech, Maroc'),
('GRESA005', 'École El Moutaheda', 'privée', 'primaire', 'Agadir, Maroc'),
('GRESA006', 'École Mohammed VI', 'publique', 'primaire', 'Tangier, Maroc'),
('GRESA007', 'École Aswat', 'privée', 'primaire', 'Meknes, Maroc'),
('GRESA008', 'École Al Nour', 'publique', 'primaire', 'Tétouan, Maroc'),
('GRESA009', 'École Ibn Sina', 'privée', 'primaire', 'Marrakech, Maroc'),
('GRESA010', 'École Jabal Al Noor', 'publique', 'primaire', 'Rabat, Maroc'),
('GRESA011', 'École Al Farabi', 'privée', 'primaire', 'Casablanca, Maroc'),
('GRESA012', 'École Al Fath', 'publique', 'primaire', 'Salé, Maroc');

INSERT INTO Eleve (id_utilisateur, code_massar, code_gresa, nom_ar, prenom_ar, lieu_naissance_ar)
VALUES
(23, 'MASSAR001', 'GRESA001', 'الهادي', 'رشيد', 'الدار البيضاء'),
(24, 'MASSAR002', 'GRESA002', 'أمينة', 'نورة', 'الرباط'),
(25, 'MASSAR003', 'GRESA003', 'ياسر', 'محمد', 'فاس'),
(26, 'MASSAR004', 'GRESA004', 'ليلى', 'أحمد', 'مراكش'),
(27, 'MASSAR005', 'GRESA005', 'عمر', 'سليم', 'أكادير'),
(28, 'MASSAR006', 'GRESA006', 'إيمان', 'لحبابي', 'طنجة'),
(29, 'MASSAR007', 'GRESA007', 'مُنير', 'الطا زي', 'مكناس'),
(30, 'MASSAR008', 'GRESA008', 'سناء', 'محفوظ', 'تطوان'),
(31, 'MASSAR009', 'GRESA009', 'أنس', 'بوحدود', 'مراكش'),
(32, 'MASSAR010', 'GRESA010', 'مريم', 'لعزيز', 'الرباط'),
(33, 'MASSAR011', 'GRESA011', 'حمزة', 'رشيد', 'الدار البيضاء'),
(34, 'MASSAR012', 'GRESA012', 'أسماء', 'زروال', 'سلا');

-- Insertion des utilisateurs de type 'enseignant'
INSERT INTO Utilisateur (nom, prenom, email, mot_de_passe, role, sexe, date_naissance, lieu_naissance, nationalite, telephone, adresse, photo, est_valide, est_actif)
VALUES
('Mohammed', 'Ben Ali', '<EMAIL>', '$2y$10$U8nL5R5v.tL7ZbP2HfBLV.fqHZjFV1sO8FSnI7kVrxj.v0LkBqVnS', 'enseignant', 'homme', '1980-01-15', 'Casablanca', 'Marocain', '0612345678', '12 Rue de l\'École, Casablanca', 'mohammed_ben_ali.jpg', TRUE, TRUE),
('Fatima', 'Zahra', '<EMAIL>', '$2y$10$JbL7TksMyi8eFzF5Ik5wXuCkTmHxa5lGG4tiDq1X75B/.f5QKQomC', 'enseignant', 'femme', '1985-02-25', 'Rabat', 'Marocain', '0623456789', '45 Rue des Roses, Rabat', 'fatima_zahra.jpg', TRUE, TRUE),
('Ahmed', 'El Mansouri', '<EMAIL>', '$2y$10$CfpLTkfgXnU8c8v2jmiqIYOw6gn0Xy2vcYo6rbxfx57g2sy6FwdQG', 'enseignant', 'homme', '1990-03-10', 'Marrakech', 'Marocain', '0634567890', '78 Rue de Marrakech, Marrakech', 'ahmed_mansouri.jpg', TRUE, TRUE),
('Nadia', 'Benkirane', '<EMAIL>', '$2y$10$JXPR1c1cyIHl3Aq7aaZG3jlM49qvS5Nmj2wdf5G1zOe0p4M1PMl1G', 'enseignant', 'femme', '1992-07-30', 'Fès', 'Marocain', '0645678901', '22 Avenue Hassan II, Fès', 'nadia_benkirane.jpg', TRUE, TRUE),
('Yassine', 'Rabat', '<EMAIL>', '$2y$10$RcsuPj6n5focqxHTzLTaD2zjLDOIT0niKhOZoGmnG3tHrHYX7XHGy', 'enseignant', 'homme', '1988-05-12', 'Tangier', 'Marocain', '0656789012', '90 Rue de Tanger, Tanger', 'yassine_rabat.jpg', TRUE, TRUE),
('Salma', 'Chakir', '<EMAIL>', '$2y$10$Rhts9OZ1jXZvnInKNh8vJxfhKTk99DN7YXyfgGbPnycWgdrwZnNzm', 'enseignant', 'femme', '1995-11-03', 'Agadir', 'Marocain', '**********', '101 Rue des Palmiers, Agadir', 'salma_chakir.jpg', TRUE, TRUE);
-- Insertion des enseignants
INSERT INTO Enseignant (id_utilisateur, num_CIN, num_CNSS, situation_familiale, nombre_enfants, date_embauche, banque, rib)
VALUES
(35, 'CIN001', 'CNSS001', 'C', 2, '2021-09-01', 'Banque Populaire', 'RIB001'),
(36, 'CIN002', 'CNSS002', 'M', 0, '2020-11-15', 'Attijariwafa Bank', 'RIB002'),
(37, 'CIN003', 'CNSS003', 'D', 1, '2018-07-10', 'Banque de l’Habitat', 'RIB003'),
(38, 'CIN004', 'CNSS004', 'V', 3, '2022-02-20', 'BMCE Bank', 'RIB004'),
(39, 'CIN005', 'CNSS005', 'M', 4, '2019-05-30', 'Société Générale', 'RIB005'),
(40, 'CIN006', 'CNSS006', 'C', 2, '2020-09-25', 'Crédit Agricole', 'RIB006');

INSERT INTO Diplome (id_enseignant, intitule, institut, specialite, date_promotion)
VALUES
(4, 'Master en Informatique', 'Université Hassan II', 'Informatique', '2020-06-15'),
(5, 'Licence en Finance', 'Université Mohamed V', 'Finance', '2018-09-10'),
(6, 'Doctorat en Mathématiques', 'Université Chouaib Doukkali', 'Mathématiques', '2016-05-22'),
(7, 'Master en Économie', 'Université Al Akhawayn', 'Économie', '2021-11-05'),
(8, 'Licence en Gestion', 'Université Cadi Ayyad', 'Gestion', '2019-03-30'),
(9, 'Doctorat en Droit', 'Université Mohammed V', 'Droit', '2015-07-14');

INSERT INTO Contrat (id_enseignant, type_contrat, poste, date_debut, date_fin, salaire_base, statut, description)
VALUES
(5, 'CDI', 'enseignant', '2021-09-01', NULL, 15000.00, 'actif', 'Enseignant permanent en informatique'),
(6, 'CDD', 'enseignant', '2020-11-15', '2021-11-15', 12000.00, 'terminé', 'Enseignant vacataire en finance'),
(7, 'CDI', 'enseignant', '2018-07-10', NULL, 16000.00, 'actif', 'Professeur titulaire en mathématiques'),
(8, 'CDI', 'enseignant', '2022-02-20', NULL, 14000.00, 'actif', 'Professeur en économie'),
(9, 'CDD', 'enseignant', '2019-05-30', '2020-05-30', 13000.00, 'terminé', 'Enseignant vacataire en gestion'),
(4, 'CDI', 'enseignant', '2020-09-25', NULL, 15500.00, 'actif', 'Professeur en droit');

-- Insertion des utilisateurs de type 'parent'
INSERT INTO Utilisateur (nom, prenom, email, 7ot_de_passe, role, sexe, date_naissance, lieu_naissance, nationalite, telephone, adresse, photo, est_valide, est_actif)
V8LUE8
('Karim', 'Bensaid', '<EMAIL>', '$2y$10$Y0XTZDzjTI2sDjkXfKJjHye2uwLKpkrPYOYQuVpa5QYgOIsjjVzM', 'parent', 'homme', '1975-12-30', 'Casablanca', 'Marocain', '0612345678', '123 Rue du Soleil, Casablanca', 'karim_bensaid.jpg', TRUE, TRUE),
('Khadija', 'El Oualidi', '<EMAIL>', '$2y$10$mlLQoPpzNRx5aFD.5xtHKtqGG7mlBdFMjtV0WRHk5UldHwZ6hNN.0', 'parent', 'femme', '1980-06-14', 'Rabat', 'Marocain', '0623456789', '45 Rue des Lilas, Rabat', 'khadija_oualidi.jpg', TRUE, TRUE),
('Ahmed', 'El Moutahari', '<EMAIL>', '$2y$10$Nl3T8k5AlmkU6teNGWbxGdfEKiqU7rPaHviOd9J1Wyep.IpLRzZ7W', 'parent', 'homme', '1983-09-21', 'Marrakech', 'Marocain', '0634567890', '78 Avenue Mohammed VI, Marrakech', 'ahmed_moutahari.jpg', TRUE, TRUE),
('Sofia', 'Zahir', '<EMAIL>', '$2y$10$vv9rGpnS27jrPSPtBOdL/OoFxxy9ZKXlgjlOwUmJZ.kXe5fF6sHk.', 'parent', 'femme', '1985-11-05', 'Fès', 'Marocain', '0645678901', '22 Rue Hassan II, Fès', 'sofia_zahir.jpg', TRUE, TRUE),
('Mohammed', 'Chahd', '<EMAIL>', '$2y$10$lh4q5eEfwINB1OqQAfTaVmG0AqzV0sy9OxjpfldOBN7UyFTu9CKWe', 'parent', 'homme', '1977-03-19', 'Agadir', 'Marocain', '0656789012', '101 Rue des Palmiers, Agadir', 'mohammed_chahd.jpg', TRUE, TRUE),
('Meriem', 'Benjelloun', '<EMAIL>', '$2y$10$0krWzQBxGQb8Hb1xU8sOGpPVD8Q0EK5zj8M2w1yEdaxptVnqCAyyK', 'parent', 'femme', '1990-04-08', 'Tangier', 'Marocain', '**********', '89 Rue de Tanger, Tanger', 'meriem_benjelloun.jpg', TRUE, TRUE),
('Youssef', 'Mansouri', '<EMAIL>', '$2y$10$7wOY4Jt8hWqR3O1QzxaU3A0bsH5K6J9wXar77A01oBy6mJ7vnUo62', 'parent', 'homme', '1976-01-22', 'Casablanca', 'Marocain', '0678901234', '33 Rue de l\'Océan, Casablanca', 'youssef_mansouri.jpg', TRUE, TRUE),
('Asmae', 'Said', '<EMAIL>', '$2y$10$HmyqSKBFL0GbA4EpxzFIZgP7bQ0Tx7V8n6ETz.s9C9BoeuZkOqAGG', 'parent', 'femme', '1988-10-30', 'Marrakech', 'Marocain', '0689012345', '120 Rue des Fleurs, Marrakech', 'asmae_said.jpg', TRUE, TRUE),
('Abdelkader', 'Amara', '<EMAIL>', '$2y$10$9h7rf6QJ4sewMoxbe5kF1yKbf0xiXwlFscMwzSH.xKh5hAKIVFZ6m', 'parent', 'homme', '1982-05-13', 'Rabat', 'Marocain', '0690123456', '56 Rue de Rabat, Rabat', 'abdelkader_amara.jpg', TRUE, TRUE);
-- Insertion des parents dans la table Parent
INSERT INTO Parent (id_utilisateur, nom_ar, prenom_ar, num_CIN)
VALUES
(41, 'كريم', 'بن سعيد', 'CIN001'),
(42, 'خديجة', 'الواليدي', 'CIN002'),
(43, 'أحمد', 'المطهري', 'CIN003'),
(44, 'سوفيا', 'ظاهر', 'CIN004'),
(45, 'محمد', 'شهاد', 'CIN005'),
(46, 'مريم', 'بن جلون', 'CIN006'),
(47, 'يوسف', 'المنصوري', 'CIN007'),
(48, 'أسماء', 'سعيد', 'CIN008'),
(49, 'عبدالقادر', 'أمارة', 'CIN009');


INSERT INTO Inscription (id_eleve, id_annee_scolaire, id_classe, date_inscription, statut)
VALUES
(34, 3, 1, '2023-09-01 10:00:00', 'Inscrit'),
(35, 3, 2, '2023-09-01 10:00:00', 'Inscrit'),
(36, 3, 3, '2023-09-01 10:00:00', 'Inscrit'),
(37, 3, 4, '2023-09-01 10:00:00', 'Inscrit'),
(38, 4, 4, '2023-09-01 10:00:00', 'Inscrit'),
(39, 4, 6, '2023-09-01 10:00:00', 'Inscrit'),
(40, 4, 7, '2023-09-01 10:00:00', 'Inscrit'),
(41, 4, 8, '2023-09-01 10:00:00', 'Inscrit'),
(42, 4, 8, '2023-09-01 10:00:00', 'Inscrit'),
(43, 4, 8, '2023-09-01 10:00:00', 'Inscrit'),
(44, 4, 1, '2024-09-01 10:00:00', 'Inscrit'),
(45, 4, 2, '2024-09-01 10:00:00', 'Inscrit');

      INSERT INTO Matiere (nom_matiere_fr, nom_matiere_ar, description)
VALUES
('Français', 'الفرنسية', 'Matière qui enseigne la langue française, la lecture et l’écriture.'),
('Arabe', 'اللغة العربية', 'Matière qui enseigne la langue arabe, la lecture et l’écriture.'),
('Mathématiques', 'الرياضيات', 'Étude des nombres, des opérations et des concepts mathématiques de base.'),
('Sciences de la vie et de la Terre', 'علوم الحياة والأرض', 'Étude des phénomènes naturels et de la biologie.'),
('Éducation Islamique', 'التربية الإسلامية', 'Enseignement des valeurs islamiques, des principes religieux et des rituels.'),
('Histoire-Géographie', 'التاريخ والجغرافيا', 'Étude des événements historiques et de la géographie du monde et du Maroc.'),
('Éducation physique et sportive', 'التربية البدنية والرياضية', 'Activités physiques visant à développer les capacités motrices et sportives des élèves.'),
('Arts plastiques', 'الفنون التشكيلية', 'Création artistique avec des matériaux variés pour développer la créativité.'),
('Musique', 'الموسيقى', 'Apprentissage des bases de la musique, du chant et des instruments.'),
('Informatique', 'العلوم الكمبيوترية', 'Introduction à l’utilisation des ordinateurs et des outils numériques.');

INSERT INTO Unite (id_matiere, nom_unite, description)
VALUES
(13, 'Unité 1 - Mathématiques', 'Introduction aux mathématiques de base, comprenant les opérations et les fonctions simples.'),
(14, 'Unité 2 - Physique', 'Étude des principes fondamentaux de la physique, notamment la mécanique et la thermodynamique.'),
(15, 'Unité 3 - Chimie', 'Exploration des réactions chimiques de base, des éléments chimiques et de la structure atomique.'),
(16, 'Unité 4 - Biologie', 'Étude de la cellule, des organismes vivants et des principes de la génétique.'),
(17, 'Unité 5 - Informatique', 'Introduction à la programmation, aux structures de données et à l’algorithmique.'),
(18, 'Unité 6 - Littérature', 'Analyse des grandes œuvres littéraires, avec un focus sur la poésie et le roman classique.'),
(19, 'Unité 7 - Histoire', 'Introduction à l’histoire des grandes civilisations et des événements marquants de l’Antiquité à l’époque moderne.'),
(20, 'Unité 8 - Géographie', 'Étude des phénomènes géographiques naturels et humains, ainsi que des enjeux environnementaux mondiaux.'),
(22, 'Unité 10 - Économie', 'Bases de l’économie, y compris la microéconomie, la macroéconomie et les concepts clés du marché.');
