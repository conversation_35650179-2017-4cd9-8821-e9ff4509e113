/**
 * Utilitaire pour gérer les couleurs des matières dans l'emploi du temps
 * Assure une cohérence visuelle à travers toute l'application
 */

export interface MatiereColorConfig {
  background: string;
  border: string;
  text: string;
  hex?: string; // Couleur hexadécimale pour d'autres usages
}

// Configuration des couleurs par matière
const MATIERE_COLORS: { [key: string]: MatiereColorConfig } = {
  // Mathématiques et sciences exactes
  'Mathématiques': {
    background: 'bg-blue-100',
    border: 'border-blue-300',
    text: 'text-blue-800',
    hex: '#3B82F6'
  },
  'Maths': {
    background: 'bg-blue-100',
    border: 'border-blue-300',
    text: 'text-blue-800',
    hex: '#3B82F6'
  },
  'Algèbre': {
    background: 'bg-blue-100',
    border: 'border-blue-300',
    text: 'text-blue-800',
    hex: '#3B82F6'
  },
  'Géométrie': {
    background: 'bg-blue-200',
    border: 'border-blue-400',
    text: 'text-blue-900',
    hex: '#2563EB'
  },
  'Physique': {
    background: 'bg-indigo-100',
    border: 'border-indigo-300',
    text: 'text-indigo-800',
    hex: '#6366F1'
  },
  'Chimie': {
    background: 'bg-purple-100',
    border: 'border-purple-300',
    text: 'text-purple-800',
    hex: '#8B5CF6'
  },
  'Sciences': {
    background: 'bg-cyan-100',
    border: 'border-cyan-300',
    text: 'text-cyan-800',
    hex: '#06B6D4'
  },
  
  // Langues
  'Français': {
    background: 'bg-green-100',
    border: 'border-green-300',
    text: 'text-green-800',
    hex: '#10B981'
  },
  'Anglais': {
    background: 'bg-red-100',
    border: 'border-red-300',
    text: 'text-red-800',
    hex: '#EF4444'
  },
  'Espagnol': {
    background: 'bg-yellow-100',
    border: 'border-yellow-300',
    text: 'text-yellow-800',
    hex: '#F59E0B'
  },
  'Allemand': {
    background: 'bg-gray-100',
    border: 'border-gray-300',
    text: 'text-gray-800',
    hex: '#6B7280'
  },
  'Arabe': {
    background: 'bg-emerald-100',
    border: 'border-emerald-300',
    text: 'text-emerald-800',
    hex: '#059669'
  },
  
  // Sciences humaines
  'Histoire': {
    background: 'bg-amber-100',
    border: 'border-amber-300',
    text: 'text-amber-800',
    hex: '#D97706'
  },
  'Géographie': {
    background: 'bg-lime-100',
    border: 'border-lime-300',
    text: 'text-lime-800',
    hex: '#65A30D'
  },
  'Histoire-Géographie': {
    background: 'bg-orange-100',
    border: 'border-orange-300',
    text: 'text-orange-800',
    hex: '#EA580C'
  },
  'Philosophie': {
    background: 'bg-violet-100',
    border: 'border-violet-300',
    text: 'text-violet-800',
    hex: '#7C3AED'
  },
  
  // Arts et activités
  'Arts': {
    background: 'bg-pink-100',
    border: 'border-pink-300',
    text: 'text-pink-800',
    hex: '#EC4899'
  },
  'Musique': {
    background: 'bg-rose-100',
    border: 'border-rose-300',
    text: 'text-rose-800',
    hex: '#F43F5E'
  },
  'EPS': {
    background: 'bg-teal-100',
    border: 'border-teal-300',
    text: 'text-teal-800',
    hex: '#14B8A6'
  },
  'Sport': {
    background: 'bg-teal-100',
    border: 'border-teal-300',
    text: 'text-teal-800',
    hex: '#14B8A6'
  },
  
  // Informatique et technologie
  'Informatique': {
    background: 'bg-slate-100',
    border: 'border-slate-300',
    text: 'text-slate-800',
    hex: '#475569'
  },
  'Technologie': {
    background: 'bg-zinc-100',
    border: 'border-zinc-300',
    text: 'text-zinc-800',
    hex: '#52525B'
  },
  
  // Économie et gestion
  'Économie': {
    background: 'bg-emerald-100',
    border: 'border-emerald-300',
    text: 'text-emerald-800',
    hex: '#059669'
  },
  'Gestion': {
    background: 'bg-green-100',
    border: 'border-green-300',
    text: 'text-green-800',
    hex: '#10B981'
  },
};

// Couleurs par défaut pour les matières non définies
const DEFAULT_COLORS: MatiereColorConfig[] = [
  { background: 'bg-blue-100', border: 'border-blue-300', text: 'text-blue-800', hex: '#3B82F6' },
  { background: 'bg-green-100', border: 'border-green-300', text: 'text-green-800', hex: '#10B981' },
  { background: 'bg-purple-100', border: 'border-purple-300', text: 'text-purple-800', hex: '#8B5CF6' },
  { background: 'bg-orange-100', border: 'border-orange-300', text: 'text-orange-800', hex: '#EA580C' },
  { background: 'bg-pink-100', border: 'border-pink-300', text: 'text-pink-800', hex: '#EC4899' },
  { background: 'bg-indigo-100', border: 'border-indigo-300', text: 'text-indigo-800', hex: '#6366F1' },
  { background: 'bg-red-100', border: 'border-red-300', text: 'text-red-800', hex: '#EF4444' },
  { background: 'bg-yellow-100', border: 'border-yellow-300', text: 'text-yellow-800', hex: '#F59E0B' },
];

/**
 * Génère un hash simple pour une chaîne de caractères
 */
function generateHash(str: string): number {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash);
  }
  return Math.abs(hash);
}

/**
 * Obtient la configuration de couleur pour une matière
 */
export function getMatiereColor(nomMatiere: string): MatiereColorConfig {
  // Si la matière a une couleur définie, l'utiliser
  if (MATIERE_COLORS[nomMatiere]) {
    return MATIERE_COLORS[nomMatiere];
  }

  // Sinon, générer une couleur basée sur le hash du nom de la matière
  const hash = generateHash(nomMatiere);
  const colorIndex = hash % DEFAULT_COLORS.length;
  
  return DEFAULT_COLORS[colorIndex];
}

/**
 * Obtient les classes CSS pour une matière (format Tailwind)
 */
export function getMatiereColorClasses(nomMatiere: string): string {
  const config = getMatiereColor(nomMatiere);
  return `${config.background} ${config.border} ${config.text}`;
}

/**
 * Obtient la couleur hexadécimale pour une matière
 */
export function getMatiereColorHex(nomMatiere: string): string {
  const config = getMatiereColor(nomMatiere);
  return config.hex || '#6B7280';
}

/**
 * Obtient toutes les matières avec leurs couleurs définies
 */
export function getAllMatiereColors(): { [key: string]: MatiereColorConfig } {
  return { ...MATIERE_COLORS };
}
