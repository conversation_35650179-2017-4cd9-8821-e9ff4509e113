import React, { useState, useEffect } from "react";
import { X, User, Users, GraduationCap, Calendar, Phone, Mail, MapPin, Hash, School, BookOpen } from "lucide-react";
import { getEleve, getParentsByEleve, getInscriptionByEleve } from "../services/api";
import type { Eleve, Parent, Relation } from "../types";

interface EleveDetailModalProps {
  eleve: Eleve;
  isOpen: boolean;
  onClose: () => void;
}

interface ParentWithRelation extends Parent {
  type_relation: string;
  user: {
    nom: string;
    prenom: string;
    email: string;
    telephone: string;
    date_naissance: string;
    lieu_naissance: string;
    sexe: string;
    nationalite: string;
    adresse: string;
  };
}

interface InscriptionDetail {
  id_classe: number;
  id_annee_scolaire: number;
  date_inscription: string;
  statut: string;
}

const EleveDetailModal: React.FC<EleveDetailModalProps> = ({ eleve, isOpen, onClose }) => {
  const [eleveDetails, setEleveDetails] = useState<Eleve | null>(null);
  const [parents, setParents] = useState<ParentWithRelation[]>([]);
  const [inscription, setInscription] = useState<InscriptionDetail | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isOpen && eleve.id_eleve) {
      loadEleveDetails();
    }
  }, [isOpen, eleve.id_eleve]);

  const loadEleveDetails = async () => {
    setLoading(true);
    try {
      // Charger les détails complets de l'élève
      const eleveResponse = await getEleve(eleve.id_eleve);
      if (eleveResponse.data.success && eleveResponse.data.data) {
        setEleveDetails(eleveResponse.data.data);
      }

      // Charger les parents
      const parentsResponse = await getParentsByEleve(eleve.id_eleve);
      if (parentsResponse.data.success && parentsResponse.data.data) {
        setParents(parentsResponse.data.data);
      }

      // Charger l'inscription
      const inscriptionResponse = await getInscriptionByEleve(eleve.id_eleve);
      if (inscriptionResponse.data.success && inscriptionResponse.data.data) {
        setInscription(inscriptionResponse.data.data);
      }
    } catch (error) {
      console.error("Erreur lors du chargement des détails:", error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return "Non renseigné";
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  const getRelationLabel = (relation: string) => {
    const relations: { [key: string]: string } = {
      'père': 'Père',
      'mère': 'Mère',
      'tuteur': 'Tuteur/Tutrice'
    };
    return relations[relation] || relation;
  };

  if (!isOpen) return null;

  const currentEleve = eleveDetails || eleve;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6 rounded-t-xl">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-3">
              <User size={32} />
              <div>
                <h2 className="text-2xl font-bold">
                  {currentEleve.user?.prenom} {currentEleve.user?.nom}
                </h2>
                <p className="text-blue-100">Fiche détaillée de l'élève</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-gray-200 transition-colors"
            >
              <X size={24} />
            </button>
          </div>
        </div>

        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Chargement des détails...</p>
          </div>
        ) : (
          <div className="p-6 space-y-8">
            {/* Informations personnelles */}
            <section>
              <h3 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                <User className="mr-2 text-blue-600" size={20} />
                Informations personnelles
              </h3>
              <div className="bg-gray-50 rounded-lg p-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <span className="font-medium">Nom (العربية):</span>
                  <span>{currentEleve.nom_ar || "Non renseigné"}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="font-medium">Prénom (العربية):</span>
                  <span>{currentEleve.prenom_ar || "Non renseigné"}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Mail className="text-gray-500" size={16} />
                  <span className="font-medium">Email:</span>
                  <span>{currentEleve.user?.email || "Non renseigné"}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Phone className="text-gray-500" size={16} />
                  <span className="font-medium">Téléphone:</span>
                  <span>{currentEleve.user?.telephone || "Non renseigné"}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Calendar className="text-gray-500" size={16} />
                  <span className="font-medium">Date de naissance:</span>
                  <span>{formatDate(currentEleve.user?.date_naissance || "")}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <MapPin className="text-gray-500" size={16} />
                  <span className="font-medium">Lieu de naissance:</span>
                  <span>{currentEleve.user?.lieu_naissance || "Non renseigné"}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="font-medium">Lieu de naissance (العربية):</span>
                  <span>{currentEleve.lieu_naissance_ar || "Non renseigné"}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="font-medium">Sexe:</span>
                  <span>{currentEleve.user?.sexe === 'garçon' ? 'Garçon' : currentEleve.user?.sexe === 'fille' ? 'Fille' : 'Non renseigné'}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="font-medium">Nationalité:</span>
                  <span>{currentEleve.user?.nationalite || "Non renseigné"}</span>
                </div>
                <div className="flex items-center space-x-2 md:col-span-2">
                  <MapPin className="text-gray-500" size={16} />
                  <span className="font-medium">Adresse:</span>
                  <span>{currentEleve.user?.adresse || "Non renseigné"}</span>
                </div>
              </div>
            </section>

            {/* Informations scolaires */}
            <section>
              <h3 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                <GraduationCap className="mr-2 text-blue-600" size={20} />
                Informations scolaires
              </h3>
              <div className="bg-gray-50 rounded-lg p-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <Hash className="text-gray-500" size={16} />
                  <span className="font-medium">Code Massar:</span>
                  <span>{currentEleve.code_massar || "Non renseigné"}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <School className="text-gray-500" size={16} />
                  <span className="font-medium">Classe actuelle:</span>
                  <span>{currentEleve.nom_classe || "Non affecté"}</span>
                </div>
                {currentEleve.code_gresa && (
                  <div className="flex items-center space-x-2">
                    <Hash className="text-gray-500" size={16} />
                    <span className="font-medium">Code GRESA:</span>
                    <span>{currentEleve.code_gresa}</span>
                  </div>
                )}
                {inscription && (
                  <>
                    <div className="flex items-center space-x-2">
                      <Calendar className="text-gray-500" size={16} />
                      <span className="font-medium">Date d'inscription:</span>
                      <span>{formatDate(inscription.date_inscription)}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium">Statut:</span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        inscription.statut === 'actif' ? 'bg-green-100 text-green-800' : 
                        inscription.statut === 'inactif' ? 'bg-red-100 text-red-800' : 
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        {inscription.statut}
                      </span>
                    </div>
                  </>
                )}
              </div>
            </section>

            {/* Ancienne école */}
            {currentEleve.ancienne_ecole && (
              <section>
                <h3 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                  <BookOpen className="mr-2 text-blue-600" size={20} />
                  École précédente
                </h3>
                <div className="bg-gray-50 rounded-lg p-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">Nom de l'école:</span>
                    <span>{currentEleve.ancienne_ecole.nom}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">Type:</span>
                    <span>{currentEleve.ancienne_ecole.type}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">Cycle:</span>
                    <span>{currentEleve.ancienne_ecole.cycle}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Hash className="text-gray-500" size={16} />
                    <span className="font-medium">Code GRESA:</span>
                    <span>{currentEleve.ancienne_ecole.code_gresa}</span>
                  </div>
                  <div className="flex items-center space-x-2 md:col-span-2">
                    <MapPin className="text-gray-500" size={16} />
                    <span className="font-medium">Adresse:</span>
                    <span>{currentEleve.ancienne_ecole.adresse}</span>
                  </div>
                </div>
              </section>
            )}

            {/* Parents */}
            <section>
              <h3 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                <Users className="mr-2 text-blue-600" size={20} />
                Parents/Tuteurs ({parents.length})
              </h3>
              {parents.length > 0 ? (
                <div className="space-y-4">
                  {parents.map((parent, index) => (
                    <div key={parent.id_parent || index} className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-semibold text-lg text-blue-700">
                          {getRelationLabel(parent.type_relation)}
                        </h4>
                        <span className="text-sm text-gray-500">CIN: {parent.num_CIN}</span>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <div className="flex items-center space-x-2">
                          <User className="text-gray-500" size={16} />
                          <span className="font-medium">Nom:</span>
                          <span>{parent.user?.prenom} {parent.user?.nom}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">Nom (العربية):</span>
                          <span>{parent.prenom_ar} {parent.nom_ar}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Mail className="text-gray-500" size={16} />
                          <span className="font-medium">Email:</span>
                          <span>{parent.user?.email || "Non renseigné"}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Phone className="text-gray-500" size={16} />
                          <span className="font-medium">Téléphone:</span>
                          <span>{parent.user?.telephone || "Non renseigné"}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Calendar className="text-gray-500" size={16} />
                          <span className="font-medium">Date de naissance:</span>
                          <span>{formatDate(parent.user?.date_naissance || "")}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">Nationalité:</span>
                          <span>{parent.user?.nationalite || "Non renseigné"}</span>
                        </div>
                        <div className="flex items-center space-x-2 md:col-span-2">
                          <MapPin className="text-gray-500" size={16} />
                          <span className="font-medium">Adresse:</span>
                          <span>{parent.user?.adresse || "Non renseigné"}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="bg-gray-50 rounded-lg p-4 text-center text-gray-500">
                  Aucun parent/tuteur enregistré
                </div>
              )}
            </section>
          </div>
        )}

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 rounded-b-xl">
          <div className="flex justify-end">
            <button
              onClick={onClose}
              className="px-6 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
            >
              Fermer
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EleveDetailModal;
