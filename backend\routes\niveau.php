<?php

require_once __DIR__ . '/../controllers/NiveauController.php';

function handleNiveauRoutes($uri, $method, $pdo) {
    $niveauController = new NiveauController($pdo);

    // GET /niveaux - Récupérer tous les niveaux
    if ($uri === '/niveaux' && $method === 'GET') {
        $niveauController->getAll();
        return true;
    }



    // GET /niveaux/{id} - Récupérer un niveau par ID
    if (preg_match('/^\/niveaux\/(\d+)$/', $uri, $matches) && $method === 'GET') {
        $id = (int)$matches[1];
        $niveauController->getById($id);
        return true;
    }

    // GET /niveaux/cycle/{cycle} - Récupérer les niveaux par cycle
    if (preg_match('/^\/niveaux\/cycle\/([^\/]+)$/', $uri, $matches) && $method === 'GET') {
        $cycle = $matches[1];
        $niveauController->getByCycle($cycle);
        return true;
    }

    // POST /niveaux - Créer un nouveau niveau
    if ($uri === '/niveaux' && $method === 'POST') {
        $niveauController->create();
        return true;
    }

    // PUT /niveaux/{id} - Mettre à jour un niveau
    if (preg_match('/^\/niveaux\/(\d+)$/', $uri, $matches) && $method === 'PUT') {
        $id = (int)$matches[1];
        $niveauController->update($id);
        return true;
    }

    // DELETE /niveaux/{id} - Supprimer un niveau
    if (preg_match('/^\/niveaux\/(\d+)$/', $uri, $matches) && $method === 'DELETE') {
        $id = (int)$matches[1];
        $niveauController->delete($id);
        return true;
    }

    return false;
}
