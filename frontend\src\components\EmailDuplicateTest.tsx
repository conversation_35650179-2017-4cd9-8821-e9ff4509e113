import React, { useState } from 'react';
import { register, updateUser } from '../services/api';
import { extractErrorMessage } from '../utils/errorUtils';
import Button from './Button';
import Input from './Input';

/**
 * Composant de test pour vérifier la gestion des emails dupliqués
 */
const EmailDuplicateTest: React.FC = () => {
  const [email, setEmail] = useState('<EMAIL>');
  const [message, setMessage] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  const testCreateDuplicate = async () => {
    setIsLoading(true);
    setMessage('');

    try {
      await register({
        nom: 'Test',
        prenom: 'Duplicate',
        email: email,
        role: 'admin',
        sexe: 'homme',
        telephone: '',
        est_actif: true
      });
      
      setMessage('✅ Utilisateur créé avec succès');
    } catch (error: any) {
      console.log('🔍 Erreur création:', error);
      
      const errorMessage = error.userMessage || extractErrorMessage(error, 'Erreur par défaut');
      setMessage(`❌ Création: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testUpdateDuplicate = async () => {
    setIsLoading(true);
    setMessage('');

    try {
      // Essayer de mettre à jour un utilisateur avec un email qui existe déjà
      await updateUser(1, {
        nom: 'Test',
        prenom: 'Update',
        email: email
      });
      
      setMessage('✅ Utilisateur mis à jour avec succès');
    } catch (error: any) {
      console.log('🔍 Erreur mise à jour:', error);
      
      const errorMessage = error.userMessage || extractErrorMessage(error, 'Erreur par défaut');
      setMessage(`❌ Mise à jour: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-md mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-xl font-bold mb-4">Test Email Dupliqué</h2>
      
      <div className="space-y-4">
        <Input
          label="Email à tester"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          placeholder="<EMAIL>"
        />
        
        <div className="grid grid-cols-2 gap-2">
          <Button 
            onClick={testCreateDuplicate}
            disabled={isLoading}
            variant="primary"
            className="text-sm"
          >
            {isLoading ? 'Test...' : 'Test Création'}
          </Button>
          
          <Button 
            onClick={testUpdateDuplicate}
            disabled={isLoading}
            variant="outline"
            className="text-sm"
          >
            {isLoading ? 'Test...' : 'Test Mise à jour'}
          </Button>
        </div>
      </div>
      
      {message && (
        <div className={`mt-4 p-4 rounded-lg ${
          message.includes('✅') 
            ? 'bg-green-50 text-green-800 border border-green-200'
            : 'bg-red-50 text-red-800 border border-red-200'
        }`}>
          {message}
        </div>
      )}
      
      <div className="mt-4 text-sm text-gray-600">
        <h3 className="font-semibold mb-2">Messages attendus :</h3>
        <ul className="list-disc list-inside space-y-1">
          <li><strong>Avant :</strong> "Request failed with status code 500"</li>
          <li><strong>Après :</strong> "Cet email est déjà utilisé par un autre utilisateur"</li>
        </ul>
      </div>
    </div>
  );
};

export default EmailDuplicateTest;
