<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configuration Email - ScolaNova</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: #2563eb;
            color: white;
            padding: 20px;
            margin: -30px -30px 30px -30px;
            border-radius: 8px 8px 0 0;
            text-align: center;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="email"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        .btn {
            background: #2563eb;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn:hover {
            background: #1d4ed8;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .alert-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .instructions {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .step {
            margin-bottom: 10px;
        }
        .current-config {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📧 Configuration Email SMTP</h1>
            <p>Configurez l'envoi automatique d'emails pour ScolaNova</p>
        </div>

        <?php
        $configFile = __DIR__ . '/../config/email.php';
        $currentConfig = [];
        
        if (file_exists($configFile)) {
            $currentConfig = require $configFile;
        }

        $message = '';
        $messageType = '';

        // Traitement du formulaire
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $gmailEmail = $_POST['gmail_email'] ?? '';
            $gmailPassword = $_POST['gmail_password'] ?? '';
            $fromName = $_POST['from_name'] ?? 'ScolaNova';
            $fromEmail = $_POST['from_email'] ?? '<EMAIL>';

            if (empty($gmailEmail) || empty($gmailPassword)) {
                $message = 'Email Gmail et mot de passe d\'application requis !';
                $messageType = 'error';
            } else {
                // Générer la nouvelle configuration
                $newConfig = "<?php

/**
 * Configuration pour le service d'envoi d'emails
 * 
 * Configuré via interface web le " . date('Y-m-d H:i:s') . "
 */

return [
    // Configuration SMTP
    'smtp' => [
        'host' => 'smtp.gmail.com',
        'port' => 587,
        'username' => '" . addslashes($gmailEmail) . "',
        'password' => '" . addslashes($gmailPassword) . "',
        'encryption' => 'tls',
    ],

    // Informations de l'expéditeur
    'from' => [
        'email' => '" . addslashes($fromEmail) . "',
        'name' => '" . addslashes($fromName) . "'
    ],

    // Templates d'emails
    'templates' => [
        'temporary_password' => [
            'subject' => 'Votre mot de passe temporaire - ScolaNova',
        ],
        'password_changed' => [
            'subject' => 'Confirmation de changement de mot de passe - ScolaNova',
        ]
    ],

    // Options générales
    'options' => [
        'charset' => 'UTF-8',
        'timeout' => 30,
        'debug' => false,
    ]
];

?>";

                if (file_put_contents($configFile, $newConfig)) {
                    $message = 'Configuration sauvegardée avec succès ! Vous pouvez maintenant tester l\'envoi d\'emails.';
                    $messageType = 'success';
                    
                    // Recharger la configuration
                    $currentConfig = require $configFile;
                    
                    // Test d'envoi optionnel
                    if (isset($_POST['send_test']) && $_POST['send_test'] === '1') {
                        try {
                            require_once __DIR__ . '/../services/EmailService.php';
                            $emailService = new EmailService();
                            
                            $testResult = $emailService->sendTemporaryPassword(
                                $gmailEmail,
                                "Test ScolaNova",
                                "TEST" . rand(100, 999)
                            );
                            
                            if ($testResult) {
                                $message .= '<br><strong>Email de test envoyé à ' . htmlspecialchars($gmailEmail) . ' !</strong>';
                            }
                        } catch (Exception $e) {
                            $message .= '<br><strong>Erreur lors du test :</strong> ' . htmlspecialchars($e->getMessage());
                        }
                    }
                } else {
                    $message = 'Erreur lors de la sauvegarde de la configuration.';
                    $messageType = 'error';
                }
            }
        }
        ?>

        <?php if ($message): ?>
            <div class="alert alert-<?= $messageType ?>">
                <?= $message ?>
            </div>
        <?php endif; ?>

        <div class="instructions">
            <h3>📋 Instructions pour Gmail :</h3>
            <div class="step">1. <strong>Connectez-vous à Gmail</strong> et allez dans les paramètres Google</div>
            <div class="step">2. <strong>Sécurité</strong> → Activez la <strong>Validation en 2 étapes</strong></div>
            <div class="step">3. <strong>Sécurité</strong> → <strong>Mots de passe des applications</strong></div>
            <div class="step">4. Sélectionnez <strong>"Mail"</strong> et générez un mot de passe</div>
            <div class="step">5. Copiez le <strong>mot de passe de 16 caractères</strong> et collez-le ci-dessous</div>
        </div>

        <?php if (!empty($currentConfig)): ?>
            <div class="current-config">
                <h4>🔧 Configuration actuelle :</h4>
                <strong>SMTP Host:</strong> <?= htmlspecialchars($currentConfig['smtp']['host'] ?? 'Non configuré') ?><br>
                <strong>Username:</strong> <?= htmlspecialchars($currentConfig['smtp']['username'] ?? 'Non configuré') ?><br>
                <strong>From Name:</strong> <?= htmlspecialchars($currentConfig['from']['name'] ?? 'Non configuré') ?><br>
                <strong>From Email:</strong> <?= htmlspecialchars($currentConfig['from']['email'] ?? 'Non configuré') ?>
            </div>
        <?php endif; ?>

        <form method="POST">
            <div class="form-group">
                <label for="gmail_email">📧 Votre adresse Gmail :</label>
                <input type="email" id="gmail_email" name="gmail_email" 
                       value="<?= htmlspecialchars($currentConfig['smtp']['username'] ?? '') ?>" 
                       placeholder="<EMAIL>" required>
            </div>

            <div class="form-group">
                <label for="gmail_password">🔑 Mot de passe d'application Gmail (16 caractères) :</label>
                <input type="password" id="gmail_password" name="gmail_password" 
                       placeholder="abcd efgh ijkl mnop" required>
                <small style="color: #666;">⚠️ Utilisez le mot de passe d'application, pas votre mot de passe Gmail normal</small>
            </div>

            <div class="form-group">
                <label for="from_name">👤 Nom de l'expéditeur :</label>
                <input type="text" id="from_name" name="from_name" 
                       value="<?= htmlspecialchars($currentConfig['from']['name'] ?? 'ScolaNova') ?>">
            </div>

            <div class="form-group">
                <label for="from_email">📨 Email de l'expéditeur :</label>
                <input type="email" id="from_email" name="from_email" 
                       value="<?= htmlspecialchars($currentConfig['from']['email'] ?? '<EMAIL>') ?>">
            </div>

            <div class="form-group">
                <label>
                    <input type="checkbox" name="send_test" value="1" checked>
                    Envoyer un email de test après la configuration
                </label>
            </div>

            <button type="submit" class="btn">💾 Sauvegarder et Tester</button>
        </form>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">
            <h4>🔗 Liens utiles :</h4>
            <p>
                <a href="view_emails.php" target="_blank">📧 Voir les emails sauvegardés</a> |
                <a href="https://myaccount.google.com/security" target="_blank">🔐 Sécurité Google</a> |
                <a href="../" target="_blank">🏠 Retour à l'application</a>
            </p>
        </div>
    </div>
</body>
</html>
