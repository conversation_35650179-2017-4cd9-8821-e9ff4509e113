<?php

require_once '../models/Niveau.php';

class NiveauController
{
    private $niveau;

    public function __construct($db)
    {
        $this->niveau = new Niveau($db);
    }

    public function create()
    {
        try {
            $data = json_decode(file_get_contents("php://input"), true);
            
            // Validation des données
            if (empty($data['cycle']) || empty($data['libelle']) || 
                !isset($data['prix_mensuel']) || !isset($data['frais_inscription'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Tous les champs sont requis']);
                return;
            }

            // Vérifier si le niveau existe déjà
            if ($this->niveau->exists($data['cycle'], $data['libelle'])) {
                http_response_code(409);
                echo json_encode(['success' => false, 'message' => 'Ce niveau existe déjà pour ce cycle']);
                return;
            }

            // Validation des prix
            if ($data['prix_mensuel'] < 0 || $data['frais_inscription'] < 0) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Les prix ne peuvent pas être négatifs']);
                return;
            }

            $result = $this->niveau->create($data);
            if ($result) {
                echo json_encode([
                    'success' => true, 
                    'data' => ['id_niveau' => $result], 
                    'message' => 'Niveau créé avec succès'
                ]);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Erreur lors de la création du niveau']);
            }
        } catch (Exception $e) {
            error_log("Erreur dans NiveauController::create: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    public function getAll()
    {
        try {
            $result = $this->niveau->getAll();
            if ($result !== false) {
                echo json_encode(['success' => true, 'data' => $result]);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Erreur lors de la récupération des niveaux']);
            }
        } catch (Exception $e) {
            error_log("Erreur dans NiveauController::getAll: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    public function getById($id)
    {
        try {
            $result = $this->niveau->getById($id);
            if ($result) {
                echo json_encode(['success' => true, 'data' => $result]);
            } else {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Niveau non trouvé']);
            }
        } catch (Exception $e) {
            error_log("Erreur dans NiveauController::getById: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    public function getByCycle($cycle)
    {
        try {
            $result = $this->niveau->getByCycle($cycle);
            if ($result !== false) {
                echo json_encode(['success' => true, 'data' => $result]);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Erreur lors de la récupération des niveaux']);
            }
        } catch (Exception $e) {
            error_log("Erreur dans NiveauController::getByCycle: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    public function update($id)
    {
        try {
            $data = json_decode(file_get_contents("php://input"), true);
            
            // Validation des données
            if (empty($data['cycle']) || empty($data['libelle']) || 
                !isset($data['prix_mensuel']) || !isset($data['frais_inscription'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Tous les champs sont requis']);
                return;
            }

            // Vérifier si le niveau existe déjà (en excluant l'ID actuel)
            if ($this->niveau->exists($data['cycle'], $data['libelle'], $id)) {
                http_response_code(409);
                echo json_encode(['success' => false, 'message' => 'Ce niveau existe déjà pour ce cycle']);
                return;
            }

            // Validation des prix
            if ($data['prix_mensuel'] < 0 || $data['frais_inscription'] < 0) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Les prix ne peuvent pas être négatifs']);
                return;
            }

            $result = $this->niveau->update($id, $data);
            if ($result) {
                echo json_encode(['success' => true, 'message' => 'Niveau mis à jour avec succès']);
            } else {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Niveau non trouvé ou aucune modification']);
            }
        } catch (Exception $e) {
            error_log("Erreur dans NiveauController::update: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    public function delete($id)
    {
        try {
            $result = $this->niveau->delete($id);
            if ($result) {
                echo json_encode(['success' => true, 'message' => 'Niveau supprimé avec succès']);
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Impossible de supprimer ce niveau (des classes y sont peut-être liées)']);
            }
        } catch (Exception $e) {
            error_log("Erreur dans NiveauController::delete: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }


}
