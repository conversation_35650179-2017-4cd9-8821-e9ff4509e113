import { genererPaiementsMensuels, getAnneesScolaires } from './api';

/**
 * Service pour la génération automatique des paiements mensuels
 */
class PaiementScheduler {
  private intervalId: NodeJS.Timeout | null = null;
  private isRunning = false;

  /**
   * D<PERSON>marre le scheduler pour vérifier chaque jour si c'est le premier du mois
   */
  start() {
    if (this.isRunning) {
      console.log("📅 Scheduler déjà en cours d'exécution");
      return;
    }

    console.log("📅 Démarrage du scheduler de paiements mensuels");
    this.isRunning = true;

    // Vérifier immédiatement
    this.checkAndGenerate();

    // Puis vérifier toutes les heures (3600000 ms)
    this.intervalId = setInterval(() => {
      this.checkAndGenerate();
    }, 3600000); // 1 heure
  }

  /**
   * Arrête le scheduler
   */
  stop() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    this.isRunning = false;
    console.log("📅 Scheduler de paiements mensuels arrêté");
  }

  /**
   * Vérifie si c'est le premier jour du mois et génère les paiements si nécessaire
   */
  private async checkAndGenerate() {
    const now = new Date();
    const day = now.getDate();
    const hour = now.getHours();

    // Vérifier si c'est le premier jour du mois entre 8h et 9h
    if (day === 1 && hour >= 8 && hour < 9) {
      console.log("📅 Premier jour du mois détecté, génération des paiements...");
      
      try {
        await this.generateMonthlyPayments();
      } catch (error) {
        console.error("❌ Erreur lors de la génération automatique:", error);
      }
    }
  }

  /**
   * Génère les paiements mensuels pour tous les types
   */
  private async generateMonthlyPayments() {
    const now = new Date();
    const mois = now.toLocaleString('fr-FR', { month: 'long' });

    try {
      // Récupérer l'année scolaire active
      const anneesResponse = await getAnneesScolaires();
      if (!anneesResponse.data.success) {
        console.error("❌ Impossible de récupérer les années scolaires");
        return;
      }

      const annees = anneesResponse.data.data;
      const anneeActive = annees.find((annee: any) => annee.est_active);

      if (!anneeActive) {
        console.error("❌ Aucune année scolaire active trouvée");
        return;
      }

      // Extraire l'année de l'année scolaire active (ex: "2024-2025" -> 2024)
      const annee = parseInt(anneeActive.libelle.split('-')[0]);

      const typesPaiement = ['scolarité', 'transport'];

      console.log(`📅 Génération des paiements pour ${mois} ${annee} (Année scolaire: ${anneeActive.libelle})`);

      for (const type of typesPaiement) {
        try {
          console.log(`📅 Génération des paiements de type: ${type}`);

          const response = await genererPaiementsMensuels(mois, annee, type);

          if (response.data.success) {
            const { paiements_crees, eleves_concernes } = response.data.data;
            console.log(`✅ ${paiements_crees} paiements de ${type} créés pour ${eleves_concernes} élèves`);

            // Optionnel: Envoyer une notification
            this.notifyGeneration(type, paiements_crees, eleves_concernes, mois, annee);
          } else {
            console.warn(`⚠️ Aucun paiement ${type} généré: ${response.data.message}`);
          }
        } catch (error: any) {
          console.error(`❌ Erreur lors de la génération des paiements ${type}:`, error);

          // Continuer avec les autres types même en cas d'erreur
          if (error.response?.status !== 409) { // 409 = Conflict (paiements déjà existants)
            console.error(`❌ Erreur inattendue pour ${type}:`, error.response?.data?.message || error.message);
          }
        }
      }
    } catch (error) {
      console.error("❌ Erreur lors de la récupération de l'année scolaire active:", error);
    }
  }

  /**
   * Notifie la génération des paiements (peut être étendu pour envoyer des emails, etc.)
   */
  private notifyGeneration(type: string, paiementsCrees: number, elevesConernes: number, mois: string, annee: number) {
    const message = `Génération automatique: ${paiementsCrees} paiements de ${type} créés pour ${elevesConernes} élèves (${mois} ${annee})`;
    
    // Log dans la console
    console.log(`🔔 ${message}`);
    
    // Optionnel: Stocker dans localStorage pour affichage dans l'interface
    const notifications = JSON.parse(localStorage.getItem('paiement_notifications') || '[]');
    notifications.push({
      id: Date.now(),
      message,
      type: 'success',
      timestamp: new Date().toISOString(),
      read: false
    });
    
    // Garder seulement les 10 dernières notifications
    if (notifications.length > 10) {
      notifications.splice(0, notifications.length - 10);
    }
    
    localStorage.setItem('paiement_notifications', JSON.stringify(notifications));
    
    // Optionnel: Déclencher un événement personnalisé pour l'interface
    window.dispatchEvent(new CustomEvent('paiementGenerated', {
      detail: { type, paiementsCrees, elevesConernes, mois, annee }
    }));
  }

  /**
   * Génère manuellement les paiements pour un mois spécifique
   */
  async generateForMonth(mois: string, annee: number, types: string[] = ['scolarité', 'transport']) {
    console.log(`📅 Génération manuelle des paiements pour ${mois} ${annee}`);
    
    const results = [];
    
    for (const type of types) {
      try {
        const response = await genererPaiementsMensuels(mois, annee, type);
        
        if (response.data.success) {
          const result = {
            type,
            success: true,
            paiements_crees: response.data.data.paiements_crees,
            eleves_concernes: response.data.data.eleves_concernes
          };
          results.push(result);
          
          this.notifyGeneration(type, result.paiements_crees, result.eleves_concernes, mois, annee);
        } else {
          results.push({
            type,
            success: false,
            error: response.data.message
          });
        }
      } catch (error: any) {
        results.push({
          type,
          success: false,
          error: error.response?.data?.message || error.message
        });
      }
    }
    
    return results;
  }

  /**
   * Vérifie le statut du scheduler
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      nextCheck: this.intervalId ? 'Dans 1 heure' : 'Arrêté'
    };
  }
}

// Instance singleton
export const paiementScheduler = new PaiementScheduler();

// Auto-démarrage si on est en production
if (process.env.NODE_ENV === 'production') {
  paiementScheduler.start();
}

export default PaiementScheduler;
