<?php

class Classe
{
    private $pdo;
    private $table = "Classe";

    public function __construct($db)
    {
        $this->pdo = $db;
    }

    public function create($data)
    {
        try {
            $query = "INSERT INTO " . $this->table . " (id_niveau, nom_classe) 
                        VALUES (:id_niveau, :nom_classe)";
            $stmt = $this->pdo->prepare($query);

            $stmt->bindParam(':id_niveau', $data['id_niveau'], PDO::PARAM_INT);
            $stmt->bindParam(':nom_classe', $data['nom_classe'], PDO::PARAM_STR);

            if ($stmt->execute()) {
                return $this->pdo->lastInsertId();
            } else {
                error_log("Erreur SQL Classe : " . implode(", ", $stmt->errorInfo()));
                return false;
            }
        } catch (PDOException $e) {
            error_log("Erreur PDO dans Classe::create : " . $e->getMessage());
            error_log("Données reçues : " . json_encode($data));
            return false;
        }
    }

    public function getAll()
    {
        try {
            $query = "SELECT c.*, n.libelle as niveau_libelle, n.cycle,
                    COUNT(i.id_eleve) as nombre_eleves FROM " . $this->table . " c
                    LEFT JOIN Niveau n ON c.id_niveau = n.id_niveau
                    LEFT JOIN Inscription i ON c.id_classe = i.id_classe
                    GROUP BY c.id_classe
                    ORDER BY n.cycle, n.libelle, c.nom_classe";

            $stmt = $this->pdo->prepare($query);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur dans getAll: " . $e->getMessage());
            return false;
        }
    }

    public function getById($id)
    {
        try {
            $query = "SELECT c.*, n.libelle as niveau_libelle, n.cycle,
                    COUNT(i.id_eleve) as nombre_eleves FROM " . $this->table . " c
                    LEFT JOIN Niveau n ON c.id_niveau = n.id_niveau
                    LEFT JOIN Inscription i ON c.id_classe = i.id_classe
                    WHERE c.id_classe = :id
                    GROUP BY c.id_classe";
            $stmt = $this->pdo->prepare($query);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return $result ? $result : false;
        } catch (PDOException $e) {
            error_log("Erreur dans getById: " . $e->getMessage());
            return false;
        }
    }

    public function getByNiveau($idNiveau)
    {
        try {
            $query = "SELECT c.*, n.libelle as niveau_libelle, n.cycle,
                    COUNT(i.id_eleve) as nombre_eleves FROM " . $this->table . " c
                    LEFT JOIN Niveau n ON c.id_niveau = n.id_niveau
                    LEFT JOIN Inscription i ON c.id_classe = i.id_classe
                    WHERE c.id_niveau = :id_niveau
                    GROUP BY c.id_classe
                    ORDER BY c.nom_classe";
            $stmt = $this->pdo->prepare($query);
            $stmt->bindParam(':id_niveau', $idNiveau, PDO::PARAM_INT);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur dans getByNiveau: " . $e->getMessage());
            return false;
        }
    }

    public function update($id, $data)
    {
        try {
            $query = "UPDATE " . $this->table . " SET 
                     id_niveau = :id_niveau,
                     nom_classe = :nom_classe
                     WHERE id_classe = :id";
            $stmt = $this->pdo->prepare($query);

            $stmt->bindParam(':id_niveau', $data['id_niveau'], PDO::PARAM_INT);
            $stmt->bindParam(':nom_classe', $data['nom_classe'], PDO::PARAM_STR);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);

            if ($stmt->execute()) {
                return $stmt->rowCount() > 0;
            } else {
                error_log("Erreur SQL Update Classe : " . implode(", ", $stmt->errorInfo()));
                return false;
            }
        } catch (PDOException $e) {
            error_log("Erreur PDO dans Classe::update : " . $e->getMessage());
            return false;
        }
    }

    public function delete($id)
    {
        try {
            // Vérifier s'il y a des inscriptions liées à cette classe
            $query = "SELECT COUNT(*) FROM Inscription WHERE id_classe = :id";
            $checkStmt = $this->pdo->prepare($query);
            $checkStmt->bindParam(':id', $id, PDO::PARAM_INT);
            $checkStmt->execute();
            $count = $checkStmt->fetchColumn();

            if ($count > 0) {
                error_log("Impossible de supprimer la classe $id : des élèves y sont inscrits");
                return false;
            }

            $stmt = $this->pdo->prepare("DELETE FROM " . $this->table . " WHERE id_classe = :id");
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            
            if ($stmt->execute()) {
                return $stmt->rowCount() > 0;
            } else {
                error_log("Erreur SQL Delete Classe : " . implode(", ", $stmt->errorInfo()));
                return false;
            }
        } catch (PDOException $e) {
            error_log("Erreur PDO dans Classe::delete : " . $e->getMessage());
            return false;
        }
    }

    public function exists($idNiveau, $nomClasse, $excludeId = null)
    {
        try {
            // Vérifier si la classe existe déjà pour ce niveau
            $sql = "SELECT COUNT(*) FROM " . $this->table . " 
                    WHERE id_niveau = :id_niveau AND nom_classe = :nom_classe";
            if ($excludeId) {
                $sql .= " AND id_classe != :excludeId";
            }
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':id_niveau', $idNiveau, PDO::PARAM_INT);
            $stmt->bindParam(':nom_classe', $nomClasse, PDO::PARAM_STR);
            if ($excludeId) {
                $stmt->bindParam(':excludeId', $excludeId, PDO::PARAM_INT);
            }
            
            $stmt->execute();
            return $stmt->fetchColumn() > 0;
        } catch (PDOException $e) {
            error_log("Erreur dans exists: " . $e->getMessage());
            return false;
        }
    }

    public function getEleves($idClasse)
    {
        try {
            // Récupérer les élèves inscrits dans la classe
            $query = "SELECT e.*, u.nom, u.prenom, u.email, u.telephone, i.date_inscription, i.statut
                      FROM Inscription i
                      JOIN Eleve e ON i.id_eleve = e.id_eleve
                      JOIN Utilisateur u ON e.id_utilisateur = u.id_utilisateur
                      WHERE i.id_classe = :id_classe
                      ORDER BY u.nom, u.prenom";
            $stmt = $this->pdo->prepare($query);
            $stmt->bindParam(':id_classe', $idClasse, PDO::PARAM_INT);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur dans getEleves: " . $e->getMessage());
            return false;
        }
    }


}
