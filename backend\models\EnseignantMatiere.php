<?php

class EnseignantMatiere {
    private $pdo;

    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    /**  <PERSON><PERSON><PERSON><PERSON><PERSON> toutes les matières d'un enseignant */
    public function getMatieresEnseignant($id_enseignant) {
        try {
            $sql = "SELECT m.id_matiere, m.nom_matiere_fr, m.nom_matiere_ar, m.description 
                    FROM Enseignant_Matiere em 
                    JOIN Matiere m ON em.id_matiere = m.id_matiere 
                    WHERE em.id_enseignant = :id_enseignant";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':id_enseignant', $id_enseignant, PDO::PARAM_INT);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur lors de la récupération des matières de l'enseignant: " . $e->getMessage());
            throw $e;
        }
    }

    /**Affecter des matières à un enseignant (création) */
    public function assignMatieresEnseignant($id_enseignant, $matieres) {
        try {
            $this->pdo->beginTransaction(); //désactive temporairement l’auto-commit.

            // Supprimer toutes les affectations existantes
            $this->deleteMatieresEnseignant($id_enseignant);

            // Ajouter les nouvelles affectations
            if (!empty($matieres)) {
                $sql = "INSERT INTO Enseignant_Matiere (id_enseignant, id_matiere) 
                        VALUES (:id_enseignant, :id_matiere)";
                $stmt = $this->pdo->prepare($sql);

                foreach ($matieres as $id_matiere) {
                    $stmt->bindParam(':id_enseignant', $id_enseignant, PDO::PARAM_INT);
                    $stmt->bindParam(':id_matiere', $id_matiere, PDO::PARAM_INT);
                    $stmt->execute();
                }
            }

            $this->pdo->commit(); // Valide toutes les modifications
            return true;
        } catch (PDOException $e) {
            $this->pdo->rollBack();  // Annule tout si un problème survient
            error_log("Erreur lors de l'affectation des matières: " . $e->getMessage());
            throw $e;
        }
    }

    /** Mettre à jour les matières d'un enseignant (modification) */
    public function updateMatieresEnseignant($id_enseignant, $matieres) {
        // Même logique que assignMatieresEnseignant
        return $this->assignMatieresEnseignant($id_enseignant, $matieres);
    }

    /** Supprimer toutes les affectations d'un enseignant  */
    public function deleteMatieresEnseignant($id_enseignant) {
        try {
            $sql = "DELETE FROM Enseignant_Matiere WHERE id_enseignant = :id_enseignant";
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':id_enseignant', $id_enseignant, PDO::PARAM_INT);
            $stmt->execute();
            
            return true;
        } catch (PDOException $e) {
            error_log("Erreur lors de la suppression des matières de l'enseignant: " . $e->getMessage());
            throw $e;
        }
    }

    /**  Vérifier si une affectation existe */
    public function affectationExists($id_enseignant, $id_matiere) {
        try {
            $sql = "SELECT COUNT(*) FROM Enseignant_Matiere 
                    WHERE id_enseignant = :id_enseignant AND id_matiere = :id_matiere";
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':id_enseignant', $id_enseignant, PDO::PARAM_INT);
            $stmt->bindParam(':id_matiere', $id_matiere, PDO::PARAM_INT);
            $stmt->execute();
            
            return $stmt->fetchColumn() > 0;
        } catch (PDOException $e) {
            error_log("Erreur lors de la vérification de l'affectation: " . $e->getMessage());
            throw $e;
        }
    }

    /**Récupérer tous les enseignants d'une matière  */
    public function getEnseignantsMatiere($id_matiere) {
        try {
            $sql = "SELECT e.id_enseignant, u.nom, u.prenom, u.email 
                    FROM Enseignant_Matiere em 
                    JOIN Enseignant e ON em.id_enseignant = e.id_enseignant 
                    JOIN Utilisateur u ON e.id_utilisateur = u.id_utilisateur 
                    WHERE em.id_matiere = :id_matiere";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':id_matiere', $id_matiere, PDO::PARAM_INT);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur lors de la récupération des enseignants de la matière: " . $e->getMessage());
            throw $e;
        }
    }
}

?>
