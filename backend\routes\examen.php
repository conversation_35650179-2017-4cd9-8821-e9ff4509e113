<?php

require_once __DIR__ . '/../controllers/ExamenController.php';
require_once __DIR__ . '/../controllers/NoteController.php';

function handleExamenRoutes($uri, $method, $pdo) {
    $examenController = new ExamenController($pdo);

    // GET /examens - Récupérer tous les examens
    if ($uri === '/examens' && $method === 'GET') {
        $examenController->getAll();
        return true;
    }

    // GET /examens/{id} - Récupérer un examen par ID
    if (preg_match('/^\/examens\/(\d+)$/', $uri, $matches) && $method === 'GET') {
        $id = (int)$matches[1];
        $examenController->getById($id);
        return true;
    }

    // POST /examens - Créer un nouvel examen
    if ($uri === '/examens' && $method === 'POST') {
        $examenController->create();
        return true;
    }

    // PUT /examens/{id} - Mettre à jour un examen
    if (preg_match('/^\/examens\/(\d+)$/', $uri, $matches) && $method === 'PUT') {
        $id = (int)$matches[1];
        $examenController->update($id);
        return true;
    }

    // DELETE /examens/{id} - Supprimer un examen
    if (preg_match('/^\/examens\/(\d+)$/', $uri, $matches) && $method === 'DELETE') {
        $id = (int)$matches[1];
        $examenController->delete($id);
        return true;
    }

    // GET /examens/{id}/notes - Récupérer les notes d'un examen
    if (preg_match('/^\/examens\/(\d+)\/notes$/', $uri, $matches) && $method === 'GET') {
        $id = (int)$matches[1];
        $noteController = new NoteController($pdo);
        $noteController->getByExamen($id);
        return true;
    }

    // GET /eleves/{id}/notes - Récupérer les notes d'un élève
    if (preg_match('/^\/eleves\/(\d+)\/notes$/', $uri, $matches) && $method === 'GET') {
        $id = (int)$matches[1];
        $noteController = new NoteController($pdo);
        $noteController->getByEleve($id);
        return true;
    }

    return false;
}

function handleNoteRoutes($uri, $method, $pdo) {
    $noteController = new NoteController($pdo);

    // GET /notes - Récupérer toutes les notes
    if ($uri === '/notes' && $method === 'GET') {
        $noteController->getAll();
        return true;
    }

    // POST /notes - Créer une nouvelle note
    if ($uri === '/notes' && $method === 'POST') {
        $noteController->create();
        return true;
    }

    // PUT /notes/{id_eleve}/{id_examen} - Mettre à jour une note
    if (preg_match('/^\/notes\/(\d+)\/(\d+)$/', $uri, $matches) && $method === 'PUT') {
        $id_eleve = (int)$matches[1];
        $id_examen = (int)$matches[2];
        $noteController->update($id_eleve, $id_examen);
        return true;
    }

    // DELETE /notes/{id_eleve}/{id_examen} - Supprimer une note
    if (preg_match('/^\/notes\/(\d+)\/(\d+)$/', $uri, $matches) && $method === 'DELETE') {
        $id_eleve = (int)$matches[1];
        $id_examen = (int)$matches[2];
        $noteController->delete($id_eleve, $id_examen);
        return true;
    }

    return false;
}
?>
