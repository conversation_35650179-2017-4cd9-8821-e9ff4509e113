import React from 'react';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Legend, Tooltip } from 'recharts';
import Card from '../Card';
import { Users } from 'lucide-react';

interface GenderData {
  genre: string;
  nombre: number;
  pourcentage: number;
}

interface GenderChartProps {
  data: GenderData[];
  loading?: boolean;
}

const GenderChart: React.FC<GenderChartProps> = ({ data, loading }) => {
  const COLORS = {
    'M': '#3B82F6', // Bleu pour les garçons
    'F': '#EC4899', // Rose pour les filles
    'Masculin': '#3B82F6',
    'Féminin': '#EC4899'
  };

  const formatData = data.map(item => ({
    name: item.genre === 'M' ? 'Garçons' : item.genre === 'F' ? 'Filles' : item.genre,
    value: item.nombre,
    pourcentage: item.pourcentage,
    color: COLORS[item.genre as keyof typeof COLORS] || '#6B7280'
  }));

  const renderCustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: any) => {
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text 
        x={x} 
        y={y} 
        fill="white" 
        textAnchor={x > cx ? 'start' : 'end'} 
        dominantBaseline="central"
        fontSize="12"
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900">{data.name}</p>
          <p className="text-sm text-gray-600">
            Nombre: <span className="font-medium">{data.value}</span>
          </p>
          <p className="text-sm text-gray-600">
            Pourcentage: <span className="font-medium">{data.pourcentage}%</span>
          </p>
        </div>
      );
    }
    return null;
  };

  if (loading) {
    return (
      <Card>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Users className="w-5 h-5 text-blue-600" />
            <h3 className="text-lg font-medium text-gray-900">Répartition par genre</h3>
          </div>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </Card>
    );
  }

  const total = data.reduce((sum, item) => sum + item.nombre, 0);

  return (
    <Card>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Users className="w-5 h-5 text-blue-600" />
          <h3 className="text-lg font-medium text-gray-900">Répartition par genre</h3>
        </div>
        <div className="text-sm text-gray-500">
          Total: {total} élèves
        </div>
      </div>

      {data.length === 0 ? (
        <div className="flex items-center justify-center h-64 text-gray-500">
          <div className="text-center">
            <Users className="w-12 h-12 mx-auto mb-2 text-gray-300" />
            <p>Aucune donnée disponible</p>
          </div>
        </div>
      ) : (
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={formatData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={renderCustomLabel}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {formatData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
              <Legend 
                verticalAlign="bottom" 
                height={36}
                formatter={(value, entry: any) => (
                  <span style={{ color: entry.color }}>
                    {value} ({entry.payload.value})
                  </span>
                )}
              />
            </PieChart>
          </ResponsiveContainer>
        </div>
      )}

      {/* Statistiques détaillées */}
      <div className="mt-4 grid grid-cols-2 gap-4">
        {formatData.map((item, index) => (
          <div key={index} className="text-center p-3 bg-gray-50 rounded-lg">
            <div 
              className="w-4 h-4 rounded-full mx-auto mb-1"
              style={{ backgroundColor: item.color }}
            ></div>
            <div className="text-sm font-medium text-gray-900">{item.name}</div>
            <div className="text-lg font-bold" style={{ color: item.color }}>
              {item.value}
            </div>
            <div className="text-xs text-gray-500">{item.pourcentage}%</div>
          </div>
        ))}
      </div>
    </Card>
  );
};

export default GenderChart;
