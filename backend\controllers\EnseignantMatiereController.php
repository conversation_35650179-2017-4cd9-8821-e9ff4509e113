<?php

require_once __DIR__ . '/../models/EnseignantMatiere.php';

class EnseignantMatiereController {
    private $enseignantMatiereModel;

    public function __construct($pdo) {
        $this->enseignantMatiereModel = new EnseignantMatiere($pdo);
    }

    /* Récupérer toutes les matières d'un enseignant */
    public function getMatieresEnseignant($id_enseignant) {
        try {
            if (!is_numeric($id_enseignant) || $id_enseignant <= 0) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'ID enseignant invalide'
                ]);
                return;
            }

            $matieres = $this->enseignantMatiereModel->getMatieresEnseignant($id_enseignant);

            echo json_encode([
                'success' => true,
                'data' => $matieres,
                'message' => 'Matières récupérées avec succès'
            ]);

        } catch (Exception $e) {
            error_log("Erreur dans getMatieresEnseignant: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la récupération des matières: ' . $e->getMessage()
            ]);
        }
    }

    /**  Affecter des matières à un enseignant */
    public function assignMatieresEnseignant($id_enseignant) {
        try {
            if (!is_numeric($id_enseignant) || $id_enseignant <= 0) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'ID enseignant invalide'
                ]);
                return;
            }

            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!isset($input['matieres']) || !is_array($input['matieres'])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Liste des matières requise'
                ]);
                return;
            }

            $matieres = $input['matieres'];

            // Valider que tous les IDs de matières sont numériques
            foreach ($matieres as $id_matiere) {
                if (!is_numeric($id_matiere) || $id_matiere <= 0) {
                    http_response_code(400);
                    echo json_encode([
                        'success' => false,
                        'message' => 'ID matière invalide: ' . $id_matiere
                    ]);
                    return;
                }
            }

            $result = $this->enseignantMatiereModel->assignMatieresEnseignant($id_enseignant, $matieres);

            if ($result) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Matières affectées avec succès'
                ]);
            } else {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de l\'affectation des matières'
                ]);
            }

        } catch (Exception $e) {
            error_log("Erreur dans assignMatieresEnseignant: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de l\'affectation des matières: ' . $e->getMessage()
            ]);
        }
    }

    /* Mettre à jour les matières d'un enseignant */
    public function updateMatieresEnseignant($id_enseignant) {
        try {
            // Valider l'ID enseignant
            if (!is_numeric($id_enseignant) || $id_enseignant <= 0) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'ID enseignant invalide'
                ]);
                return;
            }

            // Récupérer les données JSON
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!isset($input['matieres']) || !is_array($input['matieres'])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Liste des matières requise'
                ]);
                return;
            }

            $matieres = $input['matieres'];

            // Valider que tous les IDs de matières sont numériques
            foreach ($matieres as $id_matiere) {
                if (!is_numeric($id_matiere) || $id_matiere <= 0) {
                    http_response_code(400);
                    echo json_encode([
                        'success' => false,
                        'message' => 'ID matière invalide: ' . $id_matiere
                    ]);
                    return;
                }
            }

            $result = $this->enseignantMatiereModel->updateMatieresEnseignant($id_enseignant, $matieres);

            if ($result) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Matières mises à jour avec succès'
                ]);
            } else {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la mise à jour des matières'
                ]);
            }

        } catch (Exception $e) {
            error_log("Erreur dans updateMatieresEnseignant: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la mise à jour des matières: ' . $e->getMessage()
            ]);
        }
    }

    /* Supprimer toutes les affectations d'un enseignant */
    public function deleteMatieresEnseignant($id_enseignant) {
        try {
            if (!is_numeric($id_enseignant) || $id_enseignant <= 0) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'ID enseignant invalide'
                ]);
                return;
            }

            $result = $this->enseignantMatiereModel->deleteMatieresEnseignant($id_enseignant);

            if ($result) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Affectations supprimées avec succès'
                ]);
            } else {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la suppression des affectations'
                ]);
            }

        } catch (Exception $e) {
            error_log("Erreur dans deleteMatieresEnseignant: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la suppression des affectations: ' . $e->getMessage()
            ]);
        }
    }

    /* Récupérer tous les enseignants d'une matière  */
    public function getEnseignantsMatiere($id_matiere) {
        try {
            if (!is_numeric($id_matiere) || $id_matiere <= 0) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'ID matière invalide'
                ]);
                return;
            }

            $enseignants = $this->enseignantMatiereModel->getEnseignantsMatiere($id_matiere);

            echo json_encode([
                'success' => true,
                'data' => $enseignants,
                'message' => 'Enseignants récupérés avec succès'
            ]);

        } catch (Exception $e) {
            error_log("Erreur dans getEnseignantsMatiere: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur lors de la récupération des enseignants: ' . $e->getMessage()
            ]);
        }
    }
}

?>
