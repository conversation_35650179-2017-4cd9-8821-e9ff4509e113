<?php

require_once __DIR__ . '/../controllers/InscriptionController.php';

function handleInscriptionRoutes($uri, $method, $pdo) {
    $controller = new InscriptionController($pdo);

    if ($uri === '/inscriptions') {
        if ($method === 'GET') {
            $controller->getAll();
            return true;
        } elseif ($method === 'POST') {
            $controller->create();
            return true;
        }
    } elseif (preg_match('#^/inscriptions/eleve/(\d+)$#', $uri, $matches)) {
        $id_eleve = (int)$matches[1];
        if ($method === 'GET') {
            $controller->getInscriptionByEleve($id_eleve);
            return true;
        } elseif ($method === 'PUT') {
            $controller->updateByEleve($id_eleve);
            return true;
        }
    } elseif (preg_match('#^/inscriptions/(\d+)$#', $uri, $matches)) {
        $id = (int)$matches[1];
        if ($method === 'PUT') {
            $controller->update($id);
            return true;
        } elseif ($method === 'DELETE') {
            $controller->delete($id);
            return true;
        }
    }
    return false;
}
