<?php

class Database {
    private $host = "localhost";
    private $db_name = "ScolaNova";
    private $username = "root";
    private $password = "root";
    public $pdo;

    public function getConnection() {
        $this->pdo = null;  
        try {
            $this->pdo = new PDO("mysql:host=$this->host;dbname=$this->db_name;charset=utf8",
            $this->username, $this->password );
            $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch(PDOException $exception) {
            // echo "Erreur de connexion: " . $exception->getMessage();
            http_response_code(500);
            echo json_encode(["error" => "Erreur de connexion à la base de données"]);
            exit;
        }
        
        return $this->pdo;
    }
}
?>
