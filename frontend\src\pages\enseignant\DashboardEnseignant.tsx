import React, { useState, useEffect } from 'react';
import { Clock, Users, ClipboardCheck, CalendarClock, Activity, AlertCircle, User, Award } from 'lucide-react';
import Card from '../../components/Card';
import Button from '../../components/Button';
// import SaisieNotesIndividuelle from '../../components/SaisieNotesIndividuelle';
import { getEleves, getActivites, getEnseignant, getElevesEnseignant, getClassesEnseignant, getEnseignants, getCoursAujourdhuiEnseignant } from '../../services/api';
import type { Eleve, Activite } from '../../types';

interface DashboardStats {
  total_eleves: number;
  classes_enseignees: number;
  activites_encadrees: number;
  moyenne_classe: number;
}

const DashboardEnseignant: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [eleves, setEleves] = useState<Eleve[]>([]);
  const [activites, setActivites] = useState<Activite[]>([]);
  const [coursAujourdhui, setCoursAujourdhui] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>("");
  const [successMessage, setSuccessMessage] = useState<string>("");
  const [errorMessage, setErrorMessage] = useState<string>("");

  // Données simulées de l'enseignant connecté
  const user = JSON.parse(localStorage.getItem("user") || '{}');


  const enseignant = {
    id: user.id_utilisateur,
    nom: user.nom,
    prenom: user.prenom,
    matiere: "Mathématiques",
    classes: ["6ème A", "6ème B", "5ème A"]
  };

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    setLoading(true);
    setError("");

    try {
      // Récupérer l'ID de l'enseignant connecté
      const userId = user?.id_utilisateur;
      if (!userId) {
        throw new Error("Utilisateur non connecté");
      }

      // D'abord, récupérer les données de l'enseignant pour avoir son ID
      const enseignantsRes = await getEnseignants();
      const enseignantsData = enseignantsRes.data?.data || [];

      // Trouver l'enseignant correspondant à l'utilisateur connecté
      const enseignantConnecte = enseignantsData.find((ens: any) => ens.id_utilisateur === userId);

      if (!enseignantConnecte) {
        throw new Error("Enseignant non trouvé");
      }

      const idEnseignant = enseignantConnecte.id_enseignant;

      // Récupérer les données spécifiques à l'enseignant
      const [elevesRes, classesRes, activitesRes, coursRes] = await Promise.all([
        getElevesEnseignant(idEnseignant),
        getClassesEnseignant(idEnseignant),
        getActivites(),
        getCoursAujourdhuiEnseignant(idEnseignant)
      ]);

      const mesEleves = elevesRes.data?.data || [];
      const mesClasses = classesRes.data?.data || [];
      const activitesData = activitesRes.data?.data || [];
      const coursData = coursRes.data?.data || [];

      setEleves(mesEleves);
      setActivites(activitesData.slice(0, 3));
      setCoursAujourdhui(coursData);

      // Calculer les statistiques
      setStats({
        total_eleves: mesEleves.length,
        classes_enseignees: mesClasses.length,
        activites_encadrees: 0, // À récupérer via API
        moyenne_classe: 0 // À récupérer via API
      });

    } catch (error) {
      console.error("Erreur lors du chargement du dashboard:", error);
      setError("Erreur lors du chargement des données");
    } finally {
      setLoading(false);
    }
  };

  const handleSuccess = (message: string) => {
    setSuccessMessage(message);
    setTimeout(() => setSuccessMessage(""), 5000);
  };

  const handleError = (message: string) => {
    setErrorMessage(message);
    setTimeout(() => setErrorMessage(""), 5000);
  };

  const StatCard = ({ title, value, subtitle, icon: Icon, color, loading }: {
    title: string;
    value: string | number;
    subtitle?: string;
    icon: any;
    color: 'blue' | 'green' | 'purple' | 'orange';
    loading?: boolean;
  }) => {
    const colorClasses = {
      blue: 'bg-blue-50 text-blue-600',
      green: 'bg-green-50 text-green-600',
      purple: 'bg-purple-50 text-purple-600',
      orange: 'bg-orange-50 text-orange-600'
    };

    if (loading) {
      return (
        <Card>
          <div className="animate-pulse">
            <div className="flex items-center justify-between">
              <div>
                <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-16 mb-1"></div>
                <div className="h-3 bg-gray-200 rounded w-20"></div>
              </div>
              <div className="p-3 rounded-lg bg-gray-100">
                <div className="w-6 h-6 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>
        </Card>
      );
    }

    return (
      <Card>
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
            <p className="text-2xl font-bold text-gray-900 mb-1">
              {typeof value === 'number' ? value.toLocaleString() : value}
            </p>
            {subtitle && (
              <p className="text-sm text-gray-500">{subtitle}</p>
            )}
          </div>
          <div className={`p-3 rounded-lg ${colorClasses[color]}`}>
            <Icon className="w-6 h-6" />
          </div>
        </div>
      </Card>
    );
  };

  if (error) {
    return (
      <div className="p-6">
        <Card>
          <div className="flex items-center justify-center py-8 text-red-600">
            <AlertCircle className="w-8 h-8 mr-2" />
            <span>{error}</span>
          </div>
        </Card>
      </div>
    );
  }

  const today = new Date().toLocaleDateString('fr-FR', { 
    weekday: 'long', 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  });

  return (
    <div className="p-6 space-y-6">
      {/* En-tête de bienvenue */}
      <Card className="bg-gradient-to-r from-[#005bac] to-blue-600 text-white">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-2xl font-bold">Bonjour {enseignant.prenom.charAt(0).toUpperCase() +  enseignant.prenom.slice(1).toLowerCase()} !</h1>
            <p className="mt-1 text-blue-100">
              {today} • {enseignant.matiere}
            </p>
          </div>
          
          <div className="mt-4 md:mt-0 flex flex-wrap gap-2">
            <div className="bg-white/10 rounded-md px-4 py-2 backdrop-blur-sm text-center">
              <p className="text-xs text-blue-100">Classes</p>
              <p className="font-bold">{stats?.classes_enseignees || 0}</p>
            </div>
            <div className="bg-white/10 rounded-md px-4 py-2 backdrop-blur-sm text-center">
              <p className="text-xs text-blue-100">Élèves</p>
              <p className="font-bold">{stats?.total_eleves || 0}</p>
            </div>
            <div className="bg-white/10 rounded-md px-4 py-2 backdrop-blur-sm text-center">
              <p className="text-xs text-blue-100">Moyenne</p>
              <p className="font-bold">{stats?.moyenne_classe || 0}/20</p>
            </div>
          </div>
        </div>
      </Card>



      {/* Emploi du temps et élèves */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Emploi du temps du jour */}
        <Card>
          <div className="flex items-center gap-2 mb-4">
            <CalendarClock className="w-5 h-5 text-blue-600" />
            <h3 className="text-lg font-medium text-gray-900">Cours d'aujourd'hui</h3>
          </div>
          
          <div className="space-y-3">
            {coursAujourdhui.length > 0 ? (
              coursAujourdhui.map((cours, index) => (
                <div key={index} className="border-l-4 p-3 rounded-r-lg border-blue-500 bg-blue-50">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-gray-900">{cours.nom_matiere_fr} - {cours.nom_classe}</h4>
                      <p className="text-sm text-gray-600">{cours.nom_unite} • Salle {cours.nom_salle}</p>
                    </div>
                    <div className="text-sm font-medium text-blue-600">
                      {cours.heure_debut} - {cours.heure_fin}
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-6 text-gray-500">
                <CalendarClock className="mx-auto mb-2 w-8 h-8" />
                <p>Aucun cours programmé aujourd'hui</p>
              </div>
            )}
          </div>
        </Card>

        {/* Élèves récents */}
        <Card>
          <div className="flex items-center gap-2 mb-4">
            <Users className="w-5 h-5 text-green-600" />
            <h3 className="text-lg font-medium text-gray-900">Mes élèves</h3>
          </div>
          
          {loading ? (
            <div className="space-y-3">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-16 bg-gray-200 rounded-lg"></div>
                </div>
              ))}
            </div>
          ) : eleves.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Users className="w-12 h-12 mx-auto mb-2 text-gray-300" />
              <p>Aucun élève trouvé</p>
            </div>
          ) : (
            <div className="space-y-3">
              {eleves.slice(0, 5).map((eleve) => (
                <div key={eleve.id_eleve} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                      <User className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">{eleve.user?.nom} {eleve.user?.prenom}</h4>
                      <p className="text-sm text-gray-600">Classe: {(eleve as any).nom_classe}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-blue-600">
                      {eleve.user?.email}
                    </div>
                    <p className="text-xs text-gray-500">Email</p>
                  </div>
                </div>
              ))}
              
              <Button variant="outline" className="w-full">
                Voir tous les élèves
              </Button>
            </div>
          )}
        </Card>
      </div>

      {/* Activités encadrées */}
      <Card>
        <div className="flex items-center gap-2 mb-4">
          <Activity className="w-5 h-5 text-purple-600" />
          <h3 className="text-lg font-medium text-gray-900">Activités que j'encadre</h3>
        </div>
        
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="h-24 bg-gray-200 rounded-lg"></div>
              </div>
            ))}
          </div>
        ) : activites.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Activity className="w-12 h-12 mx-auto mb-2 text-gray-300" />
            <p>Aucune activité encadrée</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {activites.map((activite) => (
              <div key={activite.id_activite} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{activite.nom_activite}</h4>
                    <p className="text-sm text-gray-600 capitalize">{activite.type_activite}</p>
                    <p className="text-xs text-gray-500 mt-1">
                      {activite.nombre_participants || 0} participants
                    </p>
                  </div>
                  <Button size="sm" variant="outline">
                    Gérer
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </Card>

      {/* Messages de succès/erreur */}
      {successMessage && (
        <div className="fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50">
          <div className="flex items-center gap-2">
            <Award className="w-5 h-5" />
            {successMessage}
          </div>
        </div>
      )}

      {errorMessage && (
        <div className="fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50">
          <div className="flex items-center gap-2">
            <AlertCircle className="w-5 h-5" />
            {errorMessage}
          </div>
        </div>
      )}

     
    </div>
  );
};

export default DashboardEnseignant;
