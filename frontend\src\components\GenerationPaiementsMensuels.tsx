import React, { useState, useEffect } from "react";
import { Calendar, DollarSign, Users, AlertCircle, CheckCircle, Play } from "lucide-react";
import Button from "./Button";
import { genererPaiementsMensuels, verifierPaiementsMensuels, getAnneesScolaires } from "../services/api";
import type { AnneeScolaire } from "../types";

interface GenerationPaiementsMensuelsProps {
  onGenerated: () => void;
  onClose: () => void;
}

const GenerationPaiementsMensuels: React.FC<GenerationPaiementsMensuelsProps> = ({ onGenerated, onClose }) => {
  const [formData, setFormData] = useState({
    mois: "",
    type_paiement: "scolarité",
    montant: "500"
  });

  const [anneesScolaires, setAnneesScolaires] = useState<AnneeScolaire[]>([]);
  const [anneeActive, setAnneeActive] = useState<AnneeScolaire | null>(null);
  const [verification, setVerification] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [verifying, setVerifying] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);
  const [success, setSuccess] = useState("");

  const moisOptions = [
    "Janvier", "Février", "Mars", "Avril", "Mai", "Juin",
    "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"
  ];

  const typesPaiement = [
    { value: "scolarité", label: "Scolarité", montantDefaut: "500" },
    { value: "transport", label: "Transport", montantDefaut: "200" },
    { value: "inscription", label: "Inscription", montantDefaut: "300" },
    { value: "activité", label: "Activité", montantDefaut: "150" }
  ];

  useEffect(() => {
    fetchAnneesScolaires();
    // Sélectionner le mois courant par défaut
    const currentMonth = moisOptions[new Date().getMonth()];
    setFormData(prev => ({ ...prev, mois: currentMonth }));
  }, []);

  useEffect(() => {
    // Mettre à jour le montant par défaut selon le type de paiement
    const typeSelectionne = typesPaiement.find(t => t.value === formData.type_paiement);
    if (typeSelectionne) {
      setFormData(prev => ({ ...prev, montant: typeSelectionne.montantDefaut }));
    }
  }, [formData.type_paiement]);

  const fetchAnneesScolaires = async () => {
    try {
      const response = await getAnneesScolaires();
      if (response.data.success) {
        const annees = response.data.data;
        setAnneesScolaires(annees);

        // Trouver l'année active
        const active = annees.find((annee: AnneeScolaire) => annee.est_active);
        if (active) {
          setAnneeActive(active);
        } else {
          // Si aucune année active, prendre la première
          setAnneeActive(annees[0] || null);
        }
      }
    } catch (error) {
      console.error("Erreur lors du chargement des années scolaires:", error);
    }
  };

  const handleVerification = async () => {
    if (!anneeActive) {
      setErrors(["Aucune année scolaire active trouvée"]);
      return;
    }

    if (!formData.mois) {
      setErrors(["Veuillez sélectionner un mois"]);
      return;
    }

    setVerifying(true);
    setErrors([]);
    setVerification(null);

    try {
      // Extraire l'année de l'année scolaire active (ex: "2024-2025" -> 2024)
      const annee = parseInt(anneeActive.libelle.split('-')[0]);
      const response = await verifierPaiementsMensuels(formData.mois, annee);
      if (response.data.success) {
        setVerification(response.data.data);
      } else {
        setErrors([response.data.message || "Erreur lors de la vérification"]);
      }
    } catch (error: any) {
      console.error("Erreur lors de la vérification:", error);
      setErrors([error.response?.data?.message || "Erreur lors de la vérification"]);
    } finally {
      setVerifying(false);
    }
  };

  const handleGeneration = async () => {
    if (!formData.mois || !formData.type_paiement || !formData.montant) {
      setErrors(["Veuillez remplir tous les champs obligatoires"]);
      return;
    }

    if (!anneeActive) {
      setErrors(["Aucune année scolaire active trouvée"]);
      return;
    }

    setLoading(true);
    setErrors([]);
    setSuccess("");

    try {
      // Extraire l'année de l'année scolaire active
      const annee = parseInt(anneeActive.libelle.split('-')[0]);
      const response = await genererPaiementsMensuels(
        formData.mois,
        annee,
        formData.type_paiement
      );

      if (response.data.success) {
        setSuccess(`${response.data.data.paiements_crees} paiements créés avec succès`);
        onGenerated();
        setTimeout(() => {
          onClose();
        }, 2000);
      } else {
        setErrors([response.data.message || "Erreur lors de la génération"]);
      }
    } catch (error: any) {
      console.error("Erreur lors de la génération:", error);
      setErrors([error.response?.data?.message || "Erreur lors de la génération des paiements"]);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (field: string, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setErrors([]);
    setSuccess("");
    setVerification(null);
  };

  return (
    <div className="space-y-6">
      <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <AlertCircle className="h-5 w-5 text-blue-400" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">
              Génération automatique des paiements mensuels
            </h3>
            <div className="mt-2 text-sm text-blue-700">
              <p>Cette fonction créera automatiquement les paiements pour tous les élèves qui n'ont pas encore payé ce mois.</p>
              {anneeActive && (
                <p className="mt-1 font-medium">Année scolaire active : {anneeActive.libelle}</p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Messages d'erreur */}
      {errors.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Erreurs</h3>
              <div className="mt-2 text-sm text-red-700">
                <ul className="list-disc pl-5 space-y-1">
                  {errors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Message de succès */}
      {success && (
        <div className="bg-green-50 border border-green-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <CheckCircle className="h-5 w-5 text-green-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-green-800">{success}</p>
            </div>
          </div>
        </div>
      )}

      {/* Formulaire */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Mois */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            <Calendar size={16} className="inline mr-1" />
            Mois <span className="text-red-500">*</span>
          </label>
          <select
            value={formData.mois}
            onChange={(e) => handleChange('mois', e.target.value)}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
            required
          >
            <option value="">Sélectionner un mois</option>
            {moisOptions.map((mois) => (
              <option key={mois} value={mois}>
                {mois}
              </option>
            ))}
          </select>
        </div>

        {/* Type de paiement */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            <DollarSign size={16} className="inline mr-1" />
            Type de paiement <span className="text-red-500">*</span>
          </label>
          <select
            value={formData.type_paiement}
            onChange={(e) => handleChange('type_paiement', e.target.value)}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
            required
          >
            {typesPaiement.map((type) => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>

        {/* Montant par défaut */}
        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            <DollarSign size={16} className="inline mr-1" />
            Montant par défaut (MAD) <span className="text-red-500">*</span>
          </label>
          <input
            type="number"
            step="0.01"
            min="0"
            value={formData.montant}
            onChange={(e) => handleChange('montant', e.target.value)}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
            required
          />
        </div>
      </div>

      {/* Bouton de vérification */}
      <div className="flex justify-center">
        <Button
          variant="outline"
          onClick={handleVerification}
          disabled={verifying || !formData.mois}
          icon={<Users size={16} />}
        >
          {verifying ? "Vérification..." : "Vérifier les élèves concernés"}
        </Button>
      </div>

      {/* Résultats de la vérification */}
      {verification && (
        <div className="bg-gray-50 border border-gray-200 rounded-md p-4">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Résultats de la vérification :</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{verification.total_eleves}</div>
              <div className="text-gray-600">Total élèves</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{verification.deja_payes}</div>
              <div className="text-gray-600">Déjà payés</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{verification.a_generer}</div>
              <div className="text-gray-600">À générer</div>
            </div>
          </div>
        </div>
      )}

      {/* Boutons d'action */}
      <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
        <Button
          type="button"
          variant="outline"
          onClick={onClose}
          disabled={loading}
        >
          Annuler
        </Button>
        <Button
          type="button"
          variant="primary"
          onClick={handleGeneration}
          disabled={loading || !verification}
          icon={<Play size={16} />}
        >
          {loading ? (
            <>
              <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
              Génération...
            </>
          ) : (
            "Générer les paiements"
          )}
        </Button>
      </div>
    </div>
  );
};

export default GenerationPaiementsMensuels;
