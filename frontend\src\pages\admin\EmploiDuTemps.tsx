import React, { useState, useEffect } from "react";
import { Plus, Calendar, Clock, Users, BookOpen, MapPin, Filter, Eye } from "lucide-react";
import Button from "../../components/Button";
import Modal from "../../components/Modal";
import CoursForm from "../../components/CoursForm";
import EmploiDuTempsGrid from "../../components/EmploiDuTempsGrid";
import { getCours, deleteCours, getClasses, getClassesEnseignant } from "../../services/api";
import type { Cours, Classe } from "../../types";
import { useDeleteConfirmation } from "../../hooks/useConfirmation";
import { useAuth } from "../../context/AuthContext";
import { useEnseignantConnecte } from "../../hooks/useEnseignantConnecte";

const EmploiDuTemps: React.FC = () => {
  const [cours, setCours] = useState<Cours[]>([]);
  const [classes, setClasses] = useState<Classe[]>([]);
  const [filteredCours, setFilteredCours] = useState<Cours[]>([]);
  const [selectedClasse, setSelectedClasse] = useState<number | null>(null);
  const [selectedEnseignant, setSelectedEnseignant] = useState<number | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [coursToEdit, setCoursToEdit] = useState<Cours | null>(null);
  const [loading, setLoading] = useState(true);
  const [errorMessage, setErrorMessage] = useState("");
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const { user } = useAuth();
  const { enseignants, enseignantConnecte, getEnseignantConnecte, isEnseignant, isAdmin } = useEnseignantConnecte();

  // Hook pour la confirmation de suppression
  const { confirmDelete, ConfirmationComponent } = useDeleteConfirmation();

  useEffect(() => {
    // Charger les données (les enseignants sont chargés par le hook)
    const loadData = async () => {
      await fetchClasses();
      await fetchCours();
    };
    loadData();
  }, []);

  // Effet pour gérer la sélection automatique de l'enseignant et filtrer les cours
  useEffect(() => {
    if (user?.role === 'enseignant' && enseignantConnecte) {
      if (selectedEnseignant !== enseignantConnecte.id_enseignant) {
        setSelectedEnseignant(enseignantConnecte.id_enseignant);
      }
    }
    // Filtrer les cours à chaque changement
    filterCours();
  }, [cours, selectedClasse, selectedEnseignant, enseignantConnecte, user]);

  const fetchCours = async () => {
    setLoading(true);
    try {
      const response = await getCours();
      if (response.data.success) {
        // Charger tous les cours, le filtrage se fera côté frontend
        setCours(response.data.data);
      } else {
        setErrorMessage("Erreur lors du chargement des cours");
      }
    } catch (error) {
      console.error("Erreur lors du chargement des cours:", error);
      setErrorMessage("Erreur de connexion à l'API des cours");
    } finally {
      setLoading(false);
    }
  };

  const fetchClasses = async () => {
    try {
      if (user?.role === 'enseignant') {
        // Pour les enseignants, récupérer seulement leurs classes
        const enseignantConnecte = getEnseignantConnecte();
        if (enseignantConnecte) {
          const response = await getClassesEnseignant(enseignantConnecte.id_enseignant);
          if (response.data.success) {
            setClasses(response.data.data);
          }
        } else {
          setClasses([]);
        }
      } else {
        // Pour les admins, récupérer toutes les classes
        const response = await getClasses();
        if (response.data.success) {
          setClasses(response.data.data);
        }
      }
    } catch (error) {
      console.error("Erreur lors du chargement des classes:", error);
    }
  };



  // Fonction pour obtenir le nom de l'enseignant
  const getEnseignantName = (id_enseignant: number) => {
    const enseignant = enseignants.find(e => e.id_enseignant === id_enseignant);
    if (enseignant?.user) {
      return `${enseignant.user.prenom} ${enseignant.user.nom}`;
    }
    return 'Enseignant non trouvé';
  };

  const filterCours = () => {
    let filtered = cours;

    // Si c'est un enseignant, filtrer d'abord par ses cours
    if (user?.role === 'enseignant') {
      const enseignantConnecte = getEnseignantConnecte();
      if (enseignantConnecte) {
        filtered = filtered.filter(c => c.id_enseignant === enseignantConnecte.id_enseignant);
      } else {
        filtered = [];
      }
    }

    // Filtre par classe
    if (selectedClasse) {
      filtered = filtered.filter(c => c.id_classe === selectedClasse);
    }

    // Filtre par enseignant (seulement pour les admins)
    if (user?.role === 'admin' && selectedEnseignant) {
      filtered = filtered.filter(c => c.id_enseignant === selectedEnseignant);
    }

    // Pour les admins : Si aucun filtre n'est sélectionné, afficher un emploi du temps vide
    // Pour les enseignants : Afficher leur emploi du temps même sans sélection de classe
    if (user?.role === 'admin' && !selectedClasse && !selectedEnseignant) {
      setFilteredCours([]);
    } else {
      setFilteredCours(filtered);
    }
  };

  const handleAdd = () => {
    setCoursToEdit(null);
    setShowModal(true);
  };

  const handleEdit = (cours: Cours) => {
    setCoursToEdit(cours);
    setShowModal(true);
  };

  const handleDelete = async (id: number) => {
    try {
      const response = await deleteCours(id);
      if (response.data.success) {
        await fetchCours();
      } else {
        setErrorMessage("Erreur lors de la suppression du cours");
      }
    } catch (error) {
      console.error("Erreur lors de la suppression:", error);
      setErrorMessage("Erreur lors de la suppression du cours");
    }
  };

  const handleDeleteClick = (cours: Cours) => {
    const coursName = `${cours.nom_matiere_fr} - ${cours.nom_classe} (${cours.jour_semaine} ${cours.heure_debut.substring(0, 5)}-${cours.heure_fin.substring(0, 5)})`;
    confirmDelete(
      () => handleDelete(cours.id_cours),
      coursName,
      "Cette action supprimera définitivement ce cours de l'emploi du temps."
    );
  };

  const handleModalClose = () => {
    setShowModal(false);
    setCoursToEdit(null);
  };

  const handleCoursSubmit = async () => {
    setShowModal(false);
    setCoursToEdit(null);
    await fetchCours();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
        <span className="ml-2">Chargement de l'emploi du temps...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Messages d'erreur */}
      {errorMessage && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {errorMessage}
        </div>
      )}

      {/* En-tête */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        {/* Boutons de vue */}
        <div className="flex rounded-md shadow-sm">
          <button
            onClick={() => setViewMode('grid')}
            className={`px-3 py-2 text-sm font-medium rounded-l-md border ${
              viewMode === 'grid'
                ? 'bg-primary text-white border-primary'
                : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
            }`}
          >
            <Calendar size={16} className="inline mr-1" />
            Grille
          </button>
          <button
            onClick={() => setViewMode('list')}
            className={`px-3 py-2 text-sm font-medium rounded-r-md border-t border-r border-b ${
              viewMode === 'list'
                ? 'bg-primary text-white border-primary'
                : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
            }`}
          >
            <Eye size={16} className="inline mr-1" />
            Liste
          </button>
        </div>
      {user?.role === "admin" && (
        <div className="flex items-center gap-3">
          {/* Filtre par classe */}
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Filter size={18} className="text-gray-400" />
            </div>
            <select
              value={selectedClasse || ''}
              onChange={(e) => setSelectedClasse(e.target.value ? parseInt(e.target.value) : null)}
              className="block w-48 pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
            >
              <option value="">Choisir une classe</option>
              {classes.map((classe) => (
                <option key={classe.id_classe} value={classe.id_classe}>
                  {classe.nom_classe}
                </option>
              ))}
            </select>
          </div>

          {/* Filtre par enseignant - masqué pour les enseignants */}
          {user?.role !== 'enseignant' && (
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Users size={18} className="text-gray-400" />
              </div>
              <select
                value={selectedEnseignant || ''}
                onChange={(e) => setSelectedEnseignant(e.target.value ? parseInt(e.target.value) : null)}
                className="block w-48 pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
              >
                <option value="">Choisir un enseignant</option>
                {enseignants.map((enseignant) => (
                  <option key={enseignant.id_enseignant} value={enseignant.id_enseignant}>
                    {enseignant.user?.prenom || ''} {enseignant.user?.nom || ''}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* Bouton pour effacer les filtres */}
          {(selectedClasse || selectedEnseignant) && (
            <Button
              variant="outline"
              onClick={() => {
                setSelectedClasse(null);
                setSelectedEnseignant(null);
              }}
            >
              Effacer les filtres
            </Button>
          )}

          {user?.role === 'admin' && (
            <Button
              icon={<Plus size={16} />}
              variant="primary"
              onClick={handleAdd}
            >
              Ajouter un cours
            </Button>
          )}
        </div>
      )}
      </div>

      {/* Statistiques rapides */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <BookOpen className="h-8 w-8 text-primary" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Total cours</p>
              <p className="text-lg font-semibold text-gray-900">{filteredCours.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Users className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Classes</p>
              <p className="text-lg font-semibold text-gray-900">
                {selectedClasse ? 1 : new Set(cours.map(c => c.id_classe)).size}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Clock className="h-8 w-8 text-orange-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Heures/semaine</p>
              <p className="text-lg font-semibold text-gray-900">
                {filteredCours.reduce((total, cours) => {
                  const debut = new Date(`2000-01-01T${cours.heure_debut}`);
                  const fin = new Date(`2000-01-01T${cours.heure_fin}`);
                  return total + (fin.getTime() - debut.getTime()) / (1000 * 60 * 60);
                }, 0).toFixed(1)}h
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <MapPin className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Salles utilisées</p>
              <p className="text-lg font-semibold text-gray-900">
                {new Set(filteredCours.map(c => c.id_salle)).size}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Contenu principal */}
      {user?.role === 'admin' && !selectedClasse && !selectedEnseignant ? (
        // Message d'information pour admin sans sélection
        <div className="bg-white rounded-lg border border-gray-200">
          <div className="text-center py-16">
            <div className="text-gray-400 mb-4">
              <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Emploi du temps</h3>
            <p className="text-gray-500 mb-2">
              Veuillez sélectionner une classe ou un enseignant
            </p>
            <p className="text-sm text-gray-400">
              pour afficher l'emploi du temps correspondant
            </p>
          </div>
        </div>
      ) : viewMode === 'grid' ? (
        <EmploiDuTempsGrid
          cours={filteredCours}
          onEdit={handleEdit}
          onDelete={handleDeleteClick}
          creneauxDetailles={true}
          userRole={user?.role}
          showSelectionMessage={false}
          selectedClasse={selectedClasse}
          selectedEnseignant={selectedEnseignant}
        />
      ) : (
        <div className="bg-white rounded-lg border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Liste des cours</h3>
          </div>
          <div className="divide-y divide-gray-200">
            {filteredCours.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Calendar className="mx-auto mb-2 w-10 h-10" />
                {user?.role === 'enseignant' ? (
                  <div>
                    <p>Aucun cours programmé</p>
                    <p className="text-sm">Votre emploi du temps est vide</p>
                  </div>
                ) : (
                  <div>
                    <p>Aucun cours planifié</p>
                    <p className="text-sm">Commencez par ajouter des cours à l'emploi du temps</p>
                  </div>
                )}
              </div>
            ) : (
              filteredCours.map((cours) => (
                <div key={cours.id_cours} className="p-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-4">
                        <div className="flex-shrink-0">
                          <div className="w-12 h-12 bg-primary bg-opacity-10 rounded-lg flex items-center justify-center">
                            <BookOpen className="w-6 h-6 text-primary" />
                          </div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {cours.nom_matiere_fr} - {cours.nom_unite}
                          </p>
                          <p className="text-sm text-gray-500">
                            {cours.nom_classe} • {getEnseignantName(cours.id_enseignant)}
                          </p>
                          <div className="flex items-center space-x-4 mt-1">
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary bg-opacity-10 text-primary">
                              {cours.jour_semaine}
                            </span>
                            <span className="text-xs text-gray-500">
                              {cours.heure_debut.substring(0, 5)} - {cours.heure_fin.substring(0, 5)}
                            </span>
                            <span className="text-xs text-gray-500">
                              Salle: {cours.nom_salle}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                    {user?.role === 'admin' && (
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(cours)}
                        >
                          Modifier
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteClick(cours)}
                          className="text-red-600 hover:text-red-700 border-red-200 hover:border-red-300"
                        >
                          Supprimer
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      )}

      {/* Modal de formulaire */}
      <Modal
        isOpen={showModal}
        onClose={handleModalClose}
        title={coursToEdit ? "Modifier le cours" : "Ajouter un cours"}
      >
        <CoursForm
          cours={coursToEdit}
          onSubmit={handleCoursSubmit}
          onCancel={handleModalClose}
        />
      </Modal>

      {/* Composant de confirmation de suppression */}
      <ConfirmationComponent />
    </div>
  );
};

export default EmploiDuTemps;
