import React, { useState, useEffect } from "react";
import { Calendar, Clock, MapPin, Users, User } from "lucide-react";
import { getCours } from "../../services/api";
import { getMatiereColorClasses } from "../../utils/matiereColors";
import type { Cours } from "../../types";

const EmploiDuTempsEleve: React.FC = () => {
  console.log("🎓 EmploiDuTempsEleve - Composant chargé");
  console.log("📍 URL actuelle:", window.location.pathname);
  const [cours, setCours] = useState<Cours[]>([]);
  const [loading, setLoading] = useState(true);
  const [errorMessage, setErrorMessage] = useState("");

  const jours = ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi'];
  const heures = [
    '08:00', '08:30', '09:00', '09:30', '10:00', '10:30',
    '11:00', '11:30', '12:00', '12:30', '13:00', '13:30',
    '14:00', '14:30', '15:00', '15:30'
  ];

  useEffect(() => {
    console.log("🚀 useEffect déclenché");
    fetchCours();
  }, []);

  const fetchCours = async () => {
    console.log("🔄 Début du chargement des cours");
    setLoading(true);
    try {
      const response = await getCours();
      console.log("📡 Réponse API:", response.data);
      if (response.data.success) {
        console.log("✅ Cours chargés:", response.data.data.length, "cours");
        setCours(response.data.data);
      } else {
        console.log("❌ Erreur API:", response.data.message);
        setErrorMessage("Erreur lors du chargement de l'emploi du temps");
      }
    } catch (error) {
      console.error("💥 Erreur lors du chargement des cours:", error);
      setErrorMessage("Erreur de connexion");
    } finally {
      console.log("🏁 Fin du chargement, loading = false");
      setLoading(false);
    }
  };

  // Organiser les cours par jour et heure
  const organiserCours = () => {
    const grille: { [jour: string]: { [heure: string]: Cours[] } } = {};

    jours.forEach(jour => {
      grille[jour] = {};
      heures.forEach(heure => {
        grille[jour][heure] = [];
      });
    });

    cours.forEach(cours => {
      const heureDebut = cours.heure_debut.substring(0, 5);

      // Fonction pour mapper une heure à un créneau de 30 minutes
      const mapperHeureVersCreneaux = (heure: string): string => {
        const [h, m] = heure.split(':').map(Number);
        const minutesTotal = h * 60 + m;

        // Trouver le créneau exact ou le plus proche
        let meilleurCreneau = heures[0];
        let meilleureDifference = Infinity;

        heures.forEach(creneau => {
          const [hCreneau, mCreneau] = creneau.split(':').map(Number);
          const minutesCreneau = hCreneau * 60 + mCreneau;

          // Si c'est exactement l'heure du créneau
          if (minutesCreneau === minutesTotal) {
            meilleurCreneau = creneau;
            return;
          }

          // Si le cours commence après ce créneau mais avant le suivant
          if (minutesCreneau <= minutesTotal) {
            const difference = minutesTotal - minutesCreneau;
            if (difference < meilleureDifference) {
              meilleureDifference = difference;
              meilleurCreneau = creneau;
            }
          }
        });

        return meilleurCreneau;
      };

      const heureSlot = mapperHeureVersCreneaux(heureDebut);

      if (grille[cours.jour_semaine] && grille[cours.jour_semaine][heureSlot]) {
        grille[cours.jour_semaine][heureSlot].push(cours);
      }
    });

    return grille;
  };

  const grilleOrganisee = organiserCours();



  const CoursCard: React.FC<{ cours: Cours; colorClass: string; heureActuelle: string }> = ({ cours, colorClass, heureActuelle }) => {
    // Calculer la position et la hauteur exactes
    const heureDebut = cours.heure_debut.substring(0, 5);
    const heureFin = cours.heure_fin.substring(0, 5);

    // Convertir en minutes
    const [hDebut, mDebut] = heureDebut.split(':').map(Number);
    const [hFin, mFin] = heureFin.split(':').map(Number);
    const [hActuelle, mActuelle] = heureActuelle.split(':').map(Number);

    const minutesDebut = hDebut * 60 + mDebut;
    const minutesFin = hFin * 60 + mFin;
    const minutesActuelle = hActuelle * 60 + mActuelle;

    // Calculer l'offset depuis le début du créneau actuel
    const offsetMinutes = minutesDebut - minutesActuelle;
    const hauteurCellule = 40; // Créneaux de 30 minutes = 40px
    const offsetPixels = (offsetMinutes / 30) * hauteurCellule;

    // Calculer la hauteur totale
    const dureeMinutes = minutesFin - minutesDebut;
    const hauteurTotale = (dureeMinutes / 30) * hauteurCellule;

    return (
      <div
        className={`p-1 rounded border-l-2 ${colorClass} text-xs absolute left-0 right-0 flex flex-col`}
        style={{
          top: `${offsetPixels}px`,
          height: `${hauteurTotale}px`,
          zIndex: 10,
          border: '1px solid #e5e7eb'
        }}
      >
        <div className="text-xs font-medium truncate leading-tight">
          {cours.nom_matiere_fr}
        </div>
        <div className="flex flex-col space-y-1 mt-0.5 flex-1">
          <div className="flex items-center space-x-1 text-xs text-gray-600">
            <User size={10} />
            <span className="truncate">{cours.nom_enseignant || "Enseignant"}</span>
          </div>
          <div className="flex items-center space-x-1 text-xs text-gray-500">
            <Clock size={10} />
            <span>{heureDebut}-{heureFin}</span>
          </div>
          <div className="flex items-center space-x-1 text-xs text-gray-500">
            <MapPin size={10} />
            <span>{cours.nom_salle}</span>
          </div>
        </div>
      </div>
    );
  };

  console.log("🎨 Rendu - loading:", loading, "errorMessage:", errorMessage, "cours:", cours.length);

  if (loading) {
    console.log("⏳ Affichage du spinner de chargement");
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Chargement de votre emploi du temps...</span>
      </div>
    );
  }

  if (errorMessage) {
    console.log("❌ Affichage du message d'erreur:", errorMessage);
    return (
      <div className="text-center py-8 text-red-600">
        <Calendar className="mx-auto mb-2 w-10 h-10" />
        <p>{errorMessage}</p>
      </div>
    );
  }

  console.log("🎨 Rendu principal - cours:", cours.length);

  // Calculer les cours d'aujourd'hui
  const getCoursAujourdhui = () => {
    const aujourdhui = new Date();
    const joursMap = ['Dimanche', 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'];
    const jourAujourdhui = joursMap[aujourdhui.getDay()];

    return cours.filter(c => c.jour_semaine === jourAujourdhui)
               .sort((a, b) => a.heure_debut.localeCompare(b.heure_debut));
  };

  const coursAujourdhui = getCoursAujourdhui();

  return (
    <div className="space-y-6">
      {/* Cours d'aujourd'hui */}
      {coursAujourdhui.length > 0 && (
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 bg-blue-50">
            <div className="flex items-center gap-2">
              <Clock className="w-5 h-5 text-blue-600" />
              <h3 className="text-lg font-medium text-gray-900">Mes cours d'aujourd'hui</h3>
              <span className="text-sm text-blue-600">
                ({new Date().toLocaleDateString('fr-FR', { weekday: 'long' })})
              </span>
            </div>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {coursAujourdhui.map((cours, index) => (
                <div key={cours.id_cours} className={`rounded-lg p-4 border-l-4 ${getMatiereColorClasses(cours.nom_matiere_fr || 'Matière')}`}>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900 text-sm">
                        {cours.nom_matiere_fr || cours.nom_unite}
                      </h4>
                      <div className="flex items-center gap-1 mt-1 text-xs text-gray-600">
                        <User size={10} />
                        <span>{cours.nom_enseignant || "Enseignant"}</span>
                      </div>
                      <div className="flex items-center gap-1 mt-1 text-xs text-gray-600">
                        <MapPin size={10} />
                        <span>{cours.nom_salle}</span>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-blue-600">
                        {cours.heure_debut.substring(0, 5)}
                      </div>
                      <div className="text-xs text-gray-500">
                        {cours.heure_fin.substring(0, 5)}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Statistiques rapides */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <Users className="w-8 h-8 text-blue-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Cours cette semaine</p>
              <p className="text-2xl font-semibold text-gray-900">{cours.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <Clock className="w-8 h-8 text-green-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Heures de cours</p>
              <p className="text-2xl font-semibold text-gray-900">
                {cours.reduce((total, c) => {
                  const debut = new Date(`1970-01-01T${c.heure_debut}`);
                  const fin = new Date(`1970-01-01T${c.heure_fin}`);
                  return total + (fin.getTime() - debut.getTime()) / (1000 * 60 * 60);
                }, 0)}h
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <User className="w-8 h-8 text-purple-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Enseignants</p>
              <p className="text-2xl font-semibold text-gray-900">
                {new Set(cours.map(c => c.nom_enseignant)).size}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Grille d'emploi du temps */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Mon emploi du temps</h3>
          <p className="text-sm text-gray-500 mt-1">
            Voici votre planning de cours pour cette semaine
          </p>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">
                  Heure
                </th>
                {jours.map((jour) => (
                  <th
                    key={jour}
                    className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    {jour}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {heures.map((heure) => (
                <tr key={heure} className="hover:bg-gray-50">
                  <td className="px-2 py-2 whitespace-nowrap text-sm font-medium text-gray-900 bg-gray-50">
                    {heure}
                  </td>
                  {jours.map((jour) => (
                    <td
                      key={`${jour}-${heure}`}
                      className="px-1 py-1 text-sm text-gray-500 align-top border-r border-gray-100"
                      style={{ minHeight: '40px', width: '120px' }}
                    >
                      <div className="min-h-[30px]" style={{ position: 'relative' }}>
                        {grilleOrganisee[jour][heure].map((cours, index) => (
                          <CoursCard
                            key={cours.id_cours}
                            cours={cours}
                            colorClass={getMatiereColorClasses(cours.nom_matiere_fr || 'Matière')}
                            heureActuelle={heure}
                          />
                        ))}
                      </div>
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Message si aucun cours */}
        {cours.length === 0 && (
          <div className="text-center py-12">
            <Calendar className="mx-auto mb-4 w-12 h-12 text-gray-400" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun cours disponible</h3>
            <p className="text-gray-500">
              Votre emploi du temps sera disponible une fois les cours programmés
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default EmploiDuTempsEleve;
