<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configuration SMTP - ScolaNova</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: #2563eb;
            color: white;
            padding: 20px;
            margin: -30px -30px 30px -30px;
            border-radius: 8px 8px 0 0;
            text-align: center;
        }
        .step {
            background: #f8f9fa;
            border-left: 4px solid #2563eb;
            padding: 15px;
            margin: 15px 0;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="email"], input[type="password"], select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        .btn {
            background: #2563eb;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        .btn:hover {
            background: #1d4ed8;
        }
        .btn-success {
            background: #16a085;
        }
        .btn-success:hover {
            background: #138d75;
        }
        .btn-test {
            background: #f39c12;
        }
        .btn-test:hover {
            background: #e67e22;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .alert-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .config-preview {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
            font-family: monospace;
        }
        .provider-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .provider-btn {
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background: white;
            cursor: pointer;
            text-align: center;
            transition: all 0.3s;
        }
        .provider-btn:hover {
            border-color: #2563eb;
            background: #f0f7ff;
        }
        .provider-btn.active {
            border-color: #2563eb;
            background: #e3f2fd;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📧 Configuration SMTP</h1>
            <p>Configurez l'envoi d'emails via SMTP pour ScolaNova</p>
        </div>

        <?php
        $configFile = __DIR__ . '/../config/email.php';
        $currentConfig = [];
        
        if (file_exists($configFile)) {
            $currentConfig = require $configFile;
        }

        $message = '';
        $messageType = '';
        $testResult = '';

        // Configurations prédéfinies
        $providers = [
            'gmail' => [
                'name' => 'Gmail',
                'host' => 'smtp.gmail.com',
                'port' => 587,
                'encryption' => 'tls',
                'instructions' => 'Utilisez votre email Gmail et un mot de passe d\'application'
            ],
            'outlook' => [
                'name' => 'Outlook/Hotmail',
                'host' => 'smtp-mail.outlook.com',
                'port' => 587,
                'encryption' => 'tls',
                'instructions' => 'Utilisez votre email Outlook et votre mot de passe normal'
            ],
            'yahoo' => [
                'name' => 'Yahoo',
                'host' => 'smtp.mail.yahoo.com',
                'port' => 587,
                'encryption' => 'tls',
                'instructions' => 'Utilisez votre email Yahoo et un mot de passe d\'application'
            ],
            'custom' => [
                'name' => 'Autre/Personnalisé',
                'host' => '',
                'port' => 587,
                'encryption' => 'tls',
                'instructions' => 'Configurez manuellement votre serveur SMTP'
            ]
        ];

        // Traitement du formulaire
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            if (isset($_POST['save_config'])) {
                $provider = $_POST['provider'] ?? 'gmail';
                $host = $_POST['smtp_host'] ?? $providers[$provider]['host'];
                $port = (int)($_POST['smtp_port'] ?? $providers[$provider]['port']);
                $encryption = $_POST['encryption'] ?? $providers[$provider]['encryption'];
                $username = $_POST['username'] ?? '';
                $password = $_POST['password'] ?? '';
                $fromName = $_POST['from_name'] ?? 'ScolaNova';
                $fromEmail = $_POST['from_email'] ?? $username;

                if (empty($username) || empty($password)) {
                    $message = 'Email et mot de passe requis !';
                    $messageType = 'error';
                } else {
                    // Générer la nouvelle configuration
                    $newConfig = "<?php

/**
 * Configuration SMTP pour l'envoi d'emails
 * 
 * Configuré le " . date('Y-m-d H:i:s') . "
 * Fournisseur: " . $providers[$provider]['name'] . "
 */

return [
    // Configuration SMTP
    'smtp' => [
        'host' => '" . addslashes($host) . "',
        'port' => $port,
        'username' => '" . addslashes($username) . "',
        'password' => '" . addslashes($password) . "',
        'encryption' => '$encryption',
    ],

    // Informations de l'expéditeur
    'from' => [
        'email' => '" . addslashes($fromEmail) . "',
        'name' => '" . addslashes($fromName) . "'
    ],

    // Templates d'emails
    'templates' => [
        'temporary_password' => [
            'subject' => 'Votre mot de passe temporaire - ScolaNova',
        ],
        'password_changed' => [
            'subject' => 'Confirmation de changement de mot de passe - ScolaNova',
        ]
    ],

    // Options générales
    'options' => [
        'charset' => 'UTF-8',
        'timeout' => 30,
        'debug' => false,
    ]
];

?>";

                    if (file_put_contents($configFile, $newConfig)) {
                        $message = 'Configuration SMTP sauvegardée avec succès !';
                        $messageType = 'success';
                        
                        // Recharger la configuration
                        $currentConfig = require $configFile;
                    } else {
                        $message = 'Erreur lors de la sauvegarde de la configuration.';
                        $messageType = 'error';
                    }
                }
            }

            // Test d'envoi d'email
            if (isset($_POST['test_email'])) {
                $testEmail = $_POST['test_email_address'] ?? '';
                
                if (empty($testEmail)) {
                    $testResult = 'Adresse email de test requise !';
                } else {
                    try {
                        require_once __DIR__ . '/../services/SMTPEmailService.php';
                        $smtpService = new SMTPEmailService();
                        
                        $success = $smtpService->sendTemporaryPassword(
                            $testEmail,
                            "Test ScolaNova",
                            "TEST" . rand(100, 999)
                        );
                        
                        if ($success) {
                            $testResult = "✅ Email de test envoyé avec succès à $testEmail !";
                        } else {
                            $testResult = "❌ Échec de l'envoi. Vérifiez la configuration et les logs.";
                        }
                    } catch (Exception $e) {
                        $testResult = "❌ Erreur: " . $e->getMessage();
                    }
                }
            }
        }

        // Détecter le fournisseur actuel
        $currentProvider = 'custom';
        if (!empty($currentConfig['smtp']['host'])) {
            foreach ($providers as $key => $provider) {
                if ($provider['host'] === $currentConfig['smtp']['host']) {
                    $currentProvider = $key;
                    break;
                }
            }
        }
        ?>

        <?php if ($message): ?>
            <div class="alert alert-<?= $messageType ?>">
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>

        <?php if ($testResult): ?>
            <div class="alert alert-info">
                <?= htmlspecialchars($testResult) ?>
            </div>
        <?php endif; ?>

        <div class="step">
            <h3>📋 Instructions pour Gmail (Recommandé)</h3>
            <ol>
                <li><strong>Activez la validation en 2 étapes</strong> sur votre compte Gmail</li>
                <li>Allez dans <strong>Sécurité → Mots de passe des applications</strong></li>
                <li>Générez un <strong>mot de passe d'application</strong> pour "Mail"</li>
                <li>Utilisez ce mot de passe de 16 caractères ci-dessous</li>
            </ol>
        </div>

        <form method="POST">
            <h3>1. Choisissez votre fournisseur d'email</h3>
            <div class="provider-buttons">
                <?php foreach ($providers as $key => $provider): ?>
                    <div class="provider-btn <?= $key === $currentProvider ? 'active' : '' ?>" 
                         onclick="selectProvider('<?= $key ?>')">
                        <h4><?= htmlspecialchars($provider['name']) ?></h4>
                        <small><?= htmlspecialchars($provider['instructions']) ?></small>
                        <input type="radio" name="provider" value="<?= $key ?>" 
                               <?= $key === $currentProvider ? 'checked' : '' ?> style="display: none;">
                    </div>
                <?php endforeach; ?>
            </div>

            <h3>2. Configuration SMTP</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div class="form-group">
                    <label for="smtp_host">Serveur SMTP :</label>
                    <input type="text" id="smtp_host" name="smtp_host" 
                           value="<?= htmlspecialchars($currentConfig['smtp']['host'] ?? '') ?>" required>
                </div>

                <div class="form-group">
                    <label for="smtp_port">Port :</label>
                    <input type="number" id="smtp_port" name="smtp_port" 
                           value="<?= htmlspecialchars($currentConfig['smtp']['port'] ?? '587') ?>" required>
                </div>
            </div>

            <div class="form-group">
                <label for="encryption">Chiffrement :</label>
                <select id="encryption" name="encryption">
                    <option value="tls" <?= ($currentConfig['smtp']['encryption'] ?? 'tls') === 'tls' ? 'selected' : '' ?>>TLS (Recommandé)</option>
                    <option value="ssl" <?= ($currentConfig['smtp']['encryption'] ?? '') === 'ssl' ? 'selected' : '' ?>>SSL</option>
                </select>
            </div>

            <h3>3. Authentification</h3>
            <div class="form-group">
                <label for="username">Email / Nom d'utilisateur :</label>
                <input type="email" id="username" name="username" 
                       value="<?= htmlspecialchars($currentConfig['smtp']['username'] ?? '') ?>" 
                       placeholder="<EMAIL>" required>
            </div>

            <div class="form-group">
                <label for="password">Mot de passe :</label>
                <input type="password" id="password" name="password" 
                       placeholder="Mot de passe d'application (Gmail) ou mot de passe normal" required>
                <small style="color: #666;">⚠️ Pour Gmail, utilisez un mot de passe d'application de 16 caractères</small>
            </div>

            <h3>4. Informations de l'expéditeur</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div class="form-group">
                    <label for="from_name">Nom de l'expéditeur :</label>
                    <input type="text" id="from_name" name="from_name" 
                           value="<?= htmlspecialchars($currentConfig['from']['name'] ?? 'ScolaNova') ?>">
                </div>

                <div class="form-group">
                    <label for="from_email">Email de l'expéditeur :</label>
                    <input type="email" id="from_email" name="from_email" 
                           value="<?= htmlspecialchars($currentConfig['from']['email'] ?? '') ?>" 
                           placeholder="<EMAIL>">
                </div>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <button type="submit" name="save_config" class="btn btn-success">
                    💾 Sauvegarder la Configuration
                </button>
            </div>
        </form>

        <?php if (!empty($currentConfig['smtp']['host'])): ?>
            <div style="border-top: 1px solid #ddd; padding-top: 20px; margin-top: 20px;">
                <h3>5. Test d'envoi d'email</h3>
                <form method="POST" style="display: flex; gap: 15px; align-items: end;">
                    <div class="form-group" style="flex: 1; margin-bottom: 0;">
                        <label for="test_email_address">Email de test :</label>
                        <input type="email" id="test_email_address" name="test_email_address" 
                               value="<?= htmlspecialchars($currentConfig['smtp']['username'] ?? '') ?>" 
                               placeholder="<EMAIL>" required>
                    </div>
                    <button type="submit" name="test_email" class="btn btn-test">
                        🧪 Envoyer un Test
                    </button>
                </form>
            </div>
        <?php endif; ?>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">
            <h4>🔗 Liens utiles :</h4>
            <p>
                <a href="view_emails.php" target="_blank">📧 Voir les emails sauvegardés</a> |
                <a href="https://myaccount.google.com/security" target="_blank">🔐 Sécurité Google</a> |
                <a href="../" target="_blank">🏠 Retour à l'application</a>
            </p>
        </div>
    </div>

    <script>
        function selectProvider(provider) {
            // Désélectionner tous les boutons
            document.querySelectorAll('.provider-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Sélectionner le bouton cliqué
            event.currentTarget.classList.add('active');
            
            // Cocher le radio button
            document.querySelector(`input[name="provider"][value="${provider}"]`).checked = true;
            
            // Configurations prédéfinies
            const configs = {
                gmail: { host: 'smtp.gmail.com', port: 587, encryption: 'tls' },
                outlook: { host: 'smtp-mail.outlook.com', port: 587, encryption: 'tls' },
                yahoo: { host: 'smtp.mail.yahoo.com', port: 587, encryption: 'tls' },
                custom: { host: '', port: 587, encryption: 'tls' }
            };
            
            if (configs[provider]) {
                document.getElementById('smtp_host').value = configs[provider].host;
                document.getElementById('smtp_port').value = configs[provider].port;
                document.getElementById('encryption').value = configs[provider].encryption;
            }
        }

        // Auto-remplir l'email de l'expéditeur quand on change le username
        document.getElementById('username').addEventListener('input', function() {
            const fromEmailField = document.getElementById('from_email');
            if (!fromEmailField.value || fromEmailField.value === fromEmailField.getAttribute('placeholder')) {
                fromEmailField.value = this.value;
            }
        });
    </script>
</body>
</html>
