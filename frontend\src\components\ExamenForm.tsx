import React, { useState, useEffect } from 'react';
import { X, Calendar, Clock, BookOpen, Users, User } from 'lucide-react';
import Button from './Button';
import Input from './Input';
import Select from './Select';
import { addExamen, updateExamen, getMatieres, getClasses, getEnseignants } from '../services/api';
import { ExamenSimple, Matiere, Classe, Enseignant } from '../types';

interface ExamenFormProps {
  examen?: ExamenSimple | null;
  onClose: () => void;
  onSuccess: () => void;
}

const ExamenForm: React.FC<ExamenFormProps> = ({ examen, onClose, onSuccess }) => {
  const [formData, setFormData] = useState({
    id_matiere: '',
    id_classe: '',
    id_enseignant: '',
    type_examen: '',
    date_examen: '',
    duree_examen: '',
    semestre: '',
    commentaire: ''
  });

  const [matieres, setMatieres] = useState<Matiere[]>([]);
  const [classes, setClasses] = useState<Classe[]>([]);
  const [enseignants, setEnseignants] = useState<Enseignant[]>([]);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);
  const [fieldErrors, setFieldErrors] = useState<{[key: string]: string}>({});

  useEffect(() => {
    fetchData();
    if (examen) {
      setFormData({
        id_matiere: examen.id_matiere.toString(),
        id_classe: examen.id_classe.toString(),
        id_enseignant: examen.id_enseignant?.toString() || '',
        type_examen: examen.type_examen,
        date_examen: examen.date_examen,
        duree_examen: examen.duree_examen.toString(),
        semestre: examen.semestre,
        commentaire: examen.commentaire || ''
      });
    }
  }, [examen]);

  const fetchData = async () => {
    try {
      const [matieresRes, classesRes, enseignantsRes] = await Promise.all([
        getMatieres(),
        getClasses(),
        getEnseignants()
      ]);

      setMatieres(matieresRes.data.data || []);
      setClasses(classesRes.data.data || []);
      setEnseignants(enseignantsRes.data.data || []);
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
      setErrors(['Erreur lors du chargement des données']);
    }
  };

  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Effacer l'erreur du champ quand l'utilisateur commence à taper
    if (fieldErrors[field]) {
      setFieldErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: string[] = [];
    const newFieldErrors: {[key: string]: string} = {};

    // Validation des champs requis
    if (!formData.id_matiere) {
      newFieldErrors.id_matiere = 'La matière est requise';
      newErrors.push('La matière est requise');
    }

    if (!formData.id_classe) {
      newFieldErrors.id_classe = 'La classe est requise';
      newErrors.push('La classe est requise');
    }

    if (!formData.type_examen) {
      newFieldErrors.type_examen = 'Le type d\'examen est requis';
      newErrors.push('Le type d\'examen est requis');
    }

    if (!formData.date_examen) {
      newFieldErrors.date_examen = 'La date est requise';
      newErrors.push('La date est requise');
    } else {
      // Vérifier que la date n'est pas dans le passé (sauf pour modification)
      const selectedDate = new Date(formData.date_examen);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      if (!examen && selectedDate < today) {
        newFieldErrors.date_examen = 'La date ne peut pas être dans le passé';
        newErrors.push('La date ne peut pas être dans le passé');
      }
    }

    if (!formData.duree_examen) {
      newFieldErrors.duree_examen = 'La durée est requise';
      newErrors.push('La durée est requise');
    } else {
      const duree = parseInt(formData.duree_examen);
      if (isNaN(duree) || duree <= 0) {
        newFieldErrors.duree_examen = 'La durée doit être un nombre positif';
        newErrors.push('La durée doit être un nombre positif');
      } else if (duree > 480) { // 8 heures max
        newFieldErrors.duree_examen = 'La durée ne peut pas dépasser 8 heures (480 minutes)';
        newErrors.push('La durée ne peut pas dépasser 8 heures');
      }
    }

    if (!formData.semestre) {
      newFieldErrors.semestre = 'Le semestre est requis';
      newErrors.push('Le semestre est requis');
    }

    setErrors(newErrors);
    setFieldErrors(newFieldErrors);
    return newErrors.length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setLoading(true);
    setErrors([]);

    try {
      const examenData = {
        id_matiere: parseInt(formData.id_matiere),
        id_classe: parseInt(formData.id_classe),
        id_enseignant: formData.id_enseignant ? parseInt(formData.id_enseignant) : null,
        type_examen: formData.type_examen as 'examen' | 'devoir' | 'contrôle' | 'participation',
        date_examen: formData.date_examen,
        duree_examen: parseInt(formData.duree_examen),
        semestre: formData.semestre as 'S1' | 'S2',
        commentaire: formData.commentaire || null
      };

      if (examen) {
        await updateExamen(examen.id_examen, examenData);
      } else {
        await addExamen(examenData);
      }

      onSuccess();
    } catch (error: any) {
      console.error('Erreur lors de la sauvegarde:', error);
      const errorMessage = error.response?.data?.message || 'Erreur lors de la sauvegarde';
      setErrors([errorMessage]);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {examen ? 'Modifier l\'examen' : 'Nouvel examen'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X size={24} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6">
          {/* Messages d'erreur globaux */}
          {errors.length > 0 && (
            <div className="mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
              <ul className="list-disc list-inside space-y-1">
                {errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Matière */}
            <Select
              name="id_matiere"
              label="Matière"
              value={formData.id_matiere}
              onChange={(e) => handleChange('id_matiere', e.target.value)}
              required
              error={fieldErrors.id_matiere}
              options={[
                { value: "", label: "Sélectionner une matière" },
                ...matieres.map((matiere) => ({
                  value: matiere.id_matiere.toString(),
                  label: matiere.nom_matiere_fr
                }))
              ]}
            />

            {/* Classe */}
            <Select
              name="id_classe"
              label="Classe"
              value={formData.id_classe}
              onChange={(e) => handleChange('id_classe', e.target.value)}
              required
              error={fieldErrors.id_classe}
              options={[
                { value: "", label: "Sélectionner une classe" },
                ...classes.map((classe) => ({
                  value: classe.id_classe.toString(),
                  label: classe.nom_classe
                }))
              ]}
            />

            {/* Type d'examen */}
            <Select
              name="type_examen"
              label="Type d'examen"
              value={formData.type_examen}
              onChange={(e) => handleChange('type_examen', e.target.value)}
              required
              error={fieldErrors.type_examen}
              options={[
                { value: "", label: "Sélectionner un type" },
                { value: "examen", label: "Examen" },
                { value: "devoir", label: "Devoir" },
                { value: "contrôle", label: "Contrôle" },
                { value: "participation", label: "Participation" }
              ]}
            />

            {/* Enseignant */}
            <Select
              name="id_enseignant"
              label="Enseignant (optionnel)"
              value={formData.id_enseignant}
              onChange={(e) => handleChange('id_enseignant', e.target.value)}
              error={fieldErrors.id_enseignant}
              options={[
                { value: "", label: "Aucun enseignant spécifique" },
                ...enseignants.map((enseignant) => ({
                  value: enseignant.id_enseignant.toString(),
                  label: `${enseignant.user?.prenom} ${enseignant.user?.nom}`
                }))
              ]}
            />

            {/* Date */}
            <Input
              name="date_examen"
              label="Date de l'examen"
              type="date"
              value={formData.date_examen}
              onChange={(e) => handleChange('date_examen', e.target.value)}
              required
              error={fieldErrors.date_examen}
            />

            {/* Durée */}
            <Input
              name="duree_examen"
              label="Durée (en minutes)"
              type="number"
              min="1"
              max="480"
              value={formData.duree_examen}
              onChange={(e) => handleChange('duree_examen', e.target.value)}
              required
              placeholder="Ex: 120 pour 2h"
              error={fieldErrors.duree_examen}
            />

            {/* Semestre */}
            <Select
              name="semestre"
              label="Semestre"
              value={formData.semestre}
              onChange={(e) => handleChange('semestre', e.target.value)}
              required
              error={fieldErrors.semestre}
              options={[
                { value: "", label: "Sélectionner un semestre" },
                { value: "S1", label: "Semestre 1" },
                { value: "S2", label: "Semestre 2" }
              ]}
            />
          </div>

          {/* Commentaire */}
          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Commentaire
            </label>
            <textarea
              value={formData.commentaire}
              onChange={(e) => handleChange('commentaire', e.target.value)}
              rows={3}
              className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 sm:text-sm ${
                fieldErrors.commentaire 
                  ? 'border-red-300 focus:border-red-500 focus:ring-red-500' 
                  : 'border-gray-300 focus:border-blue-600 focus:ring-blue-600'
              }`}
              placeholder="Commentaire optionnel sur l'examen..."
            />
            {fieldErrors.commentaire && (
              <p className="mt-1 text-sm text-red-600">
                {fieldErrors.commentaire}
              </p>
            )}
          </div>

          {/* Boutons */}
          <div className="flex justify-end space-x-3 mt-6">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={loading}
            >
              Annuler
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={loading}
            >
              {examen ? 'Modifier' : 'Créer'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ExamenForm;
