import React, { useState, useEffect } from 'react';
import { Calendar, Clock, MapPin, User, X, ChevronLeft, ChevronRight, Users, AlertCircle } from 'lucide-react';
import Card from './Card';
import Button from './Button';
import { getCours, getCoursByClasse, getEleves } from '../services/api';

interface Cours {
  id_cours: number;
  jour_semaine: string;
  heure_debut: string;
  heure_fin: string;
  type_cours: string;
  nom_matiere: string;
  couleur_matiere?: string;
  nom_enseignant: string;
  nom_salle: string;
  type_salle?: string;
  nom_classe?: string;
}

interface EmploiTemps {
  lundi: Cours[];
  mardi: Cours[];
  mercredi: Cours[];
  jeudi: Cours[];
  vendredi: Cours[];
}

interface EmploiTempsParentProps {
  eleveId: number;
  eleveNom: string;
  onClose: () => void;
}

const EmploiTempsParent: React.FC<EmploiTempsParentProps> = ({ eleveId, eleveNom, onClose }) => {
  console.log("🎓 EmploiTempsParent - Composant créé avec:", { eleveId, eleveNom });

  const [emploiTemps, setEmploiTemps] = useState<EmploiTemps | null>(null);
  const [coursAujourdhui, setCoursAujourdhui] = useState<Cours[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>("");
  const [semaineCourante, setSemaineCourante] = useState(0);

  const jours = [
    { key: 'lundi', nom: 'Lundi', court: 'Lun' },
    { key: 'mardi', nom: 'Mardi', court: 'Mar' },
    { key: 'mercredi', nom: 'Mercredi', court: 'Mer' },
    { key: 'jeudi', nom: 'Jeudi', court: 'Jeu' },
    { key: 'vendredi', nom: 'Vendredi', court: 'Ven' }
  ];

  const heures = [
    '08:00', '08:30', '09:00', '09:30', '10:00', '10:30',
    '11:00', '11:30', '12:00', '12:30', '13:00', '13:30',
    '14:00', '14:30', '15:00', '15:30'
  ];

  useEffect(() => {
    const fetchEmploiTemps = async () => {
      setLoading(true);
      setError("");

      try {
        console.log("🔄 Chargement emploi du temps pour élève:", eleveId);

        // D'abord, récupérer les informations de l'élève pour connaître sa classe
        const elevesResponse = await getEleves();
        console.log("👥 Élèves API response:", elevesResponse);

        let tousCours = [];

        if (elevesResponse.data.success) {
          const eleve = elevesResponse.data.data.find((e: any) => e.id_eleve === eleveId);
          console.log("🎓 Élève trouvé:", eleve);

          if (eleve && eleve.id_classe) {
            // Récupérer les cours de la classe de l'élève
            const coursResponse = await getCoursByClasse(eleve.id_classe);
            console.log("📚 Cours de la classe API response:", coursResponse);

            if (coursResponse.data.success) {
              tousCours = coursResponse.data.data;
              console.log("📊 Cours de la classe récupérés:", tousCours.length);
            }
          } else {
            console.log("⚠️ Élève sans classe assignée, récupération de tous les cours");
            // Fallback : récupérer tous les cours
            const coursResponse = await getCours();
            if (coursResponse.data.success) {
              tousCours = coursResponse.data.data;
            }
          }
        } else {
          console.log("⚠️ Impossible de récupérer les élèves, récupération de tous les cours");
          // Fallback : récupérer tous les cours
          const coursResponse = await getCours();
          if (coursResponse.data.success) {
            tousCours = coursResponse.data.data;
          }
        }

        if (tousCours.length > 0) {
          console.log("📊 Total cours à traiter:", tousCours.length);

          // Organiser les cours par jour de la semaine
          const emploiTempsOrganise: EmploiTemps = {
            lundi: [],
            mardi: [],
            mercredi: [],
            jeudi: [],
            vendredi: [],
            samedi: []
          };

          // Convertir les cours en format attendu et les organiser par jour
          tousCours.forEach((cours: any) => {
            const coursFormate = {
              id_cours: cours.id_cours,
              jour_semaine: cours.jour_semaine.toLowerCase(),
              heure_debut: cours.heure_debut,
              heure_fin: cours.heure_fin,
              type_cours: 'cours',
              nom_matiere: cours.nom_matiere_fr || cours.nom_unite,
              couleur_matiere: '#3B82F6',
              nom_enseignant: cours.nom_enseignant || 'Enseignant',
              nom_salle: cours.nom_salle || 'Salle'
            };

            const jourKey = cours.jour_semaine.toLowerCase() as keyof EmploiTemps;
            if (emploiTempsOrganise[jourKey]) {
              emploiTempsOrganise[jourKey].push(coursFormate);
            }
          });

          setEmploiTemps(emploiTempsOrganise);

          // Filtrer les cours d'aujourd'hui
          const aujourdhui = new Date();
          const joursMap = ['dimanche', 'lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi', 'samedi'];
          const jourAujourdhui = joursMap[aujourdhui.getDay()];

          const coursAujourdhui = emploiTempsOrganise[jourAujourdhui as keyof EmploiTemps] || [];
          setCoursAujourdhui(coursAujourdhui);

          console.log("✅ Emploi du temps organisé:", emploiTempsOrganise);
          console.log("📅 Cours aujourd'hui (" + jourAujourdhui + "):", coursAujourdhui);
        } else {
          console.log("⚠️ Pas de cours trouvés");
          setEmploiTemps({
            lundi: [],
            mardi: [],
            mercredi: [],
            jeudi: [],
            vendredi: [],
            samedi: []
          });
          setCoursAujourdhui([]);
        }

      } catch (error) {
        console.error("💥 Erreur lors du chargement des données:", error);
        setError("Erreur de connexion - Données non disponibles");

        // En cas d'erreur, afficher un emploi du temps vide
        setEmploiTemps({
          lundi: [],
          mardi: [],
          mercredi: [],
          jeudi: [],
          vendredi: [],
          samedi: []
        });
        setCoursAujourdhui([]);
      } finally {
        setLoading(false);
      }
    };

    fetchEmploiTemps();
  }, [eleveId]);

  const formatHeure = (heure: string) => {
    return heure.substring(0, 5); // "08:00:00" -> "08:00"
  };

  const getCoursColor = (matiere: string, couleur?: string) => {
    if (couleur) return couleur;
    
    const colors: { [key: string]: string } = {
      'Mathématiques': '#3B82F6',
      'Français': '#10B981',
      'Histoire-Géographie': '#F59E0B',
      'Anglais': '#8B5CF6',
      'Sciences': '#EF4444',
      'EPS': '#84CC16',
      'Arts': '#F97316'
    };
    
    return colors[matiere] || '#6B7280';
  };

  const getDateSemaine = (jourIndex: number, semaineOffset: number = 0) => {
    const aujourd = new Date();
    const lundiCetteSemaine = new Date(aujourd);
    lundiCetteSemaine.setDate(aujourd.getDate() - aujourd.getDay() + 1 + (semaineOffset * 7));
    
    const dateJour = new Date(lundiCetteSemaine);
    dateJour.setDate(lundiCetteSemaine.getDate() + jourIndex);
    
    return dateJour.toLocaleDateString('fr-FR', { day: 'numeric', month: 'short' });
  };

  const isAujourdhui = (jourKey: string) => {
    const aujourd = new Date();
    const jourActuel = aujourd.getDay(); // 0 = dimanche, 1 = lundi, etc.
    const joursMap: { [key: string]: number } = {
      'lundi': 1, 'mardi': 2, 'mercredi': 3, 'jeudi': 4, 'vendredi': 5, 'samedi': 6
    };
    
    return semaineCourante === 0 && joursMap[jourKey] === jourActuel;
  };

  const CoursCard = ({ cours }: { cours: Cours }) => {
    const couleur = getCoursColor(cours.nom_matiere, cours.couleur_matiere);
    
    return (
      <div
        className="p-3 rounded-lg border-l-4 shadow-sm hover:shadow-md transition-shadow flex flex-col"
        style={{
          borderLeftColor: couleur,
          backgroundColor: `${couleur}10`
        }}
      >
        <div className="flex items-start justify-between">
          <h4 className="font-medium text-gray-900 text-sm flex-1">{cours.nom_matiere}</h4>
          {cours.type_cours !== 'cours' && (
            <span className="text-xs px-2 py-1 bg-orange-100 text-orange-800 rounded-full">
              {cours.type_cours}
            </span>
          )}
        </div>
        <div className="flex flex-col space-y-1 mt-1 flex-1">
          <div className="flex items-center gap-2 text-xs text-gray-600">
            <Clock className="w-4 h-4" />
            <span>{formatHeure(cours.heure_debut)} - {formatHeure(cours.heure_fin)}</span>
          </div>
          <div className="flex items-center gap-2 text-xs text-gray-600">
            <User className="w-4 h-4" />
            <span>{cours.nom_enseignant}</span>
          </div>
          <div className="flex items-center gap-2 text-xs text-gray-600">
            <MapPin className="w-4 h-4" />
            <span>{cours.nom_salle}</span>
          </div>
        </div>
      </div>
    );
  };

  console.log("🎨 Rendu EmploiTempsParent - loading:", loading, "error:", error);

  if (loading) {
    console.log("⏳ Affichage du spinner de chargement");
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white p-8 rounded-lg">
          <div className="flex items-center gap-3">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span>Chargement de l'emploi du temps...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-7xl w-full max-h-[90vh] overflow-hidden">
        {/* En-tête */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-[#005bac] to-blue-600 text-white">
          <div className="flex items-center gap-3">
            <Calendar className="w-6 h-6" />
            <div>
              <h2 className="text-xl font-semibold">Emploi du temps</h2>
              <p className="text-blue-100">{eleveNom}</p>
            </div>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={onClose}
            className="text-white border-white hover:bg-white hover:text-blue-600"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-80px)]">
          {error && (
            <div className="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-center gap-2 text-yellow-800">
                <AlertCircle className="w-5 h-5" />
                <span>Données simulées - {error}</span>
              </div>
            </div>
          )}

          {/* Navigation semaine */}
          <div className="flex items-center justify-between mb-6">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSemaineCourante(semaineCourante - 1)}
            >
              <ChevronLeft className="w-4 h-4 mr-1" />
              Semaine précédente
            </Button>
            
            <h3 className="text-lg font-medium text-gray-900">
              {semaineCourante === 0 ? 'Cette semaine' : 
               semaineCourante > 0 ? `Dans ${semaineCourante} semaine${semaineCourante > 1 ? 's' : ''}` :
               `Il y a ${Math.abs(semaineCourante)} semaine${Math.abs(semaineCourante) > 1 ? 's' : ''}`}
            </h3>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSemaineCourante(semaineCourante + 1)}
            >
              Semaine suivante
              <ChevronRight className="w-4 h-4 ml-1" />
            </Button>
          </div>

          {/* Cours d'aujourd'hui */}
          {semaineCourante === 0 && coursAujourdhui.length > 0 && (
            <Card className="mb-6 bg-blue-50">
              <div className="flex items-center gap-2 mb-4">
                <Clock className="w-5 h-5 text-blue-600" />
                <h3 className="text-lg font-medium text-gray-900">Cours d'aujourd'hui</h3>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {coursAujourdhui.map((cours) => (
                  <CoursCard key={cours.id_cours} cours={cours} />
                ))}
              </div>
            </Card>
          )}

          {/* Grille emploi du temps */}
          <Card>
            <div className="flex items-center gap-2 mb-4">
              <Users className="w-5 h-5 text-green-600" />
              <h3 className="text-lg font-medium text-gray-900">Planning hebdomadaire</h3>
            </div>
            
            {emploiTemps ? (
              <div className="overflow-x-auto">
                <div className="grid grid-cols-7 gap-2 min-w-[800px]">
                  {/* En-tête avec les jours */}
                  <div className="p-2"></div> {/* Cellule vide pour l'alignement */}
                  {jours.map((jour, index) => (
                    <div 
                      key={jour.key} 
                      className={`p-3 text-center font-medium rounded-lg ${
                        isAujourdhui(jour.key) 
                          ? 'bg-blue-100 text-blue-800 border-2 border-blue-300' 
                          : 'bg-gray-50 text-gray-700'
                      }`}
                    >
                      <div className="text-sm">{jour.nom}</div>
                      <div className="text-xs text-gray-500 mt-1">
                        {getDateSemaine(index, semaineCourante)}
                      </div>
                    </div>
                  ))}

                  {/* Grille des cours */}
                  {heures.map((heure) => (
                    <React.Fragment key={heure}>
                      {/* Colonne des heures */}
                      <div className="p-2 text-center text-sm font-medium text-gray-600 border-r">
                        {heure}
                      </div>
                      
                      {/* Colonnes des jours */}
                      {jours.map((jour) => {
                        const coursHeure = emploiTemps[jour.key as keyof EmploiTemps]?.find(
                          cours => formatHeure(cours.heure_debut) === heure
                        );
                        
                        return (
                          <div key={`${jour.key}-${heure}`} className="p-1 min-h-[60px]">
                            {coursHeure ? (
                              <CoursCard cours={coursHeure} />
                            ) : (
                              <div className="h-full border border-gray-100 rounded-lg"></div>
                            )}
                          </div>
                        );
                      })}
                    </React.Fragment>
                  ))}
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Calendar className="w-12 h-12 mx-auto mb-2 text-gray-300" />
                <p>Aucun emploi du temps disponible</p>
              </div>
            )}
          </Card>

          {/* Légende */}
          <Card className="mt-6">
            <h4 className="font-medium text-gray-900 mb-3">Légende</h4>
            <div className="flex flex-wrap gap-4 text-sm">
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-blue-500 rounded"></div>
                <span>Cours normal</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-orange-500 rounded"></div>
                <span>Contrôle/Examen</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-green-500 rounded"></div>
                <span>Travaux pratiques</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4 text-gray-600" />
                <span>Horaire</span>
              </div>
              <div className="flex items-center gap-2">
                <User className="w-4 h-4 text-gray-600" />
                <span>Enseignant</span>
              </div>
              <div className="flex items-center gap-2">
                <MapPin className="w-4 h-4 text-gray-600" />
                <span>Salle</span>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default EmploiTempsParent;
