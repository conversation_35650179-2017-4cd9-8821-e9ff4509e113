<?php

class Inscription
{
    private $pdo;
    private $table = "Inscription";

    public function __construct($db)
    {
        $this->pdo = $db;
    }

    public function create($id_eleve, $id_annee_scolaire, $id_classe)
    {
        try {
            $query = "INSERT INTO " . $this->table . " (id_eleve, id_annee_scolaire, id_classe) 
                        VALUES (:id_eleve, :id_annee_scolaire, :id_classe)";
            $stmt = $this->pdo->prepare($query);
            $stmt->bindParam(':id_eleve', $id_eleve, PDO::PARAM_INT);
            $stmt->bindParam(':id_annee_scolaire', $id_annee_scolaire, PDO::PARAM_INT);
            $stmt->bindParam(':id_classe', $id_classe, PDO::PARAM_INT);

            if ($stmt->execute()) {
                return true;
            } else {
                error_log("Erreur SQL Inscription : " . implode(", ", $stmt->errorInfo()));
                return false;
            }
        } catch (PDOException $e) {
            error_log("Erreur PDO dans Inscription::create : " . $e->getMessage());
            return false;
        }
    }

    public function update($id_eleve, $id_annee_scolaire, $old_id_classe, $new_id_classe)
    {
        try {
            $query = "UPDATE " . $this->table . " SET id_classe = :new_id_classe 
            WHERE id_eleve = :id_eleve AND id_annee_scolaire = :id_annee_scolaire 
            AND id_classe = :old_id_classe";
            $stmt = $this->pdo->prepare($query);
            $stmt->bindParam(':new_id_classe', $new_id_classe, PDO::PARAM_INT);
            $stmt->bindParam(':id_eleve', $id_eleve, PDO::PARAM_INT);
            $stmt->bindParam(':id_annee_scolaire', $id_annee_scolaire, PDO::PARAM_INT);
            $stmt->bindParam(':old_id_classe', $old_id_classe, PDO::PARAM_INT);

            if ($stmt->execute()) {
                return $stmt->rowCount() > 0;
            } else {
                error_log("Erreur SQL Inscription update : " . implode(", ", $stmt->errorInfo()));
                return false;
            }
        } catch (PDOException $e) {
            error_log("Erreur PDO dans Inscription::update : " . $e->getMessage());
            return false;
        }
    }

    public function getInscriptionByEleve($id_eleve)
    {
        $query = "SELECT i.* FROM " . $this->table . " i
                      INNER JOIN Annee_Scolaire a ON i.id_annee_scolaire = a.id_annee_scolaire
                      WHERE i.id_eleve = :id_eleve AND a.est_active = 1";
        $stmt = $this->pdo->prepare($query);
        $stmt->bindParam(':id_eleve', $id_eleve, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
}