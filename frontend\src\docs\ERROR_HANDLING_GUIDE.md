# Guide de Gestion d'Erreur - ScolaNova

## 🎯 Objectif

Ce système de gestion d'erreur personnalisé permet d'afficher des messages d'erreur spécifiques du backend au lieu des messages génériques comme "Request failed with status code 500".

## 🔧 Composants du Système

### 1. Utilitaires d'Erreur (`utils/errorUtils.ts`)

- `extractErrorMessage()` - Extrait le message d'erreur du backend
- `extractValidationErrors()` - Extrait les erreurs de validation par champ
- `handleGlobalError()` - Gestion globale avec logging
- `isNetworkError()` - Détecte les erreurs de réseau
- `isAuthError()` - Détecte les erreurs d'authentification

### 2. Intercepteur Axios (`services/api.ts`)

Intercepte automatiquement toutes les réponses d'erreur et ajoute `error.userMessage` avec le message du backend.

### 3. Hook Personnalisé (`hooks/useErrorHandler.ts`)

- `useErrorHandler()` - Hook de base pour la gestion d'erreur
- `useFormErrorHandler()` - Hook spécialisé pour les formulaires

### 4. Composant d'Affichage (`components/ErrorDisplay.tsx`)

Composant réutilisable pour afficher les erreurs avec différents styles.

## 🚀 Utilisation

### Méthode 1: Utilisation Simple (Recommandée)

```tsx
import { extractErrorMessage } from '../utils/errorUtils';

const handleSubmit = async () => {
  try {
    await apiCall();
  } catch (error: any) {
    // Le message du backend est automatiquement extrait par l'intercepteur
    const errorMessage = error.userMessage || extractErrorMessage(error, 'Erreur par défaut');
    setMessage({ type: 'error', text: errorMessage });
  }
};
```

### Méthode 2: Avec Hook (Pour Formulaires Complexes)

```tsx
import { useFormErrorHandler } from '../hooks/useErrorHandler';
import ErrorDisplay from '../components/ErrorDisplay';

const MyComponent = () => {
  const { error, validationErrors, clearError, executeWithErrorHandling } = useFormErrorHandler();

  const handleSubmit = async () => {
    await executeWithErrorHandling(
      () => apiCall(),
      'sauvegarde des données',
      (result) => console.log('Succès:', result)
    );
  };

  return (
    <div>
      <ErrorDisplay error={error} onClose={clearError} />
      <Input error={validationErrors.fieldName} />
    </div>
  );
};
```

## 📋 Types d'Erreurs Gérées

### 1. Erreurs de Validation (422)
- Affichées sous les champs concernés
- Extraites automatiquement du backend

### 2. Erreurs Métier (400, 409)
- Messages spécifiques du backend
- Ex: "Cet email existe déjà"

### 3. Erreurs d'Authentification (401, 403)
- Messages de session expirée
- Redirection automatique possible

### 4. Erreurs Serveur (500)
- Messages personnalisés du backend
- Fallback vers messages génériques

### 5. Erreurs Réseau
- Détection automatique
- Messages adaptés

## 🎨 Styles d'Erreur

Le composant `ErrorDisplay` supporte plusieurs variantes :
- `error` (rouge) - Erreurs critiques
- `warning` (jaune) - Avertissements
- `info` (bleu) - Informations

## 📝 Exemples Concrets

### Backend Response Examples

```json
// Erreur de validation
{
  "success": false,
  "message": "Données invalides",
  "errors": {
    "email": ["L'email est déjà utilisé"],
    "nom": ["Le nom est requis"]
  }
}

// Erreur métier
{
  "success": false,
  "message": "Cette année scolaire existe déjà"
}

// Erreur serveur
{
  "success": false,
  "message": "Erreur lors de la mise à jour de l'élève"
}
```

### Frontend Usage

```tsx
// Avant (message générique)
catch (error) {
  setMessage("Request failed with status code 500");
}

// Après (message spécifique)
catch (error) {
  const errorMessage = error.userMessage || extractErrorMessage(error, 'Erreur par défaut');
  setMessage(errorMessage); // "Erreur lors de la mise à jour de l'élève"
}
```

## 🔍 Debugging

L'intercepteur Axios log automatiquement :
- URL de la requête
- Méthode HTTP
- Status code
- Message du backend
- Erreur complète

## 📚 Migration des Composants Existants

1. Importer `extractErrorMessage`
2. Remplacer `error.message` par `error.userMessage || extractErrorMessage(error, 'default')`
3. Optionnel: Utiliser `useFormErrorHandler` pour les formulaires complexes
4. Optionnel: Utiliser `ErrorDisplay` pour un affichage cohérent

## ✅ Avantages

- Messages d'erreur spécifiques du backend
- Gestion automatique des différents types d'erreurs
- Logging centralisé pour le debugging
- Composants réutilisables
- Migration progressive possible
- Expérience utilisateur améliorée
