-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.1.2
-- https://www.phpmyadmin.net/
--
-- Hôte : localhost:3306
-- <PERSON><PERSON><PERSON><PERSON> le : lun. 23 juin 2025 à 07:28
-- Version du serveur : 5.7.24
-- Version de PHP : 8.3.1

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Base de données : `scolanova`
--

-- --------------------------------------------------------

--
-- Structure de la table `absence_eleve`
--

CREATE TABLE `absence_eleve` (
  `id_absence_eleve` int(11) NOT NULL,
  `id_eleve` int(11) NOT NULL,
  `id_classe` int(11) NOT NULL,
  `date_debut` datetime NOT NULL,
  `date_fin` datetime NOT NULL,
  `duree` int(11) GENERATED ALWAYS AS (timestampdiff(MINUTE,`date_debut`,`date_fin`)) STORED,
  `motif` enum('maladie','familial','autre') NOT NULL,
  `justifiee` tinyint(1) DEFAULT '0',
  `commentaire` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `absence_enseignant`
--

CREATE TABLE `absence_enseignant` (
  `id_absence_enseignant` int(11) NOT NULL,
  `id_enseignant` int(11) NOT NULL,
  `date_debut` date NOT NULL,
  `date_fin` date NOT NULL,
  `type` enum('absence','retard') NOT NULL,
  `justifiee` tinyint(1) DEFAULT '0',
  `motif` enum('maladie','congé personnel','formation','autre') NOT NULL,
  `commentaire` text,
  `id_remplacant` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `activite`
--

CREATE TABLE `activite` (
  `id_activite` int(11) NOT NULL,
  `id_annee_scolaire` int(11) NOT NULL,
  `nom_activite` varchar(50) NOT NULL,
  `type_activite` enum('sportive','culturelle','autre') NOT NULL,
  `date_debut` date DEFAULT NULL,
  `date_fin` date DEFAULT NULL,
  `prix` decimal(6,2) NOT NULL,
  `description` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `ancienne_ecole`
--

CREATE TABLE `ancienne_ecole` (
  `code_gresa` varchar(10) NOT NULL,
  `nom` varchar(100) NOT NULL,
  `type` enum('publique','privée') NOT NULL,
  `cycle` enum('maternelle','primaire','collège','lycée') NOT NULL,
  `adresse` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Déchargement des données de la table `ancienne_ecole`
--

INSERT INTO `ancienne_ecole` (`code_gresa`, `nom`, `type`, `cycle`, `adresse`) VALUES
('GRESA001', 'École primaire Al Amal', 'publique', 'primaire', 'Casablanca, Maroc'),
('GRESA002', 'École Jabrane', 'publique', 'primaire', 'Rabat, Maroc'),
('GRESA003', 'École Al Imane', 'privée', 'primaire', 'Fès, Maroc'),
('GRESA004', 'École Al Hidaya', 'publique', 'primaire', 'Marrakech, Maroc'),
('GRESA005', 'École El Moutaheda', 'privée', 'primaire', 'Agadir, Maroc'),
('GRESA006', 'École Mohammed VI', 'publique', 'primaire', 'Tangier, Maroc'),
('GRESA007', 'École Aswat', 'privée', 'primaire', 'Meknes, Maroc'),
('GRESA008', 'École Al Nour', 'publique', 'primaire', 'Tétouan, Maroc'),
('GRESA009', 'École Ibn Sina', 'privée', 'primaire', 'Marrakech, Maroc'),
('GRESA010', 'École Jabal Al Noor', 'publique', 'primaire', 'Rabat, Maroc'),
('GRESA011', 'École Al Farabi', 'privée', 'primaire', 'Casablanca, Maroc'),
('GRESA012', 'École Al Fath', 'publique', 'primaire', 'Salé, Maroc');

-- --------------------------------------------------------

--
-- Structure de la table `annee_scolaire`
--

CREATE TABLE `annee_scolaire` (
  `id_annee_scolaire` int(11) NOT NULL,
  `libelle` varchar(9) NOT NULL,
  `date_debut` date DEFAULT NULL,
  `date_fin` date DEFAULT NULL,
  `est_active` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Déchargement des données de la table `annee_scolaire`
--

INSERT INTO `annee_scolaire` (`id_annee_scolaire`, `libelle`, `date_debut`, `date_fin`, `est_active`) VALUES
(3, '2023-2024', '2023-09-01', '2024-06-30', 0),
(4, '2024-2025', '2024-09-01', '2025-06-30', 1);

-- --------------------------------------------------------

--
-- Structure de la table `beneficier_transport`
--

CREATE TABLE `beneficier_transport` (
  `id_eleve` int(11) NOT NULL,
  `id_transport` int(11) NOT NULL,
  `id_annee_scolaire` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `bulletin`
--

CREATE TABLE `bulletin` (
  `id_bulletin` int(11) NOT NULL,
  `id_eleve` int(11) NOT NULL,
  `id_annee_scolaire` int(11) NOT NULL,
  `semestre` enum('S1','S2') NOT NULL,
  `moyenne_generale` decimal(4,2) NOT NULL,
  `appreciation_generale` text,
  `date_creation` datetime DEFAULT CURRENT_TIMESTAMP,
  `date_maj` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `bulletin_matiere`
--

CREATE TABLE `bulletin_matiere` (
  `id_bulletin` int(11) NOT NULL,
  `id_matiere` int(11) NOT NULL,
  `moyenne_matiere` decimal(4,2) NOT NULL,
  `appreciation` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `classe`
--

CREATE TABLE `classe` (
  `id_classe` int(11) NOT NULL,
  `id_niveau` int(11) NOT NULL,
  `nom_classe` varchar(20) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Déchargement des données de la table `classe`
--

INSERT INTO `classe` (`id_classe`, `id_niveau`, `nom_classe`) VALUES
(1, 1, 'PS G1'),
(2, 2, 'MS G1'),
(4, 4, 'CP G1'),
(3, 5, 'CE1 G1'),
(6, 6, 'CE2 G1'),
(7, 10, 'CM1 G1'),
(8, 11, 'CM2 G1');

-- --------------------------------------------------------

--
-- Structure de la table `classe_activite`
--

CREATE TABLE `classe_activite` (
  `id_classe` int(11) NOT NULL,
  `id_activite` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `contrat`
--

CREATE TABLE `contrat` (
  `id_contrat` int(11) NOT NULL,
  `id_enseignant` int(11) NOT NULL,
  `type_contrat` enum('CDI','CDD','Vacataire','Autre') NOT NULL,
  `poste` enum('enseignant','administratif','autre') NOT NULL,
  `date_debut` date NOT NULL,
  `date_fin` date DEFAULT NULL,
  `salaire_base` decimal(10,2) NOT NULL,
  `statut` enum('actif','terminé','suspendu') DEFAULT 'actif',
  `description` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Déchargement des données de la table `contrat`
--

INSERT INTO `contrat` (`id_contrat`, `id_enseignant`, `type_contrat`, `poste`, `date_debut`, `date_fin`, `salaire_base`, `statut`, `description`) VALUES
(6, 5, 'CDI', 'enseignant', '2021-09-01', NULL, '15000.00', 'actif', 'Enseignant permanent en informatique'),
(7, 6, 'CDD', 'enseignant', '2020-11-15', '2021-11-15', '12000.00', 'terminé', 'Enseignant vacataire en finance'),
(8, 7, 'CDI', 'enseignant', '2018-07-10', NULL, '16000.00', 'actif', 'Professeur titulaire en mathématiques'),
(9, 8, 'CDI', 'enseignant', '2022-02-20', NULL, '14000.00', 'actif', 'Professeur en économie'),
(10, 9, 'CDD', 'enseignant', '2019-05-30', '2020-05-30', '13000.00', 'terminé', 'Enseignant vacataire en gestion'),
(11, 4, 'CDI', 'enseignant', '2020-09-25', NULL, '15500.00', 'actif', 'Professeur en droit'),
(12, 6, 'CDD', 'enseignant', '2025-06-02', NULL, '5000.00', 'actif', NULL),
(13, 9, 'CDD', 'enseignant', '2025-05-26', NULL, '5000.00', 'actif', NULL),
(14, 10, 'CDD', 'enseignant', '2025-06-03', NULL, '5000.00', 'actif', NULL);

-- --------------------------------------------------------

--
-- Structure de la table `cours`
--

CREATE TABLE `cours` (
  `id_cours` int(11) NOT NULL,
  `id_unite` int(11) NOT NULL,
  `id_enseignant` int(11) NOT NULL,
  `id_classe` int(11) NOT NULL,
  `id_salle` int(11) NOT NULL,
  `jour_semaine` enum('Lundi','Mardi','Mercredi','Jeudi','Vendredi','Samedi') NOT NULL,
  `heure_debut` time NOT NULL,
  `heure_fin` time NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Déchargement des données de la table `cours`
--

INSERT INTO `cours` (`id_cours`, `id_unite`, `id_enseignant`, `id_classe`, `id_salle`, `jour_semaine`, `heure_debut`, `heure_fin`) VALUES
(1, 15, 4, 8, 1, 'Jeudi', '09:30:00', '11:00:00'),
(2, 11, 5, 8, 3, 'Lundi', '09:30:00', '10:00:00');

-- --------------------------------------------------------

--
-- Structure de la table `diplome`
--

CREATE TABLE `diplome` (
  `id_diplome` int(11) NOT NULL,
  `id_enseignant` int(11) NOT NULL,
  `intitule` varchar(100) NOT NULL,
  `institut` varchar(100) NOT NULL,
  `specialite` varchar(50) DEFAULT NULL,
  `date_promotion` date NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Déchargement des données de la table `diplome`
--

INSERT INTO `diplome` (`id_diplome`, `id_enseignant`, `intitule`, `institut`, `specialite`, `date_promotion`) VALUES
(11, 4, 'Master en Informatique', 'Université Hassan II', 'Informatique', '2020-06-15'),
(12, 5, 'Licence en Finance', 'Université Mohamed V', 'Finance', '2018-09-10'),
(13, 6, 'Doctorat en Mathématiques', 'Université Chouaib Doukkali', 'Mathématiques', '2016-05-22'),
(14, 7, 'Master en Économie', 'Université Al Akhawayn', 'Économie', '2021-11-05'),
(15, 8, 'Licence en Gestion', 'Université Cadi Ayyad', 'Gestion', '2019-03-30'),
(16, 9, 'Doctorat en Droit', 'Université Mohammed V', 'Droit', '2015-07-14'),
(17, 10, 'a', 'a', 'a', '2025-06-05');

-- --------------------------------------------------------

--
-- Structure de la table `ecole`
--

CREATE TABLE `ecole` (
  `id_ecole` int(11) NOT NULL,
  `nom_ecole` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `adresse` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `telephone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `site_web` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `directeur` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `logo` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `type_etablissement` enum('public','prive','semi_prive') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'prive',
  `cycles_proposes` text COLLATE utf8mb4_unicode_ci,
  `date_creation` date DEFAULT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `ecole`
--

INSERT INTO `ecole` (`id_ecole`, `nom_ecole`, `adresse`, `telephone`, `email`, `site_web`, `directeur`, `logo`, `type_etablissement`, `cycles_proposes`, `date_creation`, `description`, `created_at`, `updated_at`) VALUES
(1, 'ScolaNova', '123 Avenue Mohammed V, Casablanca, Maroc', '+212 522 123 456', '<EMAIL>', 'https://www.scolanova.ma', 'Dr. Ahmed Benali', NULL, 'prive', 'primaire,college,lycee', '2020-09-01', 'École privée moderne offrant un enseignement de qualité du primaire au lycée.', '2025-06-10 12:06:58', '2025-06-10 12:06:58');

-- --------------------------------------------------------

--
-- Structure de la table `eleve`
--

CREATE TABLE `eleve` (
  `id_eleve` int(11) NOT NULL,
  `id_utilisateur` int(11) NOT NULL,
  `code_massar` varchar(10) DEFAULT NULL,
  `code_gresa` char(10) DEFAULT NULL,
  `nom_ar` varchar(50) NOT NULL,
  `prenom_ar` varchar(50) NOT NULL,
  `lieu_naissance_ar` varchar(100) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Déchargement des données de la table `eleve`
--

INSERT INTO `eleve` (`id_eleve`, `id_utilisateur`, `code_massar`, `code_gresa`, `nom_ar`, `prenom_ar`, `lieu_naissance_ar`) VALUES
(34, 23, 'MASSAR001', 'GRESA001', 'الهادي', 'رشيد', 'الدار البيضاء'),
(35, 24, 'MASSAR002', 'GRESA002', 'أمينة', 'نورة', 'الرباط'),
(36, 25, 'MASSAR003', 'GRESA003', 'ياسر', 'محمد', 'فاس'),
(37, 26, 'MASSAR004', 'GRESA004', 'ليلى', 'أحمد', 'مراكش'),
(38, 27, 'MASSAR005', 'GRESA005', 'عمر', 'سليم', 'أكادير'),
(39, 28, 'MASSAR006', 'GRESA006', 'إيمان', 'لحبابي', 'طنجة'),
(40, 29, 'MASSAR007', 'GRESA007', 'مُنير', 'الطا زي', 'مكناس'),
(41, 30, 'MASSAR008', 'GRESA008', 'سناء', 'محفوظ', 'تطوان'),
(42, 31, 'MASSAR009', 'GRESA009', 'أنس', 'بوحدود', 'مراكش'),
(43, 32, 'MASSAR010', 'GRESA010', 'مريم', 'لعزيز', 'الرباط'),
(44, 33, 'MASSAR011', 'GRESA011', 'حمزة', 'رشيد', 'الدار البيضاء'),
(45, 34, 'MASSAR012', 'GRESA012', 'أسماء', 'زروال', 'سلا'),
(46, 50, NULL, NULL, 'karim', 'said', 'casa');

-- --------------------------------------------------------

--
-- Structure de la table `enseignant`
--

CREATE TABLE `enseignant` (
  `id_enseignant` int(11) NOT NULL,
  `id_utilisateur` int(11) NOT NULL,
  `num_CIN` char(10) NOT NULL,
  `num_CNSS` char(10) DEFAULT NULL,
  `situation_familiale` enum('C','M','D','V') NOT NULL,
  `nombre_enfants` int(11) DEFAULT '0',
  `date_embauche` date NOT NULL,
  `banque` varchar(50) DEFAULT NULL,
  `rib` char(24) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Déchargement des données de la table `enseignant`
--

INSERT INTO `enseignant` (`id_enseignant`, `id_utilisateur`, `num_CIN`, `num_CNSS`, `situation_familiale`, `nombre_enfants`, `date_embauche`, `banque`, `rib`) VALUES
(4, 35, 'C00001', 'CNSS001', 'C', 2, '2021-09-01', 'Banque Populaire', 'RIB001'),
(5, 36, 'C00002', 'CNSS002', 'M', 0, '2020-11-15', 'Attijariwafa Bank', 'RIB002'),
(6, 37, 'C98003', 'CNSS003', 'D', 1, '2018-07-10', 'Banque de l’Habitat', 'RIB003'),
(7, 38, 'C12304', 'CNSS004', 'V', 3, '2022-02-20', 'BMCE Bank', 'RIB004'),
(8, 39, 'C123005', 'CNSS005', 'M', 4, '2019-05-30', 'Société Générale', 'RIB005'),
(9, 40, 'C12006', 'CNSS006', 'C', 2, '2020-09-25', 'Crédit Agricole', 'RIB006'),
(10, 53, 'M12398', NULL, 'D', 0, '2025-06-12', NULL, NULL);

-- --------------------------------------------------------

--
-- Structure de la table `enseignant_matiere`
--

CREATE TABLE `enseignant_matiere` (
  `id_enseignant` int(11) NOT NULL,
  `id_matiere` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Déchargement des données de la table `enseignant_matiere`
--

INSERT INTO `enseignant_matiere` (`id_enseignant`, `id_matiere`) VALUES
(5, 13),
(7, 13),
(4, 14),
(8, 14),
(5, 15),
(8, 17),
(10, 17),
(9, 18),
(6, 19),
(6, 22);

-- --------------------------------------------------------

--
-- Structure de la table `examen`
--

CREATE TABLE `examen` (
  `id_examen` int(11) NOT NULL,
  `id_matiere` int(11) NOT NULL,
  `id_classe` int(11) NOT NULL,
  `id_enseignant` int(11) DEFAULT NULL,
  `type_examen` enum('examen','devoir','contrôle','participation') NOT NULL,
  `date_examen` date NOT NULL,
  `duree_examen` int(11) NOT NULL,
  `semestre` enum('S1','S2') NOT NULL,
  `commentaire` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `inscription`
--

CREATE TABLE `inscription` (
  `id_eleve` int(11) NOT NULL,
  `id_annee_scolaire` int(11) NOT NULL,
  `id_classe` int(11) NOT NULL,
  `date_inscription` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `statut` varchar(50) DEFAULT 'Inscrit'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Déchargement des données de la table `inscription`
--

INSERT INTO `inscription` (`id_eleve`, `id_annee_scolaire`, `id_classe`, `date_inscription`, `statut`) VALUES
(34, 3, 1, '2023-09-01 09:00:00', 'Inscrit'),
(35, 3, 2, '2023-09-01 09:00:00', 'Inscrit'),
(36, 3, 3, '2023-09-01 09:00:00', 'Inscrit'),
(37, 3, 4, '2023-09-01 09:00:00', 'Inscrit'),
(38, 4, 4, '2023-09-01 09:00:00', 'Inscrit'),
(39, 4, 6, '2023-09-01 09:00:00', 'Inscrit'),
(40, 4, 7, '2023-09-01 09:00:00', 'Inscrit'),
(41, 4, 8, '2023-09-01 09:00:00', 'Inscrit'),
(42, 4, 8, '2023-09-01 09:00:00', 'Inscrit'),
(43, 4, 8, '2023-09-01 09:00:00', 'Inscrit'),
(44, 4, 1, '2024-09-01 09:00:00', 'Inscrit'),
(45, 4, 2, '2024-09-01 09:00:00', 'Inscrit'),
(46, 4, 8, '2025-06-21 20:59:48', 'Inscrit');

-- --------------------------------------------------------

--
-- Structure de la table `matiere`
--

CREATE TABLE `matiere` (
  `id_matiere` int(11) NOT NULL,
  `nom_matiere_fr` varchar(50) NOT NULL,
  `nom_matiere_ar` varchar(50) NOT NULL,
  `description` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Déchargement des données de la table `matiere`
--

INSERT INTO `matiere` (`id_matiere`, `nom_matiere_fr`, `nom_matiere_ar`, `description`) VALUES
(13, 'Français', 'الفرنسية', 'Matière qui enseigne la langue française, la lecture et l’écriture.'),
(14, 'Arabe', 'اللغة العربية', 'Matière qui enseigne la langue arabe, la lecture et l’écriture.'),
(15, 'Mathématiques', 'الرياضيات', 'Étude des nombres, des opérations et des concepts mathématiques de base.'),
(16, 'Sciences de la vie et de la Terre', 'علوم الحياة والأرض', 'Étude des phénomènes naturels et de la biologie.'),
(17, 'Éducation Islamique', 'التربية الإسلامية', 'Enseignement des valeurs islamiques, des principes religieux et des rituels.'),
(18, 'Histoire-Géo', 'التاريخ والجغرافيا', 'Étude des événements historiques et de la géographie du monde et du Maroc.'),
(19, 'Éducation physique et sportive', 'التربية البدنية والرياضية', 'Activités physiques visant à développer les capacités motrices et sportives des élèves.'),
(20, 'Arts plastiques', 'الفنون التشكيلية', 'Création artistique avec des matériaux variés pour développer la créativité.'),
(22, 'Informatique', 'العلوم الكمبيوترية', 'Introduction à l’utilisation des ordinateurs et des outils numériques.'),
(23, 'Anglais', 'Anglais', NULL);

-- --------------------------------------------------------

--
-- Structure de la table `message`
--

CREATE TABLE `message` (
  `id_message` int(11) NOT NULL,
  `id_emetteur` int(11) NOT NULL,
  `id_recepteur` int(11) NOT NULL,
  `objet` varchar(100) NOT NULL,
  `contenu` text NOT NULL,
  `date_envoi` datetime DEFAULT CURRENT_TIMESTAMP,
  `lu` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `niveau`
--

CREATE TABLE `niveau` (
  `id_niveau` int(11) NOT NULL,
  `cycle` enum('maternelle','primaire','collège','lycée') NOT NULL,
  `libelle` varchar(50) NOT NULL,
  `prix_mensuel` decimal(6,2) NOT NULL,
  `frais_inscription` decimal(6,2) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Déchargement des données de la table `niveau`
--

INSERT INTO `niveau` (`id_niveau`, `cycle`, `libelle`, `prix_mensuel`, `frais_inscription`) VALUES
(1, 'maternelle', 'PS', '600.00', '1000.00'),
(2, 'maternelle', 'MS', '700.00', '1000.00'),
(4, 'primaire', 'CP', '800.00', '1000.00'),
(5, 'primaire', 'CE1', '1000.00', '1000.00'),
(6, 'primaire', 'CE2', '1200.00', '1000.00'),
(8, 'maternelle', 'GS', '0.00', '0.00'),
(10, 'primaire', 'CM1', '1000.00', '1000.00'),
(11, 'primaire', 'CM2', '12.00', '12.00'),
(12, 'primaire', 'CE6', '12.00', '76.00');

-- --------------------------------------------------------

--
-- Structure de la table `niveau_matiere`
--

CREATE TABLE `niveau_matiere` (
  `id_niveau` int(11) NOT NULL,
  `id_matiere` int(11) NOT NULL,
  `coefficient` int(11) NOT NULL DEFAULT '1',
  `Nb_heures` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `note`
--

CREATE TABLE `note` (
  `id_eleve` int(11) NOT NULL,
  `id_examen` int(11) NOT NULL,
  `note` decimal(4,2) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `paiement`
--

CREATE TABLE `paiement` (
  `id_paiement` int(11) NOT NULL,
  `id_eleve` int(11) NOT NULL,
  `id_annee_scolaire` int(11) NOT NULL,
  `montant` decimal(6,2) NOT NULL,
  `mois` enum('Janvier','Février','Mars','Avril','Mai','Juin','Juillet','Août','Septembre','Octobre','Novembre','Décembre') NOT NULL,
  `date_paiement` date DEFAULT NULL,
  `date_echeance` date DEFAULT NULL,
  `type_paiement` enum('scolarité','transport','cantine','inscription','autre') NOT NULL,
  `mode_paiement` enum('espèces','chèque','virement','carte') NOT NULL,
  `statut` enum('en attente','payé','retard','annule') DEFAULT 'en attente',
  `description` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Déchargement des données de la table `paiement`
--

INSERT INTO `paiement` (`id_paiement`, `id_eleve`, `id_annee_scolaire`, `montant`, `mois`, `date_paiement`, `date_echeance`, `type_paiement`, `mode_paiement`, `statut`, `description`) VALUES
(1, 46, 4, '12.00', 'Juin', '2025-06-22', NULL, 'scolarité', 'espèces', 'payé', NULL),
(2, 44, 4, '600.00', 'Juin', '2025-06-22', NULL, 'scolarité', 'espèces', 'retard', 'Paiement automatique - Juin 2024'),
(3, 45, 4, '700.00', 'Juin', '2025-06-22', NULL, 'scolarité', 'espèces', 'payé', 'Paiement automatique - Juin 2024'),
(4, 38, 4, '800.00', 'Juin', '2025-06-22', NULL, 'scolarité', 'espèces', 'retard', 'Paiement automatique - Juin 2024'),
(5, 39, 4, '1200.00', 'Juin', '2025-06-22', NULL, 'scolarité', 'espèces', 'retard', 'Paiement automatique - Juin 2024'),
(6, 40, 4, '1000.00', 'Juin', '2025-06-22', NULL, 'scolarité', 'espèces', 'annule', 'Paiement automatique - Juin 2024'),
(7, 41, 4, '12.00', 'Juin', '2025-06-22', NULL, 'scolarité', 'espèces', 'retard', 'Paiement automatique - Juin 2024'),
(8, 42, 4, '12.00', 'Juin', '2025-06-22', NULL, 'scolarité', 'espèces', 'retard', 'Paiement automatique - Juin 2024'),
(9, 43, 4, '12.00', 'Juin', '2025-06-22', NULL, 'scolarité', 'espèces', 'retard', 'Paiement automatique - Juin 2024');

-- --------------------------------------------------------

--
-- Structure de la table `parent`
--

CREATE TABLE `parent` (
  `id_parent` int(11) NOT NULL,
  `id_utilisateur` int(11) NOT NULL,
  `nom_ar` varchar(50) NOT NULL,
  `prenom_ar` varchar(50) NOT NULL,
  `num_CIN` char(10) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Déchargement des données de la table `parent`
--

INSERT INTO `parent` (`id_parent`, `id_utilisateur`, `nom_ar`, `prenom_ar`, `num_CIN`) VALUES
(5, 41, 'كريم', 'بن سعيد', 'M12345'),
(6, 42, 'خديجة', 'الواليدي', 'CIN002'),
(7, 43, 'أحمد', 'المطهري', 'C12345'),
(8, 44, 'سوفيا', 'ظاهر', 'CIN004'),
(10, 46, 'مريم', 'بن جلون', 'CIN006'),
(11, 47, 'يوسف', 'المنصوري', 'CIN007'),
(12, 48, 'أسماء', 'سعيد', 'CIN008'),
(13, 49, 'عبدالقادر', 'أمارة', 'CIN009'),
(14, 54, 'test', 'test', 'H12348');

-- --------------------------------------------------------

--
-- Structure de la table `participation_activite`
--

CREATE TABLE `participation_activite` (
  `id_eleve` int(11) NOT NULL,
  `id_activite` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `relation_parent_eleve`
--

CREATE TABLE `relation_parent_eleve` (
  `id_parent` int(11) NOT NULL,
  `id_eleve` int(11) NOT NULL,
  `type_relation` enum('père','mère','tuteur') NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Déchargement des données de la table `relation_parent_eleve`
--

INSERT INTO `relation_parent_eleve` (`id_parent`, `id_eleve`, `type_relation`) VALUES
(5, 42, 'père'),
(7, 46, 'père'),
(10, 46, 'mère'),
(14, 46, 'mère');

-- --------------------------------------------------------

--
-- Structure de la table `salle`
--

CREATE TABLE `salle` (
  `id_salle` int(11) NOT NULL,
  `nom_salle` varchar(50) NOT NULL,
  `capacite` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Déchargement des données de la table `salle`
--

INSERT INTO `salle` (`id_salle`, `nom_salle`, `capacite`) VALUES
(1, 'Salle A01', 25),
(2, 'Salle A02', 10),
(3, 'Salle A03', 28);

-- --------------------------------------------------------

--
-- Structure de la table `transport`
--

CREATE TABLE `transport` (
  `id_transport` int(11) NOT NULL,
  `prix` decimal(6,2) NOT NULL,
  `trajet` varchar(100) NOT NULL,
  `matricule` varchar(50) NOT NULL,
  `capacite` int(11) DEFAULT '25'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `unite`
--

CREATE TABLE `unite` (
  `id_unite` int(11) NOT NULL,
  `id_matiere` int(11) NOT NULL,
  `nom_unite` varchar(50) NOT NULL,
  `description` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Déchargement des données de la table `unite`
--

INSERT INTO `unite` (`id_unite`, `id_matiere`, `nom_unite`, `description`) VALUES
(11, 13, 'Unité 1 - Mathématiques', 'Introduction aux mathématiques de base, comprenant les opérations et les fonctions simples.'),
(12, 14, 'Unité 2 - Physique', 'Étude des principes fondamentaux de la physique, notamment la mécanique et la thermodynamique.'),
(13, 15, 'Unité 3 - Chimie', 'Exploration des réactions chimiques de base, des éléments chimiques et de la structure atomique.'),
(14, 16, 'Unité 4 - Biologie', 'Étude de la cellule, des organismes vivants et des principes de la génétique.'),
(15, 17, 'Unité 5 - Informatique', 'Introduction à la programmation, aux structures de données et à l’algorithmique.'),
(16, 18, 'Unité 6 - Littérature', 'Analyse des grandes œuvres littéraires, avec un focus sur la poésie et le roman classique.'),
(17, 19, 'Unité 7 - Histoire', 'Introduction à l’histoire des grandes civilisations et des événements marquants de l’Antiquité à l’époque moderne.'),
(18, 20, 'Unité 8 - Géographie', 'Étude des phénomènes géographiques naturels et humains, ainsi que des enjeux environnementaux mondiaux.'),
(19, 22, 'Unité 10 - Économie', 'Bases de l’économie, y compris la microéconomie, la macroéconomie et les concepts clés du marché.');

-- --------------------------------------------------------

--
-- Structure de la table `utilisateur`
--

CREATE TABLE `utilisateur` (
  `id_utilisateur` int(11) NOT NULL,
  `nom` varchar(50) NOT NULL,
  `prenom` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `mot_de_passe` varchar(255) NOT NULL,
  `role` enum('admin','enseignant','eleve','parent','personnel') NOT NULL,
  `sexe` enum('garçon','fille','homme','femme') NOT NULL,
  `date_naissance` date DEFAULT NULL,
  `lieu_naissance` varchar(100) DEFAULT NULL,
  `nationalite` varchar(50) DEFAULT 'marocain',
  `telephone` varchar(15) DEFAULT NULL,
  `adresse` text,
  `photo` varchar(255) DEFAULT NULL,
  `est_valide` tinyint(1) DEFAULT '0',
  `est_actif` tinyint(1) DEFAULT '1',
  `date_creation` datetime DEFAULT CURRENT_TIMESTAMP,
  `date_maj` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Déchargement des données de la table `utilisateur`
--

INSERT INTO `utilisateur` (`id_utilisateur`, `nom`, `prenom`, `email`, `mot_de_passe`, `role`, `sexe`, `date_naissance`, `lieu_naissance`, `nationalite`, `telephone`, `adresse`, `photo`, `est_valide`, `est_actif`, `date_creation`, `date_maj`) VALUES
(1, 'jaber', 'fatima zohra', '<EMAIL>', '$2y$10$pW34E/xatzQ593AtMHlKIejVFqCKNRKymYcw0/jKZAgNsu.ZqjSj2', 'admin', 'femme', NULL, NULL, NULL, '', NULL, NULL, 1, 1, '2025-06-04 12:01:47', '2025-06-21 20:06:28'),
(23, 'Elhadi', 'Rachid', '<EMAIL>', '$2y$10$CwT7Bbyu3j5lWA9hH6Q5.eFw2of8HcVJErFZ.vGyOa2.Jjqz9/j8m', 'eleve', 'garçon', '2010-06-15', 'Casablanca', 'marocain', '0612345678', 'Casablanca, Maroc', NULL, 1, 1, '2025-06-21 20:10:23', NULL),
(24, 'Amina', 'Noura', '<EMAIL>', '$2y$10$6jfgDkjgFu5Lls1pqZhB8ETsGrA8xx6qPgl3bK3kwM6X6mgn2RPKP', 'eleve', 'fille', '2011-04-21', 'Rabat', 'marocain', '0623456789', 'Rabat, Maroc', NULL, 1, 1, '2025-06-21 20:10:23', NULL),
(25, 'Yassir', 'Mohamed', '<EMAIL>', '$2y$10$EKVtUgIpbVt6WxXXfF2Qm.D13v7FJ1WvTSnN67emc.wLt25RieW7m', 'eleve', 'garçon', '2010-03-01', 'Fes', 'marocain', '0634567890', 'Fes, Maroc', NULL, 1, 1, '2025-06-21 20:10:23', NULL),
(26, 'Laila', 'Ahmed', '<EMAIL>', '$2y$10$3XIHVJkBkvOghs7P4V4ztkDbX3txgFqH7vGd1l.xcOClAOb2iBOre', 'eleve', 'fille', '2011-07-30', 'Marrakech', 'marocain', '0645678901', 'Marrakech, Maroc', NULL, 1, 1, '2025-06-21 20:10:23', NULL),
(27, 'Omar', 'Salim', '<EMAIL>', '$2y$10$GmnRswzVuUqD6h2zjm0yYuNYVNh8PUNHH3cHpGR/2C7gDZZ.xSowu', 'eleve', 'garçon', '2010-10-10', 'Agadir', 'marocain', '0656789012', 'Agadir, Maroc', NULL, 1, 1, '2025-06-21 20:10:23', NULL),
(28, 'Imane', 'Lahbabi', '<EMAIL>', '$2y$10$gGzXmbOYd7bhn9uG9n60o4Ae3Bh3zD9rboRP1Coc9noQCrw2xXgza', 'eleve', 'fille', '2011-01-18', 'Tangier', 'marocain', '0667890123', 'Tangier, Maroc', NULL, 1, 1, '2025-06-21 20:10:23', NULL),
(29, 'Mounir', 'Tazi', '<EMAIL>', '$2y$10$GbzJHlNhbi82bbJmfkQSfG6d4lzPpCQqAa4pRH9qDVEpWg2T2fw1W', 'eleve', 'garçon', '2010-11-25', 'Meknes', 'marocain', '0678901234', 'Meknes, Maroc', NULL, 1, 1, '2025-06-21 20:10:23', NULL),
(30, 'Sanae', 'Mahfoud', '<EMAIL>', '$2y$10$sKXFXz6LzO8Pksd0y1vScMOZGjf9Rax0k7.DM0ubzO9OeMF4ABgDi', 'eleve', 'fille', '2010-12-05', 'Tétouan', 'marocain', '0689012345', 'Tétouan, Maroc', NULL, 1, 1, '2025-06-21 20:10:23', NULL),
(31, 'Anas', 'Bouhaddou', '<EMAIL>', '$2y$10$EMbBhrvCOUCOwmi7UeHwi0T9A28Us7MB0i6jeZGOMmnNGgSKjyh6G', 'eleve', 'garçon', '2011-08-13', 'Marrakech', 'marocain', '0690123456', 'Marrakech, Maroc', NULL, 1, 1, '2025-06-21 20:10:23', NULL),
(32, 'Meryem', 'Laaziz', '<EMAIL>', '$2y$10$PwYtgd6.kZgPbFHLPhiJ3Ul8IcDbjDZ5hHM0wZVPOaXhDQZCqTckC', 'eleve', 'fille', '2011-02-27', 'Rabat', 'marocain', '0612345678', 'Rabat, Maroc', NULL, 1, 1, '2025-06-21 20:10:23', NULL),
(33, 'Hamza', 'Rachid', '<EMAIL>', '$2y$10$HjREz.n0KbeWdrwDzxxATfXg8JmLPyUEdszSmMBZScSuHmV4ZG7wm', 'eleve', 'garçon', '2010-09-02', 'Casablanca', 'marocain', '0623456789', 'Casablanca, Maroc', NULL, 1, 1, '2025-06-21 20:10:23', NULL),
(34, 'Asma', 'Zeroual', '<EMAIL>', '$2y$10$uH3Kh.XERhr3R63ZJ0uMNzP5Admd1m1IaQUndUvkNwSYj9R.NKYqa', 'eleve', 'fille', '2010-11-18', 'Salé', 'marocain', '0634567890', 'Salé, Maroc', NULL, 1, 1, '2025-06-21 20:10:23', NULL),
(35, 'Mohammed', 'Ben Ali', '<EMAIL>', '$2y$10$U8nL5R5v.tL7ZbP2HfBLV.fqHZjFV1sO8FSnI7kVrxj.v0LkBqVnS', 'enseignant', 'homme', '1980-01-15', 'Casablanca', 'Marocain', '0612345678', '12 Rue de l\'École, Casablanca', 'mohammed_ben_ali.jpg', 1, 1, '2025-06-21 20:21:56', NULL),
(36, 'Fatima', 'Zahra', '<EMAIL>', '$2y$10$JbL7TksMyi8eFzF5Ik5wXuCkTmHxa5lGG4tiDq1X75B/.f5QKQomC', 'enseignant', 'femme', '1985-02-25', 'Rabat', 'Marocain', '0623456789', '45 Rue des Roses, Rabat', 'fatima_zahra.jpg', 1, 1, '2025-06-21 20:21:56', NULL),
(37, 'Ahmed', 'El Mansouri', '<EMAIL>', '$2y$10$CfpLTkfgXnU8c8v2jmiqIYOw6gn0Xy2vcYo6rbxfx57g2sy6FwdQG', 'enseignant', 'homme', '1990-03-10', 'Marrakech', 'Marocain', '0634567890', '78 Rue de Marrakech, Marrakech', 'ahmed_mansouri.jpg', 1, 1, '2025-06-21 20:21:56', NULL),
(38, 'Nadia', 'Benkirane', '<EMAIL>', '$2y$10$JXPR1c1cyIHl3Aq7aaZG3jlM49qvS5Nmj2wdf5G1zOe0p4M1PMl1G', 'enseignant', 'femme', '1992-07-30', 'Fès', 'Marocain', '0645678901', '22 Avenue Hassan II, Fès', 'nadia_benkirane.jpg', 1, 1, '2025-06-21 20:21:56', NULL),
(39, 'Yassine', 'Rabat', '<EMAIL>', '$2y$10$RcsuPj6n5focqxHTzLTaD2zjLDOIT0niKhOZoGmnG3tHrHYX7XHGy', 'enseignant', 'homme', '1988-05-12', 'Tangier', 'Marocain', '0656789012', '90 Rue de Tanger, Tanger', 'yassine_rabat.jpg', 1, 1, '2025-06-21 20:21:56', NULL),
(40, 'Salma', 'Chakir', '<EMAIL>', '$2y$10$Rhts9OZ1jXZvnInKNh8vJxfhKTk99DN7YXyfgGbPnycWgdrwZnNzm', 'enseignant', 'femme', '1995-11-03', 'Agadir', 'Marocain', '0667890123', '101 Rue des Palmiers, Agadir', 'salma_chakir.jpg', 1, 1, '2025-06-21 20:21:56', NULL),
(41, 'Karim', 'Bensaid', '<EMAIL>', '$2y$10$Y0XTZDzjTI2sDjkXfKJjHye2uwLKpkrPYOYQuVpa5QYgOIsjjVzM', 'parent', 'homme', '1975-12-30', 'Casablanca', 'Marocain', '0612345678', '123 Rue du Soleil, Casablanca', 'karim_bensaid.jpg', 1, 1, '2025-06-21 20:28:47', NULL),
(42, 'Khadija', 'El Oualidi', '<EMAIL>', '$2y$10$mlLQoPpzNRx5aFD.5xtHKtqGG7mlBdFMjtV0WRHk5UldHwZ6hNN.0', 'parent', 'femme', '1980-06-14', 'Rabat', 'Marocain', '0623456789', '45 Rue des Lilas, Rabat', 'khadija_oualidi.jpg', 1, 1, '2025-06-21 20:28:47', NULL),
(43, 'Ahmed', 'El Moutahari', '<EMAIL>', '$2y$10$Nl3T8k5AlmkU6teNGWbxGdfEKiqU7rPaHviOd9J1Wyep.IpLRzZ7W', 'parent', 'homme', '1983-09-21', 'Marrakech', 'Marocain', '0634567890', '78 Avenue Mohammed VI, Marrakech', 'ahmed_moutahari.jpg', 1, 1, '2025-06-21 20:28:47', NULL),
(44, 'Sofia', 'Zahir', '<EMAIL>', '$2y$10$vv9rGpnS27jrPSPtBOdL/OoFxxy9ZKXlgjlOwUmJZ.kXe5fF6sHk.', 'parent', 'femme', '1985-11-05', 'Fès', 'Marocain', '0645678901', '22 Rue Hassan II, Fès', 'sofia_zahir.jpg', 1, 1, '2025-06-21 20:28:47', NULL),
(46, 'Meriem', 'Benjelloun', '<EMAIL>', '$2y$10$0krWzQBxGQb8Hb1xU8sOGpPVD8Q0EK5zj8M2w1yEdaxptVnqCAyyK', 'parent', 'femme', '1990-04-08', 'Tangier', 'Marocain', '0667890123', '89 Rue de Tanger, Tanger', 'meriem_benjelloun.jpg', 1, 1, '2025-06-21 20:28:47', NULL),
(47, 'Youssef', 'Mansouri', '<EMAIL>', '$2y$10$7wOY4Jt8hWqR3O1QzxaU3A0bsH5K6J9wXar77A01oBy6mJ7vnUo62', 'parent', 'homme', '1976-01-22', 'Casablanca', 'Marocain', '0678901234', '33 Rue de l\'Océan, Casablanca', 'youssef_mansouri.jpg', 1, 1, '2025-06-21 20:28:47', NULL),
(48, 'Asmae', 'Said', '<EMAIL>', '$2y$10$HmyqSKBFL0GbA4EpxzFIZgP7bQ0Tx7V8n6ETz.s9C9BoeuZkOqAGG', 'parent', 'femme', '1988-10-30', 'Marrakech', 'Marocain', '0689012345', '120 Rue des Fleurs, Marrakech', 'asmae_said.jpg', 1, 1, '2025-06-21 20:28:47', NULL),
(49, 'Abdelkader', 'Amara', '<EMAIL>', '$2y$10$9h7rf6QJ4sewMoxbe5kF1yKbf0xiXwlFscMwzSH.xKh5hAKIVFZ6m', 'parent', 'homme', '1982-05-13', 'Rabat', 'Marocain', '0690123456', '56 Rue de Rabat, Rabat', 'abdelkader_amara.jpg', 1, 1, '2025-06-21 20:28:47', NULL),
(50, 'Karim', 'Bensaid', '<EMAIL>', '$2y$10$qGwiG1LxSxiO5pR2lXSy0.1752seBAu9coUBlPMbcoT5liK2nKE.S', 'eleve', 'garçon', '2025-06-21', 'Casablanca', 'marocain', '0612345678', '123 Rue du Soleil, Casablanca', NULL, 1, 1, '2025-06-21 21:59:47', '2025-06-21 22:05:29'),
(53, 'a', 'a', '<EMAIL>', '$2y$10$bJEN/EwIG38jLeEJvRYq8eONJAYK4ZnJtPYLJXT3Ngw4d93OJMrcm', 'enseignant', 'homme', '2025-05-28', 'Casablanca', 'espagnol', '0612345678', '123 Rue du Soleil, Casablanca', NULL, 0, 1, '2025-06-21 22:52:48', NULL),
(54, 'test', 'Bensaid', '<EMAIL>', '$2y$10$4/naK8JFSlH8PePX4C23B.qvNPHVTLb4Wd2M1Ul.QS1CzsCuXGv1G', 'parent', 'femme', '2025-05-28', 'Casablanca', 'espagnol', '0612345678', '123 Rue du Soleil, Casablanca', NULL, 1, 1, '2025-06-22 01:36:00', '2025-06-22 01:37:25');

-- --------------------------------------------------------

--
-- Doublure de structure pour la vue `vue_eleves_details`
-- (Voir ci-dessous la vue réelle)
--
CREATE TABLE `vue_eleves_details` (
`id_eleve` int(11)
,`id_utilisateur` int(11)
,`code_massar` varchar(10)
,`code_gresa` char(10)
,`nom_ar` varchar(50)
,`prenom_ar` varchar(50)
,`lieu_naissance_ar` varchar(100)
,`nom` varchar(50)
,`prenom` varchar(50)
,`email` varchar(100)
,`telephone` varchar(15)
,`date_naissance` date
,`lieu_naissance` varchar(100)
,`sexe` enum('garçon','fille','homme','femme')
,`nationalite` varchar(50)
,`adresse` text
,`id_classe` int(11)
,`nom_classe` varchar(20)
,`id_niveau` int(11)
,`nom_niveau` varchar(50)
,`nom_ecole` varchar(100)
,`pere_nom` varchar(50)
,`pere_prenom` varchar(50)
,`pere_telephone` varchar(15)
,`mere_nom` varchar(50)
,`mere_prenom` varchar(50)
,`mere_telephone` varchar(15)
);

-- --------------------------------------------------------

--
-- Structure de la vue `vue_eleves_details` exportée comme une table
--
DROP TABLE IF EXISTS `vue_eleves_details`;
CREATE TABLE`vue_eleves_details`(
    `id_eleve` int(11) NOT NULL DEFAULT '0',
    `id_utilisateur` int(11) NOT NULL,
    `code_massar` varchar(10) COLLATE utf8_general_ci DEFAULT NULL,
    `code_gresa` char(10) COLLATE utf8_general_ci DEFAULT NULL,
    `nom_ar` varchar(50) COLLATE utf8_general_ci NOT NULL,
    `prenom_ar` varchar(50) COLLATE utf8_general_ci NOT NULL,
    `lieu_naissance_ar` varchar(100) COLLATE utf8_general_ci NOT NULL,
    `nom` varchar(50) COLLATE utf8_general_ci NOT NULL,
    `prenom` varchar(50) COLLATE utf8_general_ci NOT NULL,
    `email` varchar(100) COLLATE utf8_general_ci NOT NULL,
    `telephone` varchar(15) COLLATE utf8_general_ci DEFAULT NULL,
    `date_naissance` date DEFAULT NULL,
    `lieu_naissance` varchar(100) COLLATE utf8_general_ci DEFAULT NULL,
    `sexe` enum('garçon','fille','homme','femme') COLLATE utf8_general_ci NOT NULL,
    `nationalite` varchar(50) COLLATE utf8_general_ci DEFAULT 'marocain',
    `adresse` text COLLATE utf8_general_ci DEFAULT NULL,
    `id_classe` int(11) DEFAULT NULL,
    `nom_classe` varchar(20) COLLATE utf8_general_ci DEFAULT NULL,
    `id_niveau` int(11) DEFAULT '0',
    `nom_niveau` varchar(50) COLLATE utf8_general_ci DEFAULT NULL,
    `nom_ecole` varchar(100) COLLATE utf8_general_ci DEFAULT NULL,
    `pere_nom` varchar(50) COLLATE utf8_general_ci DEFAULT NULL,
    `pere_prenom` varchar(50) COLLATE utf8_general_ci DEFAULT NULL,
    `pere_telephone` varchar(15) COLLATE utf8_general_ci DEFAULT NULL,
    `mere_nom` varchar(50) COLLATE utf8_general_ci DEFAULT NULL,
    `mere_prenom` varchar(50) COLLATE utf8_general_ci DEFAULT NULL,
    `mere_telephone` varchar(15) COLLATE utf8_general_ci DEFAULT NULL
);

--
-- Index pour les tables déchargées
--

--
-- Index pour la table `absence_eleve`
--
ALTER TABLE `absence_eleve`
  ADD PRIMARY KEY (`id_absence_eleve`),
  ADD UNIQUE KEY `id_eleve` (`id_eleve`,`date_debut`),
  ADD KEY `id_classe` (`id_classe`),
  ADD KEY `idx_date_debut` (`date_debut`);

--
-- Index pour la table `absence_enseignant`
--
ALTER TABLE `absence_enseignant`
  ADD PRIMARY KEY (`id_absence_enseignant`),
  ADD UNIQUE KEY `id_enseignant` (`id_enseignant`,`date_debut`),
  ADD KEY `id_remplacant` (`id_remplacant`);

--
-- Index pour la table `activite`
--
ALTER TABLE `activite`
  ADD PRIMARY KEY (`id_activite`),
  ADD KEY `id_annee_scolaire` (`id_annee_scolaire`);

--
-- Index pour la table `ancienne_ecole`
--
ALTER TABLE `ancienne_ecole`
  ADD PRIMARY KEY (`code_gresa`);

--
-- Index pour la table `annee_scolaire`
--
ALTER TABLE `annee_scolaire`
  ADD PRIMARY KEY (`id_annee_scolaire`),
  ADD UNIQUE KEY `libelle` (`libelle`),
  ADD KEY `idx_libelle` (`libelle`);

--
-- Index pour la table `beneficier_transport`
--
ALTER TABLE `beneficier_transport`
  ADD PRIMARY KEY (`id_eleve`,`id_transport`,`id_annee_scolaire`),
  ADD KEY `id_transport` (`id_transport`),
  ADD KEY `id_annee_scolaire` (`id_annee_scolaire`);

--
-- Index pour la table `bulletin`
--
ALTER TABLE `bulletin`
  ADD PRIMARY KEY (`id_bulletin`),
  ADD UNIQUE KEY `id_eleve` (`id_eleve`,`id_annee_scolaire`,`semestre`),
  ADD KEY `id_annee_scolaire` (`id_annee_scolaire`);

--
-- Index pour la table `bulletin_matiere`
--
ALTER TABLE `bulletin_matiere`
  ADD PRIMARY KEY (`id_bulletin`,`id_matiere`),
  ADD KEY `id_matiere` (`id_matiere`);

--
-- Index pour la table `classe`
--
ALTER TABLE `classe`
  ADD PRIMARY KEY (`id_classe`),
  ADD UNIQUE KEY `id_niveau` (`id_niveau`,`nom_classe`);

--
-- Index pour la table `classe_activite`
--
ALTER TABLE `classe_activite`
  ADD PRIMARY KEY (`id_classe`,`id_activite`),
  ADD KEY `id_activite` (`id_activite`);

--
-- Index pour la table `contrat`
--
ALTER TABLE `contrat`
  ADD PRIMARY KEY (`id_contrat`),
  ADD UNIQUE KEY `id_enseignant` (`id_enseignant`,`date_debut`,`type_contrat`);

--
-- Index pour la table `cours`
--
ALTER TABLE `cours`
  ADD PRIMARY KEY (`id_cours`),
  ADD UNIQUE KEY `id_enseignant` (`id_enseignant`,`id_classe`,`jour_semaine`,`heure_debut`,`heure_fin`),
  ADD KEY `id_unite` (`id_unite`),
  ADD KEY `id_classe` (`id_classe`),
  ADD KEY `id_salle` (`id_salle`);

--
-- Index pour la table `diplome`
--
ALTER TABLE `diplome`
  ADD PRIMARY KEY (`id_diplome`),
  ADD UNIQUE KEY `id_enseignant` (`id_enseignant`,`intitule`,`institut`);

--
-- Index pour la table `ecole`
--
ALTER TABLE `ecole`
  ADD PRIMARY KEY (`id_ecole`);

--
-- Index pour la table `eleve`
--
ALTER TABLE `eleve`
  ADD PRIMARY KEY (`id_eleve`),
  ADD UNIQUE KEY `id_utilisateur` (`id_utilisateur`),
  ADD UNIQUE KEY `code_massar` (`code_massar`),
  ADD UNIQUE KEY `code_gresa` (`code_gresa`);

--
-- Index pour la table `enseignant`
--
ALTER TABLE `enseignant`
  ADD PRIMARY KEY (`id_enseignant`),
  ADD UNIQUE KEY `id_utilisateur` (`id_utilisateur`),
  ADD UNIQUE KEY `num_CIN` (`num_CIN`),
  ADD UNIQUE KEY `num_CNSS` (`num_CNSS`);

--
-- Index pour la table `enseignant_matiere`
--
ALTER TABLE `enseignant_matiere`
  ADD PRIMARY KEY (`id_enseignant`,`id_matiere`),
  ADD KEY `id_matiere` (`id_matiere`);

--
-- Index pour la table `examen`
--
ALTER TABLE `examen`
  ADD PRIMARY KEY (`id_examen`),
  ADD KEY `id_matiere` (`id_matiere`),
  ADD KEY `id_classe` (`id_classe`),
  ADD KEY `id_enseignant` (`id_enseignant`),
  ADD KEY `idx_date_examen` (`date_examen`);

--
-- Index pour la table `inscription`
--
ALTER TABLE `inscription`
  ADD PRIMARY KEY (`id_eleve`,`id_annee_scolaire`,`id_classe`),
  ADD KEY `id_classe` (`id_classe`),
  ADD KEY `id_annee_scolaire` (`id_annee_scolaire`);

--
-- Index pour la table `matiere`
--
ALTER TABLE `matiere`
  ADD PRIMARY KEY (`id_matiere`),
  ADD UNIQUE KEY `nom_matiere_fr` (`nom_matiere_fr`),
  ADD UNIQUE KEY `nom_matiere_ar` (`nom_matiere_ar`);

--
-- Index pour la table `message`
--
ALTER TABLE `message`
  ADD PRIMARY KEY (`id_message`),
  ADD KEY `id_emetteur` (`id_emetteur`),
  ADD KEY `id_recepteur` (`id_recepteur`);

--
-- Index pour la table `niveau`
--
ALTER TABLE `niveau`
  ADD PRIMARY KEY (`id_niveau`),
  ADD UNIQUE KEY `cycle` (`cycle`,`libelle`);

--
-- Index pour la table `niveau_matiere`
--
ALTER TABLE `niveau_matiere`
  ADD PRIMARY KEY (`id_niveau`,`id_matiere`),
  ADD KEY `id_matiere` (`id_matiere`);

--
-- Index pour la table `note`
--
ALTER TABLE `note`
  ADD PRIMARY KEY (`id_eleve`,`id_examen`),
  ADD KEY `id_examen` (`id_examen`);

--
-- Index pour la table `paiement`
--
ALTER TABLE `paiement`
  ADD PRIMARY KEY (`id_paiement`),
  ADD UNIQUE KEY `id_eleve` (`id_eleve`,`id_annee_scolaire`,`type_paiement`,`mois`),
  ADD KEY `id_annee_scolaire` (`id_annee_scolaire`),
  ADD KEY `idx_date_paiement` (`date_paiement`);

--
-- Index pour la table `parent`
--
ALTER TABLE `parent`
  ADD PRIMARY KEY (`id_parent`),
  ADD UNIQUE KEY `id_utilisateur` (`id_utilisateur`),
  ADD UNIQUE KEY `num_CIN` (`num_CIN`);

--
-- Index pour la table `participation_activite`
--
ALTER TABLE `participation_activite`
  ADD PRIMARY KEY (`id_eleve`,`id_activite`),
  ADD KEY `id_activite` (`id_activite`);

--
-- Index pour la table `relation_parent_eleve`
--
ALTER TABLE `relation_parent_eleve`
  ADD PRIMARY KEY (`id_parent`,`id_eleve`),
  ADD KEY `id_eleve` (`id_eleve`);

--
-- Index pour la table `salle`
--
ALTER TABLE `salle`
  ADD PRIMARY KEY (`id_salle`),
  ADD UNIQUE KEY `nom_salle` (`nom_salle`);

--
-- Index pour la table `transport`
--
ALTER TABLE `transport`
  ADD PRIMARY KEY (`id_transport`),
  ADD UNIQUE KEY `matricule` (`matricule`);

--
-- Index pour la table `unite`
--
ALTER TABLE `unite`
  ADD PRIMARY KEY (`id_unite`),
  ADD UNIQUE KEY `nom_unite` (`nom_unite`),
  ADD KEY `id_matiere` (`id_matiere`);

--
-- Index pour la table `utilisateur`
--
ALTER TABLE `utilisateur`
  ADD PRIMARY KEY (`id_utilisateur`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `idx_nom_prenom` (`nom`,`prenom`);

--
-- AUTO_INCREMENT pour les tables déchargées
--

--
-- AUTO_INCREMENT pour la table `absence_eleve`
--
ALTER TABLE `absence_eleve`
  MODIFY `id_absence_eleve` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `absence_enseignant`
--
ALTER TABLE `absence_enseignant`
  MODIFY `id_absence_enseignant` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `activite`
--
ALTER TABLE `activite`
  MODIFY `id_activite` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `annee_scolaire`
--
ALTER TABLE `annee_scolaire`
  MODIFY `id_annee_scolaire` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT pour la table `bulletin`
--
ALTER TABLE `bulletin`
  MODIFY `id_bulletin` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `classe`
--
ALTER TABLE `classe`
  MODIFY `id_classe` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT pour la table `contrat`
--
ALTER TABLE `contrat`
  MODIFY `id_contrat` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT pour la table `cours`
--
ALTER TABLE `cours`
  MODIFY `id_cours` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT pour la table `diplome`
--
ALTER TABLE `diplome`
  MODIFY `id_diplome` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=18;

--
-- AUTO_INCREMENT pour la table `ecole`
--
ALTER TABLE `ecole`
  MODIFY `id_ecole` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT pour la table `eleve`
--
ALTER TABLE `eleve`
  MODIFY `id_eleve` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=47;

--
-- AUTO_INCREMENT pour la table `enseignant`
--
ALTER TABLE `enseignant`
  MODIFY `id_enseignant` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT pour la table `examen`
--
ALTER TABLE `examen`
  MODIFY `id_examen` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `matiere`
--
ALTER TABLE `matiere`
  MODIFY `id_matiere` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=24;

--
-- AUTO_INCREMENT pour la table `message`
--
ALTER TABLE `message`
  MODIFY `id_message` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `niveau`
--
ALTER TABLE `niveau`
  MODIFY `id_niveau` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT pour la table `paiement`
--
ALTER TABLE `paiement`
  MODIFY `id_paiement` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT pour la table `parent`
--
ALTER TABLE `parent`
  MODIFY `id_parent` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT pour la table `salle`
--
ALTER TABLE `salle`
  MODIFY `id_salle` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT pour la table `transport`
--
ALTER TABLE `transport`
  MODIFY `id_transport` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `unite`
--
ALTER TABLE `unite`
  MODIFY `id_unite` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- AUTO_INCREMENT pour la table `utilisateur`
--
ALTER TABLE `utilisateur`
  MODIFY `id_utilisateur` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=55;

--
-- Contraintes pour les tables déchargées
--

--
-- Contraintes pour la table `absence_eleve`
--
ALTER TABLE `absence_eleve`
  ADD CONSTRAINT `absence_eleve_ibfk_1` FOREIGN KEY (`id_eleve`) REFERENCES `eleve` (`id_eleve`) ON DELETE CASCADE,
  ADD CONSTRAINT `absence_eleve_ibfk_2` FOREIGN KEY (`id_classe`) REFERENCES `classe` (`id_classe`) ON DELETE CASCADE;

--
-- Contraintes pour la table `absence_enseignant`
--
ALTER TABLE `absence_enseignant`
  ADD CONSTRAINT `absence_enseignant_ibfk_1` FOREIGN KEY (`id_enseignant`) REFERENCES `enseignant` (`id_enseignant`) ON DELETE CASCADE,
  ADD CONSTRAINT `absence_enseignant_ibfk_2` FOREIGN KEY (`id_remplacant`) REFERENCES `enseignant` (`id_enseignant`) ON DELETE SET NULL;

--
-- Contraintes pour la table `activite`
--
ALTER TABLE `activite`
  ADD CONSTRAINT `activite_ibfk_1` FOREIGN KEY (`id_annee_scolaire`) REFERENCES `annee_scolaire` (`id_annee_scolaire`) ON DELETE CASCADE;

--
-- Contraintes pour la table `beneficier_transport`
--
ALTER TABLE `beneficier_transport`
  ADD CONSTRAINT `beneficier_transport_ibfk_1` FOREIGN KEY (`id_eleve`) REFERENCES `eleve` (`id_eleve`) ON DELETE CASCADE,
  ADD CONSTRAINT `beneficier_transport_ibfk_2` FOREIGN KEY (`id_transport`) REFERENCES `transport` (`id_transport`) ON DELETE CASCADE,
  ADD CONSTRAINT `beneficier_transport_ibfk_3` FOREIGN KEY (`id_annee_scolaire`) REFERENCES `annee_scolaire` (`id_annee_scolaire`) ON DELETE CASCADE;

--
-- Contraintes pour la table `bulletin`
--
ALTER TABLE `bulletin`
  ADD CONSTRAINT `bulletin_ibfk_1` FOREIGN KEY (`id_eleve`) REFERENCES `eleve` (`id_eleve`) ON DELETE CASCADE,
  ADD CONSTRAINT `bulletin_ibfk_2` FOREIGN KEY (`id_annee_scolaire`) REFERENCES `annee_scolaire` (`id_annee_scolaire`) ON DELETE CASCADE;

--
-- Contraintes pour la table `bulletin_matiere`
--
ALTER TABLE `bulletin_matiere`
  ADD CONSTRAINT `bulletin_matiere_ibfk_1` FOREIGN KEY (`id_bulletin`) REFERENCES `bulletin` (`id_bulletin`) ON DELETE CASCADE,
  ADD CONSTRAINT `bulletin_matiere_ibfk_2` FOREIGN KEY (`id_matiere`) REFERENCES `matiere` (`id_matiere`);

--
-- Contraintes pour la table `classe`
--
ALTER TABLE `classe`
  ADD CONSTRAINT `classe_ibfk_1` FOREIGN KEY (`id_niveau`) REFERENCES `niveau` (`id_niveau`);

--
-- Contraintes pour la table `classe_activite`
--
ALTER TABLE `classe_activite`
  ADD CONSTRAINT `classe_activite_ibfk_1` FOREIGN KEY (`id_classe`) REFERENCES `classe` (`id_classe`) ON DELETE CASCADE,
  ADD CONSTRAINT `classe_activite_ibfk_2` FOREIGN KEY (`id_activite`) REFERENCES `activite` (`id_activite`) ON DELETE CASCADE;

--
-- Contraintes pour la table `contrat`
--
ALTER TABLE `contrat`
  ADD CONSTRAINT `contrat_ibfk_1` FOREIGN KEY (`id_enseignant`) REFERENCES `enseignant` (`id_enseignant`) ON DELETE CASCADE;

--
-- Contraintes pour la table `cours`
--
ALTER TABLE `cours`
  ADD CONSTRAINT `cours_ibfk_1` FOREIGN KEY (`id_unite`) REFERENCES `unite` (`id_unite`),
  ADD CONSTRAINT `cours_ibfk_2` FOREIGN KEY (`id_enseignant`) REFERENCES `enseignant` (`id_enseignant`),
  ADD CONSTRAINT `cours_ibfk_3` FOREIGN KEY (`id_classe`) REFERENCES `classe` (`id_classe`),
  ADD CONSTRAINT `cours_ibfk_4` FOREIGN KEY (`id_salle`) REFERENCES `salle` (`id_salle`);

--
-- Contraintes pour la table `diplome`
--
ALTER TABLE `diplome`
  ADD CONSTRAINT `diplome_ibfk_1` FOREIGN KEY (`id_enseignant`) REFERENCES `enseignant` (`id_enseignant`) ON DELETE CASCADE;

--
-- Contraintes pour la table `eleve`
--
ALTER TABLE `eleve`
  ADD CONSTRAINT `eleve_ibfk_1` FOREIGN KEY (`id_utilisateur`) REFERENCES `utilisateur` (`id_utilisateur`) ON DELETE CASCADE,
  ADD CONSTRAINT `eleve_ibfk_2` FOREIGN KEY (`code_gresa`) REFERENCES `ancienne_ecole` (`code_gresa`) ON DELETE SET NULL;

--
-- Contraintes pour la table `enseignant`
--
ALTER TABLE `enseignant`
  ADD CONSTRAINT `enseignant_ibfk_1` FOREIGN KEY (`id_utilisateur`) REFERENCES `utilisateur` (`id_utilisateur`) ON DELETE CASCADE;

--
-- Contraintes pour la table `enseignant_matiere`
--
ALTER TABLE `enseignant_matiere`
  ADD CONSTRAINT `enseignant_matiere_ibfk_1` FOREIGN KEY (`id_enseignant`) REFERENCES `enseignant` (`id_enseignant`) ON DELETE CASCADE,
  ADD CONSTRAINT `enseignant_matiere_ibfk_2` FOREIGN KEY (`id_matiere`) REFERENCES `matiere` (`id_matiere`) ON DELETE CASCADE;

--
-- Contraintes pour la table `examen`
--
ALTER TABLE `examen`
  ADD CONSTRAINT `examen_ibfk_1` FOREIGN KEY (`id_matiere`) REFERENCES `matiere` (`id_matiere`),
  ADD CONSTRAINT `examen_ibfk_2` FOREIGN KEY (`id_classe`) REFERENCES `classe` (`id_classe`),
  ADD CONSTRAINT `examen_ibfk_3` FOREIGN KEY (`id_enseignant`) REFERENCES `enseignant` (`id_enseignant`) ON DELETE SET NULL;

--
-- Contraintes pour la table `inscription`
--
ALTER TABLE `inscription`
  ADD CONSTRAINT `inscription_ibfk_1` FOREIGN KEY (`id_classe`) REFERENCES `classe` (`id_classe`) ON DELETE CASCADE,
  ADD CONSTRAINT `inscription_ibfk_2` FOREIGN KEY (`id_eleve`) REFERENCES `eleve` (`id_eleve`) ON DELETE CASCADE,
  ADD CONSTRAINT `inscription_ibfk_3` FOREIGN KEY (`id_annee_scolaire`) REFERENCES `annee_scolaire` (`id_annee_scolaire`) ON DELETE CASCADE;

--
-- Contraintes pour la table `message`
--
ALTER TABLE `message`
  ADD CONSTRAINT `message_ibfk_1` FOREIGN KEY (`id_emetteur`) REFERENCES `utilisateur` (`id_utilisateur`) ON DELETE CASCADE,
  ADD CONSTRAINT `message_ibfk_2` FOREIGN KEY (`id_recepteur`) REFERENCES `utilisateur` (`id_utilisateur`) ON DELETE CASCADE;

--
-- Contraintes pour la table `niveau_matiere`
--
ALTER TABLE `niveau_matiere`
  ADD CONSTRAINT `niveau_matiere_ibfk_1` FOREIGN KEY (`id_niveau`) REFERENCES `niveau` (`id_niveau`) ON DELETE CASCADE,
  ADD CONSTRAINT `niveau_matiere_ibfk_2` FOREIGN KEY (`id_matiere`) REFERENCES `matiere` (`id_matiere`) ON DELETE CASCADE;

--
-- Contraintes pour la table `note`
--
ALTER TABLE `note`
  ADD CONSTRAINT `note_ibfk_1` FOREIGN KEY (`id_eleve`) REFERENCES `eleve` (`id_eleve`) ON DELETE CASCADE,
  ADD CONSTRAINT `note_ibfk_2` FOREIGN KEY (`id_examen`) REFERENCES `examen` (`id_examen`) ON DELETE CASCADE;

--
-- Contraintes pour la table `paiement`
--
ALTER TABLE `paiement`
  ADD CONSTRAINT `paiement_ibfk_1` FOREIGN KEY (`id_eleve`) REFERENCES `eleve` (`id_eleve`) ON DELETE CASCADE,
  ADD CONSTRAINT `paiement_ibfk_2` FOREIGN KEY (`id_annee_scolaire`) REFERENCES `annee_scolaire` (`id_annee_scolaire`) ON DELETE CASCADE;

--
-- Contraintes pour la table `parent`
--
ALTER TABLE `parent`
  ADD CONSTRAINT `parent_ibfk_1` FOREIGN KEY (`id_utilisateur`) REFERENCES `utilisateur` (`id_utilisateur`) ON DELETE CASCADE;

--
-- Contraintes pour la table `participation_activite`
--
ALTER TABLE `participation_activite`
  ADD CONSTRAINT `participation_activite_ibfk_1` FOREIGN KEY (`id_eleve`) REFERENCES `eleve` (`id_eleve`) ON DELETE CASCADE,
  ADD CONSTRAINT `participation_activite_ibfk_2` FOREIGN KEY (`id_activite`) REFERENCES `activite` (`id_activite`) ON DELETE CASCADE;

--
-- Contraintes pour la table `relation_parent_eleve`
--
ALTER TABLE `relation_parent_eleve`
  ADD CONSTRAINT `relation_parent_eleve_ibfk_1` FOREIGN KEY (`id_parent`) REFERENCES `parent` (`id_parent`) ON DELETE CASCADE,
  ADD CONSTRAINT `relation_parent_eleve_ibfk_2` FOREIGN KEY (`id_eleve`) REFERENCES `eleve` (`id_eleve`) ON DELETE CASCADE;

--
-- Contraintes pour la table `unite`
--
ALTER TABLE `unite`
  ADD CONSTRAINT `unite_ibfk_1` FOREIGN KEY (`id_matiere`) REFERENCES `matiere` (`id_matiere`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
