import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import Card from "../../components/Card";
import Button from "../../components/Button";
import {Users, GraduationCap, BookOpen, AlertCircle, Activity} from "lucide-react";
import { getAnneeScolaireActive, getStatistiquesGenerales, getActivites, getEleves } from "../../services/api";
import type { Activite } from "../../types";

interface DashboardStats {
  total_eleves: number;
  total_enseignants: number;
  total_classes: number;
  total_activites: number;
  garcons: number;
  filles: number;
}

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [activites, setActivites] = useState<Activite[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>("");
  const [anneeActive, setAnneeActive] = useState("");

  const statCards = [
    { title: "Total Élèves", value: stats?.total_eleves, subtitle: "Élèves inscrits", icon: Users, color: "blue" },
    { title: "Enseignants", value: stats?.total_enseignants, subtitle: "Personnel enseignant", icon: GraduationCap, color: "green" },
    { title: "Classes", value: stats?.total_classes, subtitle: "Classes actives", icon: BookOpen, color: "purple" },
    { title: "Activités", value: stats?.total_activites, subtitle: "Activités créées", icon: Activity, color: "orange" },
  ];

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    setLoading(true);
    setError("");

    try {
      // Charger les statistiques générales depuis l'API
      const [anneeRes, statsRes, elevesRes, activitesRes] = await Promise.all([
        getAnneeScolaireActive(),
        getStatistiquesGenerales(),
        getEleves(),
        getActivites()
      ]);
      setAnneeActive(anneeRes.data?.data.libelle);
      const statsData = statsRes.data?.data || {};
      const eleves = elevesRes.data?.data || [];
      const activitesData = activitesRes.data?.data || [];

      // Calculer les statistiques de genre
      const garcons = eleves.filter(e => e.user?.sexe === 'garçon').length;
      const filles = eleves.filter(e => e.user?.sexe === 'fille').length;

     setStats({
      total_eleves: statsData.total_eleves || 0,
      total_enseignants: statsData.total_enseignants || 0,
      total_classes: statsData.total_classes || 0,
      total_activites: statsData.total_activites || 0,
      garcons,
      filles
    });

      setActivites(activitesData.slice(0, 5)); // Prendre les 5 premières activités

    } catch (error) {
      console.error("Erreur lors du chargement du dashboard:", error);
      setError("Erreur lors du chargement des données");
    } finally {
      setLoading(false);
    }
  };

  // Composant de carte de statistiques simple
  const StatCard = ({ title, value, subtitle, icon: Icon, color, loading }: {
    title: string;
    value: string | number;
    subtitle?: string;
    icon: any;
    color: 'blue' | 'green' | 'purple' | 'orange';
    loading?: boolean;
  }) => {
    const colorClasses = {
      blue: 'bg-blue-50 text-blue-600',
      green: 'bg-green-50 text-green-600',
      purple: 'bg-purple-50 text-purple-600',
      orange: 'bg-orange-50 text-orange-600'
    };

    if (loading) {
      return (
        <Card className="transition-transform duration-200 hover:scale-[1.01] focus-within:ring-2 ring-blue-300">
          <div className="animate-pulse">
            <div className="flex items-center justify-between">
              <div>
                <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-16 mb-1"></div>
                <div className="h-3 bg-gray-200 rounded w-20"></div>
              </div>
              <div className="p-3 rounded-lg bg-gray-100">
                <div className="w-6 h-6 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>
        </Card>
      );
    }

    return (
      <Card className="transition-transform duration-200 hover:scale-[1.01] focus-within:ring-2 ring-blue-300">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
            <p className="text-2xl font-bold text-gray-900 mb-1">
              {typeof value === 'number' ? value.toLocaleString() : value}
            </p>
            {subtitle && (
              <p className="text-sm text-gray-500">{subtitle}</p>
            )}
          </div>
          <div className={`p-3 rounded-lg ${colorClasses[color] || colorClasses.blue}`}>
            <Icon className="w-6 h-6" />
          </div>
        </div>
      </Card>
    );
  };

  if (error) {
    return (
      <div className="p-6">
        <Card>
          <div className="flex items-center justify-center py-8 text-red-600">
            <AlertCircle className="w-8 h-8 mr-2" />
            <span>{error}</span>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* <h1 className="sr-only">Tableau de bord - ScolaNova</h1> */}

      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <p className=" font-bold text-gray-900">Année scolaire: </p>
          <p className="text-gray-600">{anneeActive}</p>
        </div>
        <div className="flex items-center gap-4">
          <Button
            size="sm"
            variant="outline"
            onClick={() => navigate('/admin/school-settings')}
          >
            Paramètre d'école
          </Button>
          {/* <div className="text-sm text-gray-500">
            Dernière mise à jour: {new Date().toLocaleString('fr-FR')}
          </div> */}
        </div>
      </div>

      {/* Cartes de statistiques principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat, i) => (
          <StatCard
            key={i}
            title={stat.title}
            value={stat.value}
            subtitle={stat.subtitle}
            icon={stat.icon}
            color={stat.color}
            loading={loading}
          />
        ))}
      </div>
     
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Répartition par genre */}
        <Card className="transition-transform duration-200 hover:scale-[1.01] focus-within:ring-2 ring-blue-300">
          <h3 className="text-lg font-medium text-gray-900 flex items-center gap-2 mb-4" aria-label="Répartition par genre">
            <Users className="w-5 h-5 text-blue-600" />
            Répartition par genre
          </h3>
          {loading ? (
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
                  <span className="text-sm font-medium">Garçons</span>
                </div>
                <div className="text-lg font-bold text-blue-600">{stats?.garcons || 0}</div>
              </div>
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-pink-500 rounded-full"></div>
                  <span className="text-sm font-medium">Filles</span>
                </div>
                <div className="text-lg font-bold text-pink-600">{stats?.filles || 0}</div>
              </div>

          
              <div className="mt-4 bg-gray-200 rounded-full h-4 overflow-hidden flex">
                <div
                  className="h-full bg-blue-500 transition-all duration-500"
                  style={{
                    width: `${stats?.total_eleves ? (stats.garcons / stats.total_eleves) * 100 : 0}%`
                  }}
                />
                <div
                  className="h-full bg-pink-500 transition-all duration-500"
                  style={{
                    width: `${stats?.total_eleves ? (stats.filles / stats.total_eleves) * 100 : 0}%`
                  }}
                />
              </div>
              <div className="text-sm text-center text-gray-600">
                {stats?.total_eleves
                  ? `${((stats.garcons / stats.total_eleves) * 100).toFixed(1)}% garçons / ${(stats.filles / stats.total_eleves * 100).toFixed(1)}% filles`
                  : "0% / 0%"}
              </div>

              <div className="text-center text-sm text-gray-600">
                Total: {stats?.total_eleves || 0} élèves
              </div>
            </div>
          )}
        </Card>

        {/* Liste des activités récentes */}
        <Card className="transition-transform duration-200 hover:scale-[1.01] focus-within:ring-2 ring-blue-300">
          <div className="flex items-center gap-2 mb-4">
            <Activity className="w-5 h-5 text-green-600" />
            <h3 className="text-lg font-medium text-gray-900">Activités récentes</h3>
          </div>

          {loading ? (
            <div className="space-y-3">
              {[1, 2, 3].map((i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-16 bg-gray-200 rounded-lg"></div>
                </div>
              ))}
            </div>
          ) : activites.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Activity className="w-12 h-12 mx-auto mb-2 text-gray-300" />
              <p>Aucune activité disponible</p>
            </div>
          ) : (
            <div className="space-y-3">
              {activites.map((activite) => (
                <div key={activite.id_activite} className="p-3 border border-gray-200 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-gray-900">{activite.nom_activite}</h4>
                      <p className="text-sm text-gray-600 capitalize">{activite.type_activite}</p>
                    </div>
                    <div className="text-right">
                      {/* <div className="text-sm font-medium text-gray-900">{activite.prix} DH</div> */}
                      <div className="text-sm font-medium text-gray-900">
                        {new Intl.NumberFormat("fr-MA", {
                          style: "currency",
                          currency: "MAD",
                          minimumFractionDigits: 0,
                        }).format(activite.prix)}
                      </div>
                      <div className="text-xs text-gray-500">
                        {activite.nombre_participants || 0} participants
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
          )}
          {/* {activites.length > 0 && (
            <div className="text-right mt-4">
              <Button size="sm" variant="link" onClick={() => navigate("/admin/activites")}>
                Voir toutes les activités →
              </Button>
            </div>
          )} */}
     


        </Card>
      </div>

    </div>
  );
};

export default Dashboard;