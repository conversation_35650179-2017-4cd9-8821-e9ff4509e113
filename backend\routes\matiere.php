<?php

require_once __DIR__ . '/../controllers/MatiereController.php';

function handleMatiereRoutes($uri, $method, $pdo) {
    $controller = new MatiereController($pdo);

    // GET /matieres - Récupérer toutes les matières
    if ($uri === '/matieres' && $method === 'GET') {
        $controller->getAll();
        return true;
    }

    // GET /matieres/{id} - Récupérer une matière par ID
    if (preg_match('/^\/matieres\/(\d+)$/', $uri, $matches) && $method === 'GET') {
        $id = (int)$matches[1];
        $controller->getById($id);
        return true;
    }

    // POST /matieres - Créer une nouvelle matière
    if ($uri === '/matieres' && $method === 'POST') {
        $controller->create();
        return true;
    }

    // PUT /matieres/{id} - Mettre à jour une matière
    if (preg_match('/^\/matieres\/(\d+)$/', $uri, $matches) && $method === 'PUT') {
        $id = (int)$matches[1];
        $controller->update($id);
        return true;
    }

    // DELETE /matieres/{id} - Supprimer une matière
    if (preg_match('/^\/matieres\/(\d+)$/', $uri, $matches) && $method === 'DELETE') {
        $id = (int)$matches[1];
        $controller->delete($id);
        return true;
    }

    return false;
}
?>
