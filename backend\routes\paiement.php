<?php

require_once __DIR__ . '/../controllers/PaiementController.php';

function handlePaiementRoutes($path, $method, $pdo) {
    // Créer une instance du contrôleur
    $paiementController = new PaiementController($pdo);

    // Routes pour les paiements
    switch ($method) {
    case 'GET':
        if ($path === '/paiements') {
            // GET /paiements - Récupérer tous les paiements
            $paiementController->getPaiements();
        } elseif (preg_match('/^\/paiements\/(\d+)$/', $path, $matches)) {
            // GET /paiements/{id} - Récupérer un paiement par ID
            $paiementController->getPaiement($matches[1]);
        } elseif ($path === '/paiements/statistiques') {
            // GET /paiements/statistiques - Récupérer les statistiques
            $paiementController->getStatistiques();
        } elseif (preg_match('/^\/paiements\/eleve\/(\d+)$/', $path, $matches)) {
            // GET /paiements/eleve/{id} - Récupérer les paiements d'un élève
            $paiementController->getPaiementsEleve($matches[1]);
        } elseif ($path === '/paiements/verifier-mensuels') {
            // GET /paiements/verifier-mensuels - Vérifier les paiements mensuels
            $paiementController->verifierPaiementsMensuels();
        } else {
            return false; // Route non gérée
        }
        break;

    case 'POST':
        if ($path === '/paiements') {
            // POST /paiements - Créer un nouveau paiement
            $paiementController->addPaiement();
        } elseif ($path === '/paiements/generer-mensuels') {
            // POST /paiements/generer-mensuels - Générer les paiements mensuels
            $paiementController->genererPaiementsMensuels();
        } else {
            return false; // Route non gérée
        }
        break;

    case 'PUT':
        if (preg_match('/^\/paiements\/(\d+)$/', $path, $matches)) {
            // PUT /paiements/{id} - Mettre à jour un paiement
            $paiementController->updatePaiement($matches[1]);
        } else {
            return false; // Route non gérée
        }
        break;

    case 'DELETE':
        if (preg_match('/^\/paiements\/(\d+)$/', $path, $matches)) {
            // DELETE /paiements/{id} - Supprimer un paiement
            $paiementController->deletePaiement($matches[1]);
        } else {
            return false; // Route non gérée
        }
        break;

    default:
        return false; // Route non gérée
        break;
    }

    return true; // Route gérée
}
