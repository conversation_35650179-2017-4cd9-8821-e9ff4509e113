
import React, { forwardRef } from 'react';
import { ChevronDown } from 'lucide-react';

interface SelectOption {
  value: string;
  label: string;
}

interface SelectProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'onChange'> {
  label?: string;
  options?: SelectOption[];
  error?: string;
  className?: string;
  value?: string;
  required?: boolean;
  onChange?: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  placeholder?: string;
  children?: React.ReactNode;
}

const Select = forwardRef<HTMLSelectElement, SelectProps>(
  ({ label, options, error, className = '', value = '',required = false, onChange, placeholder, children, ...props }, ref) => {
    return (
      <div>
        {label && (
            <label htmlFor={props.id || props.name} className="text-sm text-gray-700 font-semibold mb-1" >
              {label}{required && <span className="text-red-500 font-bold"> *</span>}
            </label>
          )}
        
        <div className="relative">
          <select
            ref={ref}
            onChange={onChange}
            value={value}
            className={`
              appearance-none w-full rounded-lg border 
              ${error ? 'border-red-500' : 'border-gray-300'} 
              shadow-sm focus:outline-none focus:border-blue-600 
              pl-4 pr-10 py-2 text-gray-800
              ${className}
            `}
            {...props}
          >
            {placeholder && (
              <option value="" disabled>
                {placeholder}
              </option>
            )}
            {options ? (
              options.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))
            ) : (
              children
            )}
          </select>
          <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
            <ChevronDown size={16} />
          </div>
        </div>
        {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
      </div>
    );
  }
);

Select.displayName = 'Select';
export default Select;