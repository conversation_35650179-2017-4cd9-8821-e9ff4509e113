import React from 'react';
import { ChevronDown } from 'lucide-react';

interface StatusSelectProps {
  label?: string;
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

const StatusSelect: React.FC<StatusSelectProps> = ({
  label,
  value,
  onChange,
  placeholder = 'Sélectionner un statut',
  className = ''
}) => {
  return (
    <div className={className}>
      {label && (
        <label className="text-sm text-gray-700 font-semibold mb-1 block">
          {label}
        </label>
      )}

      <div className="relative">
        <select
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="appearance-none w-full rounded-lg border border-gray-300 shadow-sm focus:outline-none focus:border-blue-600 pl-4 pr-10 py-2 text-gray-800 bg-white"
        >
          <option value="">Tous les statuts</option>
          <option value="actif">Actif</option>
          <option value="terminé">Terminé</option>
          <option value="suspendu">Suspendu</option>
          <option value="sans_contrat">Sans contrat</option>
        </select>

        {/* Icône de dropdown à droite */}
        <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
          <ChevronDown size={16} />
        </div>
      </div>
    </div>
  );
};

export default StatusSelect;
