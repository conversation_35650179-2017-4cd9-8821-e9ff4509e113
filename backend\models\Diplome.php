<?php

class Diplome {
    private $pdo;
    private $table = "Diplome";

    public function __construct($db) {
        $this->pdo = $db;
    }

    public function getAll() {
        $query = "SELECT * FROM " . $this->table;
        $stmt = $this->pdo->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getById($id) {
        $query = "SELECT * FROM " . $this->table . " WHERE id_diplome = :id";
        $stmt = $this->pdo->prepare($query);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function getByEnseignantId($id_enseignant) {
        $query = "SELECT * FROM " . $this->table . " WHERE id_enseignant = :id_enseignant";
        $stmt = $this->pdo->prepare($query);
        $stmt->bindParam(':id_enseignant', $id_enseignant, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function create($data) {
        $query = "INSERT INTO " . $this->table . " (
                    id_enseignant, intitule, institut, specialite, date_promotion
                ) VALUES (
                    :id_enseignant, :intitule, :institut, :specialite, :date_promotion
                )";
        $stmt = $this->pdo->prepare($query);
        
        $stmt->bindParam(':id_enseignant', $data['id_enseignant'], PDO::PARAM_INT);
        $stmt->bindParam(':intitule', $data['intitule'], PDO::PARAM_STR);
        $stmt->bindParam(':institut', $data['institut'], PDO::PARAM_STR);
        $stmt->bindParam(':specialite', $data['specialite'], PDO::PARAM_STR);
        $stmt->bindParam(':date_promotion', $data['date_promotion'], PDO::PARAM_STR);

        if ($stmt->execute()) {
            return $this->pdo->lastInsertId();
        } else {
            error_log("Erreur SQL Diplome : " . implode(", ", $stmt->errorInfo()));
            return false;
        }
    }

    public function update($id, $data) {
        try {
            error_log("Mise à jour diplôme $id avec données : " . json_encode($data));

            $query = "UPDATE " . $this->table . " SET 
                     id_enseignant = :id_enseignant,
                     intitule = :intitule,
                     institut = :institut,
                     specialite = :specialite,
                     date_promotion = :date_promotion
                     WHERE id_diplome = :id";
            $stmt = $this->pdo->prepare($query);

            $stmt->bindParam(':id_enseignant', $data['id_enseignant'], PDO::PARAM_INT);
            $stmt->bindParam(':intitule', $data['intitule'], PDO::PARAM_STR);
            $stmt->bindParam(':institut', $data['institut'], PDO::PARAM_STR);
            $stmt->bindParam(':specialite', $data['specialite'], PDO::PARAM_STR);
            $stmt->bindParam(':date_promotion', $data['date_promotion'], PDO::PARAM_STR);
            $stmt->bindParam(':id_diplome', $id, PDO::PARAM_INT);

            if ($stmt->execute()) {
                $rowCount = $stmt->rowCount();
                error_log("Diplôme mis à jour avec succès, lignes affectées: $rowCount");
                return true; // Retourner true si la requête s'exécute, même si aucune ligne modifiée
            } else {
                $errorInfo = $stmt->errorInfo();
                error_log("Erreur SQL Update Diplome : " . implode(", ", $errorInfo));
                throw new Exception("Erreur SQL: " . $errorInfo[2]);
            }
        } catch (Exception $e) {
            error_log("Exception dans Diplome::update: " . $e->getMessage());
            throw $e;
        }
    }

    public function delete($id) {
        $query = "DELETE FROM " . $this->table . " WHERE id_diplome = :id";
        $stmt = $this->pdo->prepare($query);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        
        if ($stmt->execute()) {
            return $stmt->rowCount() > 0;
        } else {
            error_log("Erreur SQL Delete Diplome : " . implode(", ", $stmt->errorInfo()));
            return false;
        }
    }
}
