import React, { useState, useEffect } from "react";
import { DollarSign, Calendar } from "lucide-react";
import Button from "./Button";
import Input from "./Input";
import Select from "./Select";
import { addPaiement, updatePaiement, getEleves, getAnneesScolaires, getNiveauEleve } from "../services/api";
import type { Paiement, Eleve, AnneeScolaire } from "../types";

interface PaiementFormProps {
  paiement?: Paiement | null;
  onSubmit: () => void;
  onCancel: () => void;
}

const PaiementForm: React.FC<PaiementFormProps> = ({ paiement, onSubmit, onCancel }) => {
  const [formData, setFormData] = useState({
    id_eleve: "",
    montant: "",
    mois: "",
    date_paiement: "",
    type_paiement: "",
    mode_paiement: "",
    statut: "en attente",
    description: "",
  });

  const [eleves, setEleves] = useState<Eleve[]>([]);
  const [anneeActive, setAnneeActive] = useState<AnneeScolaire | null>(null);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);
  const [fieldErrors, setFieldErrors] = useState<{[key: string]: string}>({});

  const moisOptions = [
    "Janvier", "Février", "Mars", "Avril", "Mai", "Juin",
    "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"
  ];

  const typesPaiement = [
    { value: "scolarité", label: "Scolarité", useNiveauPrice: "prix_mensuel" },
    { value: "transport", label: "Transport", useNiveauPrice: null },
    { value: "inscription", label: "Inscription", useNiveauPrice: "frais_inscription" },
    { value: "activité", label: "Activité", useNiveauPrice: null },
    { value: "autre", label: "Autre", useNiveauPrice: null }
  ];

  const modesPaiement = [
    { value: "espèces", label: "Espèces" },
    { value: "chèque", label: "Chèque" },
    { value: "virement", label: "Virement" },
    { value: "carte", label: "Carte bancaire" }
  ];

  const statutsOptions = [
    { value: "en attente", label: "En attente" },
    { value: "payé", label: "Payé" },
    { value: "retard", label: "En retard" },
    { value: "annule", label: "Annulé" }
  ];

  useEffect(() => {
    fetchData();

    // Effacer les erreurs quand le formulaire est réinitialisé
    setFieldErrors({});
    setErrors([]);

    if (paiement) {
      setFormData({
        id_eleve: paiement.id_eleve.toString(),
        montant: paiement.montant.toString(),
        mois: paiement.mois,
        date_paiement: paiement.date_paiement.split('T')[0],
        type_paiement: paiement.type_paiement,
        mode_paiement: paiement.mode_paiement,
        statut: paiement.statut,
        description: paiement.description || "",
      });
    } else {
      // Valeurs par défaut pour un nouveau paiement
      const today = new Date();
      const todayString = today.toISOString().split('T')[0];
      const currentMonth = moisOptions[today.getMonth()]; // getMonth() retourne 0-11

      setFormData(prev => ({
        ...prev,
        date_paiement: todayString,
        mois: currentMonth,
      }));
    }
  }, [paiement]);

  const fetchData = async () => {
    try {
      // Charger les élèves
      const elevesRes = await getEleves();
      if (elevesRes.data.success) {
        setEleves(elevesRes.data.data);
      }

      // Charger l'année scolaire active
      try {
        const anneesRes = await getAnneesScolaires();
        if (anneesRes.data.success) {
          const annees = anneesRes.data.data;
          const anneeActive = annees.find((annee: any) => annee.est_active);

          if (anneeActive) {
            setAnneeActive(anneeActive);
          } else {
            // Si aucune année active, prendre la première
            setAnneeActive(annees[0] || null);
          }
        }
      } catch (error) {
        console.log("Années scolaires non disponibles, utilisation d'une année par défaut");
        // Créer une année scolaire par défaut
        const currentYear = new Date().getFullYear();
        const anneeParDefaut = {
          id_annee_scolaire: 1,
          libelle: `${currentYear}-${currentYear + 1}`,
          date_debut: `${currentYear}-09-01`,
          date_fin: `${currentYear + 1}-06-30`,
          est_active: true
        };
        setAnneeActive(anneeParDefaut);
      }
    } catch (error) {
      console.error("Erreur lors du chargement des données:", error);
    }
  };

  // Fonction pour récupérer le montant automatiquement
  const fetchMontantAutomatique = async (id_eleve: string, type_paiement: string) => {
    if (!id_eleve || !type_paiement) return;

    const typeConfig = typesPaiement.find(t => t.value === type_paiement);
    if (!typeConfig?.useNiveauPrice) return;

    try {
      const response = await getNiveauEleve(parseInt(id_eleve));
      if (response.data.success) {
        const niveau = response.data.data;
        const montant = niveau[typeConfig.useNiveauPrice];

        if (montant) {
          setFormData(prev => ({
            ...prev,
            montant: montant.toString()
          }));
        }
      }
    } catch (error) {
      console.error("Erreur lors de la récupération du montant automatique:", error);
    }
  };

  // Validation en temps réel pour un champ spécifique
  const validateField = (fieldName: string, value: string) => {
    let error = "";

    switch (fieldName) {
      case "id_eleve":
        if (!value) error = "L'élève est requis";
        break;

      case "montant":
        if (!value) {
          error = "Le montant est requis";
        } else {
          const montant = parseFloat(value);
          if (isNaN(montant)) {
            error = "Le montant doit être un nombre valide";
          } else if (montant <= 0) {
            error = "Le montant doit être supérieur à 0";
          } else if (montant > 100000) {
            error = "Le montant ne peut pas dépasser 100 000 MAD";
          }
        }
        break;

      case "mois":
        if (!value) error = "Le mois est requis";
        break;

      case "type_paiement":
        if (!value) error = "Le type de paiement est requis";
        break;

      case "mode_paiement":
        if (!value) error = "Le mode de paiement est requis";
        break;

      case "date_paiement":
        if (!value) {
          error = "La date de paiement est requise";
        } else {
          const datePaiement = new Date(value);
          const today = new Date();
          const oneYearAgo = new Date();
          oneYearAgo.setFullYear(today.getFullYear() - 1);

          if (datePaiement > today) {
            error = "La date ne peut pas être dans le futur";
          } else if (datePaiement < oneYearAgo) {
            error = "La date ne peut pas être antérieure à un an";
          }
        }
        break;

      case "description":
        if (formData.type_paiement === "autre" && !value.trim()) {
          error = "La description est requise pour le type 'Autre'";
        } else if (value && value.length > 500) {
          error = "La description ne peut pas dépasser 500 caractères";
        }
        break;
    }

    setFieldErrors(prev => ({
      ...prev,
      [fieldName]: error
    }));

    return error === "";
  };

  const validateForm = () => {
    const newErrors: string[] = [];
    const newFieldErrors: {[key: string]: string} = {};

    // Validation de l'élève
    if (!formData.id_eleve) {
      newFieldErrors.id_eleve = "L'élève est requis";
      newErrors.push("L'élève est requis");
    }

    // Validation de l'année scolaire
    if (!anneeActive) {
      newErrors.push("Aucune année scolaire active trouvée");
    }

    // Validation du montant
    if (!formData.montant) {
      newFieldErrors.montant = "Le montant est requis";
      newErrors.push("Le montant est requis");
    } else {
      const montant = parseFloat(formData.montant);
      if (isNaN(montant)) {
        newFieldErrors.montant = "Le montant doit être un nombre valide";
        newErrors.push("Le montant doit être un nombre valide");
      } else if (montant <= 0) {
        newFieldErrors.montant = "Le montant doit être supérieur à 0";
        newErrors.push("Le montant doit être supérieur à 0");
      } else if (montant > 100000) {
        newFieldErrors.montant = "Le montant ne peut pas dépasser 100 000 MAD";
        newErrors.push("Le montant ne peut pas dépasser 100 000 MAD");
      }
    }

    // Validation du mois
    if (!formData.mois) {
      newFieldErrors.mois = "Le mois est requis";
      newErrors.push("Le mois est requis");
    }

    // Validation du type de paiement
    if (!formData.type_paiement) {
      newFieldErrors.type_paiement = "Le type de paiement est requis";
      newErrors.push("Le type de paiement est requis");
    }

    // Validation du mode de paiement
    if (!formData.mode_paiement) {
      newFieldErrors.mode_paiement = "Le mode de paiement est requis";
      newErrors.push("Le mode de paiement est requis");
    }

    // Validation de la date de paiement
    if (!formData.date_paiement) {
      newFieldErrors.date_paiement = "La date de paiement est requise";
      newErrors.push("La date de paiement est requise");
    } else {
      const datePaiement = new Date(formData.date_paiement);
      const today = new Date();
      const oneYearAgo = new Date();
      oneYearAgo.setFullYear(today.getFullYear() - 1);

      if (datePaiement > today) {
        newFieldErrors.date_paiement = "La date ne peut pas être dans le futur";
        newErrors.push("La date de paiement ne peut pas être dans le futur");
      } else if (datePaiement < oneYearAgo) {
        newFieldErrors.date_paiement = "La date ne peut pas être antérieure à un an";
        newErrors.push("La date de paiement ne peut pas être antérieure à un an");
      }
    }

    // Validation de la description pour certains types
    if (formData.type_paiement === "autre" && !formData.description.trim()) {
      newFieldErrors.description = "La description est requise pour le type 'Autre'";
      newErrors.push("La description est requise pour le type 'Autre'");
    }

    // Validation de la description (longueur)
    if (formData.description && formData.description.length > 500) {
      newFieldErrors.description = "La description ne peut pas dépasser 500 caractères";
      newErrors.push("La description ne peut pas dépasser 500 caractères");
    }

    setErrors(newErrors);
    setFieldErrors(newFieldErrors);
    return newErrors.length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setLoading(true);
    setErrors([]);

    try {
      const paiementData = {
        id_eleve: parseInt(formData.id_eleve),
        id_annee_scolaire: anneeActive!.id_annee_scolaire,
        montant: parseFloat(formData.montant),
        mois: formData.mois,
        date_paiement: formData.date_paiement,
        type_paiement: formData.type_paiement,
        mode_paiement: formData.mode_paiement,
        statut: formData.statut,
        description: formData.description || null,
      };

      let response;
      if (paiement) {
        response = await updatePaiement(paiement.id_paiement, paiementData);
      } else {
        response = await addPaiement(paiementData);
      }

      if (response.data.success) {
        onSubmit();
      } else {
        setErrors([response.data.message || "Erreur lors de la sauvegarde"]);
      }
    } catch (error: any) {
      console.error("Erreur lors de la sauvegarde:", error);
      if (error.response?.data?.message) {
        setErrors([error.response.data.message]);
      } else {
        setErrors(["Erreur lors de la sauvegarde du paiement"]);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Validation en temps réel
    validateField(field, value);

    // Récupérer automatiquement le montant quand on change l'élève ou le type de paiement
    if (field === 'id_eleve' || field === 'type_paiement') {
      const newEleveId = field === 'id_eleve' ? value : formData.id_eleve;
      const newTypePaiement = field === 'type_paiement' ? value : formData.type_paiement;

      if (newEleveId && newTypePaiement) {
        fetchMontantAutomatique(newEleveId, newTypePaiement);
      }
    }

    // Effacer les erreurs générales quand l'utilisateur modifie le formulaire
    if (errors.length > 0) {
      setErrors([]);
    }
  };



  return (
    <form onSubmit={handleSubmit} className="space-y-4 pt-6 border-t border-gray-200">
      {/* Messages d'erreur */}
      {/* {errors.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Erreurs de validation
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <ul className="list-disc pl-5 space-y-1">
                  {errors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )} */}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Select
          name="id_eleve"
          label="Élève"
          value={formData.id_eleve}
          placeholder="Sélectionner un élève"
          onChange={(e) => handleChange('id_eleve', e.target.value)}
          required
          error={fieldErrors.id_eleve}
          options={[
            ...eleves.map((eleve) => ({
              value: eleve.id_eleve.toString(),
              label: `${eleve.user?.prenom} ${eleve.user?.nom} - ${eleve.code_massar}`
            }))
          ]}
        />



        <Select
          name="type_paiement"
          label="Type de paiement"
          value={formData.type_paiement}
          placeholder="Sélectionner un type"
          onChange={(e) => handleChange('type_paiement', e.target.value)}
          required
          error={fieldErrors.type_paiement}
          options={[
            ...typesPaiement.map((type) => ({
              value: type.value,
              label: type.label
            }))
          ]}
        />

        <Select
          name="mois"
          label="Mois"
          value={formData.mois}
          placeholder="Sélectionner un mois"
          onChange={(e) => handleChange('mois', e.target.value)}
          required
          error={fieldErrors.mois}
          options={[
            ...moisOptions.map((mois) => ({
              value: mois,
              label: mois
            }))
          ]}
        />

        <div>
          <Input
            name="montant"
            label="Montant (MAD)"
            type="number"
            value={formData.montant}
            onChange={(e) => handleChange('montant', e.target.value)}
            required
            placeholder="0.00"
            error={fieldErrors.montant}
          />
          {formData.type_paiement && (formData.type_paiement === 'scolarité' || formData.type_paiement === 'inscription') && (
            <p className="text-xs text-blue-600 mt-1">(Montant automatique selon le niveau)</p>
          )}
        </div>

        <Select
          name="mode_paiement"
          label="Mode de paiement"
          value={formData.mode_paiement}
          placeholder="Sélectionner un mode"
          onChange={(e) => handleChange('mode_paiement', e.target.value)}
          required
          error={fieldErrors.mode_paiement}
          options={[
            ...modesPaiement.map((mode) => ({
              value: mode.value,
              label: mode.label
            }))
          ]}
        />

        <Input
          name="date_paiement"
          label="Date de paiement"
          type="date"
          value={formData.date_paiement}
          onChange={(e) => handleChange('date_paiement', e.target.value)}
          required
          max={new Date().toISOString().split('T')[0]}
          min={new Date(new Date().setFullYear(new Date().getFullYear() - 1)).toISOString().split('T')[0]}
          error={fieldErrors.date_paiement}
        />



        <Select
          name="statut"
          label="Statut"
          value={formData.statut}
          onChange={(e) => handleChange('statut', e.target.value)}
          required
          error={fieldErrors.statut}
          options={statutsOptions.map((statut) => ({
            value: statut.value,
            label: statut.label
          }))}
        />

        {/* Description */}
        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Description
            {formData.type_paiement === "autre" && <span className="text-red-500">*</span>}
            <span className="text-xs text-gray-500 ml-2">
              ({formData.description.length}/500 caractères)
            </span>
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => handleChange('description', e.target.value)}
            rows={3}
            maxLength={500}
            className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 sm:text-sm ${
              fieldErrors.description
                ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                : 'border-gray-300 focus:border-blue-600 focus:ring-blue-600'
            }`}
            placeholder={formData.type_paiement === "autre" ? "Description requise pour le type 'Autre'..." : "Description optionnelle du paiement..."}
          />
          {fieldErrors.description && (
            <p className="mt-1 text-sm text-red-600">
              {fieldErrors.description}
            </p>
          )}
        </div>
      </div>

      {/* Boutons */}
      <div className="flex justify-end space-x-3 ">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={loading}
        >
          Annuler
        </Button>
        <Button
          type="submit"
          variant="primary"
          disabled={loading}
        >
          {loading ? (
            <>
              <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
              {paiement ? "Modification..." : "Création..."}
            </>
          ) : (
            paiement ? "Modifier le paiement" : "Créer le paiement"
          )}
        </Button>
      </div>
    </form>
  );
};

export default PaiementForm;
