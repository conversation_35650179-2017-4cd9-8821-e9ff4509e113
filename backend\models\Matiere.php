<?php

class Matiere
{
    private $pdo;
    private $table = "Matiere";

    public function __construct($db)
    {
        $this->pdo = $db;
    }

    public function getAll()
    {
        try {
            $query = "SELECT * FROM " . $this->table . " ORDER BY nom_matiere_fr";
            $stmt = $this->pdo->prepare($query);
            $stmt->execute();
            $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return array_map(function ($row) {
                return [
                    'id_matiere' => (int) $row['id_matiere'],
                    'nom_matiere' => $row['nom_matiere_fr'], // Utiliser le nom français par défaut
                    'nom_matiere_fr' => $row['nom_matiere_fr'],
                    'nom_matiere_ar' => $row['nom_matiere_ar'],
                    'description' => $row['description']
                ];
            }, $rows);
        } catch (PDOException $e) {
            error_log("Erreur dans Matiere::getAll: " . $e->getMessage());
            return [];
        }
    }

    public function getById($id)
    {
        try {
            $stmt = $this->pdo->prepare("SELECT * FROM " . $this->table . " WHERE id_matiere = :id");
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            $stmt->execute();
            $row = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$row) {
                return null;
            }

            return [
                'id_matiere' => (int) $row['id_matiere'],
                'nom_matiere' => $row['nom_matiere_fr'],
                'nom_matiere_fr' => $row['nom_matiere_fr'],
                'nom_matiere_ar' => $row['nom_matiere_ar'],
                'description' => $row['description']
            ];
        } catch (PDOException $e) {
            error_log("Erreur dans Matiere::getById: " . $e->getMessage());
            return null;
        }
    }

    public function create($data)
    {
        try {
            $query = "INSERT INTO " . $this->table . " (nom_matiere_fr, nom_matiere_ar, description) 
                        VALUES (:nom_matiere_fr, :nom_matiere_ar, :description)";
            $stmt = $this->pdo->prepare($query);

            // Préparer les variables pour bindParam (nécessaire pour les valeurs par référence)
            $nom_matiere_fr = $data['nom_matiere_fr'];
            $nom_matiere_ar = $data['nom_matiere_ar'];
            $description = $data['description'] ?? null;

            $stmt->bindParam(':nom_matiere_fr', $nom_matiere_fr, PDO::PARAM_STR);
            $stmt->bindParam(':nom_matiere_ar', $nom_matiere_ar, PDO::PARAM_STR);
            $stmt->bindParam(':description', $description, PDO::PARAM_STR);

            if ($stmt->execute()) {
                return $this->pdo->lastInsertId();
            } else {
                error_log("Erreur SQL Matiere : " . implode(", ", $stmt->errorInfo()));
                return false;
            }
        } catch (PDOException $e) {
            error_log("Erreur PDO dans Matiere::create : " . $e->getMessage());
            return false;
        }
    }

    public function update($id, $data)
    {
        try {
            $query = "UPDATE " . $this->table . " SET 
                     nom_matiere_fr = :nom_matiere_fr,
                     nom_matiere_ar = :nom_matiere_ar,
                     description = :description
                     WHERE id_matiere = :id";
            $stmt = $this->pdo->prepare($query);

            // Préparer les variables pour bindParam (nécessaire pour les valeurs par référence)
            $nom_matiere_fr = $data['nom_matiere_fr'];
            $nom_matiere_ar = $data['nom_matiere_ar'];
            $description = $data['description'] ?? null;

            $stmt->bindParam(':nom_matiere_fr', $nom_matiere_fr, PDO::PARAM_STR);
            $stmt->bindParam(':nom_matiere_ar', $nom_matiere_ar, PDO::PARAM_STR);
            $stmt->bindParam(':description', $description, PDO::PARAM_STR);
            $stmt->bindParam(':id_matiere', $id, PDO::PARAM_INT);

            if ($stmt->execute()) {
                return $stmt->rowCount() >= 0;
            } else {
                error_log("Erreur SQL Update Matiere : " . implode(", ", $stmt->errorInfo()));
                return false;
            }
        } catch (PDOException $e) {
            error_log("Erreur PDO dans Matiere::update : " . $e->getMessage());
            return false;
        }
    }

    public function delete($id)
    {
        try {
            $stmt = $this->pdo->prepare("DELETE FROM " . $this->table . " WHERE id_matiere = :id");
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            
            if ($stmt->execute()) {
                return $stmt->rowCount() > 0;
            } else {
                error_log("Erreur SQL Delete Matiere : " . implode(", ", $stmt->errorInfo()));
                return false;
            }
        } catch (PDOException $e) {
            error_log("Erreur PDO dans Matiere::delete : " . $e->getMessage());
            return false;
        }
    }
}
