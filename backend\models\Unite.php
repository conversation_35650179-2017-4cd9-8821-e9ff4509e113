<?php

class Unite {
    private $pdo;
    private $table = 'Unite';

    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    /**
     * <PERSON><PERSON><PERSON><PERSON><PERSON> toutes les unités avec leurs matières
     */
    public function getAll() {
        try {
            $query = "SELECT 
                u.id_unite,
                u.id_matiere,
                u.nom_unite,
                u.description,
                m.nom_matiere_fr,
                m.nom_matiere_ar
            FROM " . $this->table . " u
            JOIN Matiere m ON u.id_matiere = m.id_matiere
            ORDER BY m.nom_matiere_fr, u.nom_unite";
            
            $stmt = $this->pdo->prepare($query);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur lors de la récupération des unités: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Récupérer une unité par ID
     */
    public function getById($id) {
        try {
            $query = "SELECT 
                u.id_unite,
                u.id_matiere,
                u.nom_unite,
                u.description,
                m.nom_matiere_fr,
                m.nom_matiere_ar
            FROM " . $this->table . " u
            JOIN Matiere m ON u.id_matiere = m.id_matiere
            WHERE u.id_unite = :id";
            
            $stmt = $this->pdo->prepare($query);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            $stmt->execute();
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur lors de la récupération de l'unité: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Créer une nouvelle unité
     */
    public function create($data) {
        try {
            $query = "INSERT INTO " . $this->table . " 
                     (id_matiere, nom_unite, description) 
                     VALUES (:id_matiere, :nom_unite, :description)";
            
            $stmt = $this->pdo->prepare($query);
            $stmt->bindParam(':id_matiere', $data['id_matiere'], PDO::PARAM_INT);
            $stmt->bindParam(':nom_unite', $data['nom_unite'], PDO::PARAM_STR);
            $stmt->bindParam(':description', $data['description'], PDO::PARAM_STR);
            
            if ($stmt->execute()) {
                return $this->pdo->lastInsertId();
            } else {
                return false;
            }
        } catch (PDOException $e) {
            error_log("Erreur lors de la création de l'unité: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Mettre à jour une unité
     */
    public function update($id, $data) {
        try {
            $query = "UPDATE " . $this->table . " SET 
                     id_matiere = :id_matiere,
                     nom_unite = :nom_unite,
                     description = :description
                     WHERE id_unite = :id";
            
            $stmt = $this->pdo->prepare($query);
            $stmt->bindParam(':id_matiere', $data['id_matiere'], PDO::PARAM_INT);
            $stmt->bindParam(':nom_unite', $data['nom_unite'], PDO::PARAM_STR);
            $stmt->bindParam(':description', $data['description'], PDO::PARAM_STR);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Erreur lors de la mise à jour de l'unité: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Supprimer une unité
     */
    public function delete($id) {
        try {
            $query = "DELETE FROM " . $this->table . " WHERE id_unite = :id";
            $stmt = $this->pdo->prepare($query);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Erreur lors de la suppression de l'unité: " . $e->getMessage());
            throw $e;
        }
    }
}

?>
