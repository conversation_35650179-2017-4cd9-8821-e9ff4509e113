import React, { useState, useEffect } from 'react';
import { X, Download, User, Calendar, MapPin, Award } from 'lucide-react';
import Button from './Button';
import { getBulletinData, downloadBulletinPDF } from '../services/api';
import { BulletinData } from '../types';

interface BulletinModalProps {
  idEleve: number;
  semestre: string;
  onClose: () => void;
}

const BulletinModal: React.FC<BulletinModalProps> = ({ idEleve, semestre, onClose }) => {
  const [bulletinData, setBulletinData] = useState<BulletinData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchBulletinData();
  }, [idEleve, semestre]);

  const fetchBulletinData = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await getBulletinData(idEleve, semestre);
      setBulletinData(response.data.data);
    } catch (error: any) {
      console.error('Erreur lors du chargement du bulletin:', error);
      setError('Erreur lors du chargement du bulletin');
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadPDF = async () => {
    if (!bulletinData) return;

    try {
      const response = await downloadBulletinPDF(idEleve, semestre);
      
      // Créer un lien de téléchargement
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `Bulletin_${bulletinData.eleve.nom}_${bulletinData.eleve.prenom}_${semestre}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (error: any) {
      console.error('Erreur lors du téléchargement:', error);
      setError('Erreur lors du téléchargement du bulletin');
    }
  };

  const getAppreciation = (note: number) => {
    if (note >= 18) return { text: 'Excellent', color: 'text-green-600' };
    if (note >= 16) return { text: 'Très bien', color: 'text-blue-600' };
    if (note >= 14) return { text: 'Bien', color: 'text-indigo-600' };
    if (note >= 12) return { text: 'Assez bien', color: 'text-yellow-600' };
    if (note >= 10) return { text: 'Passable', color: 'text-orange-600' };
    if (note >= 8) return { text: 'Insuffisant', color: 'text-red-500' };
    return { text: 'Très insuffisant', color: 'text-red-600' };
  };

  const getMention = (moyenne: number) => {
    if (moyenne >= 18) return { text: 'EXCELLENT', color: 'text-green-600' };
    if (moyenne >= 16) return { text: 'TRÈS BIEN', color: 'text-blue-600' };
    if (moyenne >= 14) return { text: 'BIEN', color: 'text-indigo-600' };
    if (moyenne >= 12) return { text: 'ASSEZ BIEN', color: 'text-yellow-600' };
    if (moyenne >= 10) return { text: 'PASSABLE', color: 'text-orange-600' };
    return { text: 'INSUFFISANT', color: 'text-red-600' };
  };

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
        <div className="bg-white rounded-lg shadow-xl p-8">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-3">Chargement du bulletin...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error || !bulletinData) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
        <div className="bg-white rounded-lg shadow-xl p-8 max-w-md w-full">
          <div className="text-center">
            <div className="text-red-600 mb-4">
              <X className="mx-auto h-12 w-12" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Erreur</h3>
            <p className="text-gray-600 mb-4">{error || 'Bulletin non trouvé'}</p>
            <Button variant="outline" onClick={onClose}>
              Fermer
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const mention = getMention(bulletinData.moyennes.moyenne_generale);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        {/* En-tête */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              Bulletin de Notes - {semestre}
            </h2>
            <p className="text-gray-600">
              {bulletinData.eleve.prenom} {bulletinData.eleve.nom} - {bulletinData.eleve.classe}
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Button
              variant="primary"
              onClick={handleDownloadPDF}
              icon={<Download size={16} />}
            >
              Télécharger PDF
            </Button>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X size={24} />
            </button>
          </div>
        </div>

        {/* Contenu */}
        <div className="flex-1 overflow-y-auto p-6">
          {/* Informations élève */}
          <div className="bg-blue-50 rounded-lg p-4 mb-6">
            <h3 className="text-lg font-medium text-blue-900 mb-3">Informations de l'élève</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center">
                <User className="w-4 h-4 text-blue-600 mr-2" />
                <span className="text-sm text-gray-600">Nom complet :</span>
                <span className="ml-2 font-medium">{bulletinData.eleve.prenom} {bulletinData.eleve.nom}</span>
              </div>
              <div className="flex items-center">
                <span className="text-sm text-gray-600">Code Massar :</span>
                <span className="ml-2 font-medium">{bulletinData.eleve.code_massar}</span>
              </div>
              <div className="flex items-center">
                <Calendar className="w-4 h-4 text-blue-600 mr-2" />
                <span className="text-sm text-gray-600">Date de naissance :</span>
                <span className="ml-2 font-medium">
                  {new Date(bulletinData.eleve.date_naissance).toLocaleDateString('fr-FR')}
                </span>
              </div>
              <div className="flex items-center">
                <MapPin className="w-4 h-4 text-blue-600 mr-2" />
                <span className="text-sm text-gray-600">Lieu de naissance :</span>
                <span className="ml-2 font-medium">{bulletinData.eleve.lieu_naissance}</span>
              </div>
            </div>
          </div>

          {/* Notes par matière */}
          <div className="bg-white border border-gray-200 rounded-lg overflow-hidden mb-6">
            <div className="px-4 py-3 bg-gray-50 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Notes par matière</h3>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Matière
                    </th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Contrôle
                    </th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Devoir
                    </th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Examen
                    </th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Moyenne
                    </th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Appréciation
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {Object.entries(bulletinData.notes).map(([matiere, notes]) => {
                    const appreciation = getAppreciation(notes.moyenne);
                    return (
                      <tr key={matiere} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {matiere}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-900">
                          {notes.contrôle?.toFixed(2) || '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-900">
                          {notes.devoir?.toFixed(2) || '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-900">
                          {notes.examen?.toFixed(2) || '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-center text-sm font-medium text-gray-900">
                          {notes.moyenne.toFixed(2)}
                        </td>
                        <td className={`px-6 py-4 whitespace-nowrap text-center text-sm font-medium ${appreciation.color}`}>
                          {appreciation.text}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>

          {/* Moyenne générale et mention */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6">
            <div className="text-center">
              <div className="flex items-center justify-center mb-4">
                <Award className="w-8 h-8 text-blue-600 mr-3" />
                <h3 className="text-xl font-bold text-gray-900">Résultats</h3>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="text-center">
                  <p className="text-sm text-gray-600 mb-1">Moyenne Générale</p>
                  <p className="text-3xl font-bold text-blue-600">
                    {bulletinData.moyennes.moyenne_generale.toFixed(2)}/20
                  </p>
                </div>
                
                <div className="text-center">
                  <p className="text-sm text-gray-600 mb-1">Mention</p>
                  <p className={`text-2xl font-bold ${mention.color}`}>
                    {mention.text}
                  </p>
                </div>
              </div>

              <div className="mt-4 text-sm text-gray-600">
                Année scolaire : {bulletinData.annee_scolaire.nom_annee}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BulletinModal;
