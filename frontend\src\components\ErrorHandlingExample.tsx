import React, { useState } from 'react';
import { useFormErrorHandler } from '../hooks/useErrorHandler';
import { extractErrorMessage } from '../utils/errorUtils';
import ErrorDisplay from './ErrorDisplay';
import Input from './Input';
import Button from './Button';

/**
 * Exemple d'utilisation du nouveau système de gestion d'erreur
 */
const ErrorHandlingExample: React.FC = () => {
  const [formData, setFormData] = useState({ name: '', email: '' });
  const { error, validationErrors, clearError, clearFieldError, executeWithErrorHandling } = useFormErrorHandler();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Effacer l'erreur du champ quand l'utilisateur tape
    if (validationErrors[name]) {
      clearFieldError(name);
    }
    if (error) {
      clearError();
    }
  };

  const simulateApiCall = async (data: any) => {
    // Simuler différents types d'erreurs
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    if (!data.name) {
      throw {
        response: {
          status: 422,
          data: {
            message: 'Données invalides',
            validation_errors: {
              name: 'Le nom est requis'
            }
          }
        }
      };
    }
    
    if (data.email === '<EMAIL>') {
      throw {
        response: {
          status: 500,
          data: {
            message: 'Erreur interne du serveur'
          }
        }
      };
    }
    
    if (data.email === '<EMAIL>') {
      throw {
        message: 'Network Error'
      };
    }
    
    return { success: true, message: 'Données sauvegardées avec succès' };
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const result = await executeWithErrorHandling(
      () => simulateApiCall(formData),
      'sauvegarde des données',
      (result) => {
        console.log('Succès:', result);
        alert('Données sauvegardées avec succès !');
      }
    );
  };

  return (
    <div className="max-w-md mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-xl font-bold mb-4">Exemple de gestion d'erreur</h2>
      
      {/* Affichage des erreurs globales */}
      <ErrorDisplay 
        error={error} 
        onClose={clearError}
        className="mb-4"
      />
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <Input
          name="name"
          label="Nom"
          value={formData.name}
          onChange={handleInputChange}
          error={validationErrors.name}
          placeholder="Laissez vide pour tester l'erreur de validation"
        />
        
        <Input
          name="email"
          label="Email"
          type="email"
          value={formData.email}
          onChange={handleInputChange}
          error={validationErrors.email}
          placeholder="<EMAIL> pour erreur 500, <EMAIL> pour erreur réseau"
        />
        
        <Button type="submit" variant="primary" className="w-full">
          Tester la gestion d'erreur
        </Button>
      </form>
      
      <div className="mt-6 text-sm text-gray-600">
        <h3 className="font-semibold mb-2">Tests disponibles :</h3>
        <ul className="list-disc list-inside space-y-1">
          <li>Laissez le nom vide → Erreur de validation</li>
          <li>Email "<EMAIL>" → Erreur 500</li>
          <li>Email "<EMAIL>" → Erreur réseau</li>
          <li>Autres valeurs → Succès</li>
        </ul>
      </div>
    </div>
  );
};

export default ErrorHandlingExample;
